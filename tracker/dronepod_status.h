#ifndef DRONEPOD_STATUS_H
#define DRONEPOD_STATUS_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include "dronepod_k40t_protocol.h"

typedef struct {
   
    Payload_Camera_System_Status_0x000003_t camera_system_status;
    Payload_IR_Camera_Status_0x000004_t ir_camera_status;
    Payload_VL_Camera_Status_0x000005_t visible_camera_status;
    Payload_Gimbal_Status_0x000001_t gimbal_status;
    
    //目标经度
    int obj_lontitude;
    //目标纬度
    int obj_latitude;
    //目标海拔
    int obj_altitude;
    //目标距离
    int obj_distance;
    //飞行器经度
    int plane_lontitude;
    //飞行器纬度
    int plane_latitude;
    //飞行器海拔
    int plane_altitude;
    //跟踪状态
    int track_state;
    //变倍倍率
    int zoom_factor;
    //俯仰角度
    int pitch_angle;
    //偏航角度
    int yaw_angle;
    
    //OSD开关
    int watermark_switch;

    //测温开关
    int temp_switch;

    //测温类型
    int temp_measure_type;

    //红外机芯类型
    int ir_core_type;

    //伪彩
    int color_type;

    //测距开关
    int laser_switch;

    //俯仰波轮速度
    int pitch_dial_speed;

    //方位波轮速度
    int yaw_dial_speed;
} dronepod_status_t;

dronepod_status_t * get_dronepod_status();




void set_watermark_switch(int watermark_switch);



#ifdef __cplusplus
}
#endif

#endif