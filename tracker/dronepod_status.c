#include "dronepod_status.h"


static dronepod_status_t dronepod_status = {
        .camera_system_status = {
            .photo_video_mode = 0,        // 0:拍照, 1:录像
            .network_resolution = 0,      // 0:1080P30fps, 1:720P30fps
            .video_encoding = 1,          // 0:H264, 1:H265
            .streaming_mode = 0,          // 0x00:红外, 0x05/0x06:可见光, 0x07:分屏
            .video_output_bitrate = 4,    // 1:1M, 2:1.5M, 3:2M, 4:4M, 5:8M, 6:12M
            .photo_mode = 0,              // 拍照模式 (0:单拍, 1:连拍3/5张, 2:延时拍)
            .timed_photo_interval = 5,    // 延时拍照间隔延时拍照时间 (5/7/30/60 秒)
            .burst_count = 3,             // 连拍张数 (3/5)
            .sd_card_status = 0,          // SD卡状态 (0:正常, 1:异常, 2:慢速, 3:未插入, 4:已满, 5:格式错误)
            .sd_total_capacity = 1024,    // SD卡总容量, 单位: MB*10 (10240MB = 10GB)
            .sd_remaining_capacity = 512, // SD卡剩余容量, 单位: MB*10 (5120MB = 5GB)
            .sd_used_capacity = 512,      // SD卡已用容量, 单位: MB*10 (5120MB = 5GB)
            .reserved = {0}               // 预留
        },
        .ir_camera_status = {
            .area_max_temp = 0,         // 区域最高温度值
            .area_max_temp_x = 0,       // 区域最高温坐标
            .area_max_temp_y = 0,       // 区域最高温坐标
            .area_min_temp = 0,         // 区域最低温度值
            .area_min_temp_x = 0,        // 区域最低温坐标
            .area_min_temp_y = 0,        // 区域最低温坐标
            .area_center_temp = 0,     // 区域中心温度值
            .area_center_temp_x = 0,     // 区域中心温坐标
            .area_center_temp_y = 0,     // 区域中心温坐标
            .spot_temp = 0,            // 点测温温度值
            .spot_temp_x = 0,            // 点测温坐标X
            .spot_temp_y = 0,            // 点测温坐标Y
            .area_avg_temp = 0,        // 区域平均温度值
            .high_temp_alert = 0,         // 高温预警标志 (0:未, 1:开始)
            .low_temp_alert = 0,          // 低温预警标志 (0:未, 1:开始)
            .temp_diff_alert = 0,         // 温度差预警标志 (0:未, 1:开始)
            .threshold_temp_alert = 0,    // 阈值温度预警标志 (0:未, 1:开始)
            .reserved = {0}               // 预留
        },
        .visible_camera_status = {
            .zoom_status = 0,               // 变倍状态 (0x00:完成, 0x01:正在变倍)
            .focal_length = 800,            // 焦距, 单位: 0.01mm
            .hybrid_zoom_ratio = 10,        // 混合变倍倍率, 单位 0.1倍
            .ev_value = 0,                  // 曝光值
            .iso_value = 0,                 // ISO值
            .shutter_speed = 0,             // 快门速度, 单位 0.01秒
            .ae_lock_status = 0,            // AE锁定状态 (0x01:开, 0x02:关)
            .focus_status = 0,              // 对焦状态 (0x00:完成, 0x01:正在对焦)
            .precise_refocus_focal_length = 0, // 精准复拍焦距, 单位 0.01mm
            .reserved = 0                   // 预留
        },
        .gimbal_status = {
            .gimbal_camera_status = 0, // 云台相机状态 (0:正常, 1:异常)
            .upgrade_status = 0,
            .self_check_result = 0,
            .stabilization_status = 0,
            .reserved1 = {0}, // 预留
            .reserved2 = {0}  // 预留
        }
};
 


dronepod_status_t * get_dronepod_status(){
    return &dronepod_status;
}




void set_watermark_switch(int watermark_switch){
    dronepod_status.watermark_switch = watermark_switch;
}