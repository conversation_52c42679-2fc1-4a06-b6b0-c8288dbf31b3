
#ifndef REPORT_MESSAGE_H
#define REPORT_MESSAGE_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <pthread.h>
#include <sys/prctl.h>
#include <stdlib.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include "dronepod_k40t_protocol.h"
#include "gimbal_protocol.h"

#define MAX_DETECT_RESULT 255



typedef struct {
    int type;               //0: detect, 1: track
    int track_source;       //0: short focal lens, 1: long focal lens 2:infra
    int track_state;        //0: not updated, 1: tracking, 2: lost & re-tracking 3: losted
    AI_Target_Data_t track_result;
    int detect_result_num;
    AI_Target_Data_t detect_result[MAX_DETECT_RESULT];
    
} ai_result_t;


int start_report_message_proc();
void stop_report_message_proc();
void set_ai_result(ai_result_t* ai_result);


void set_gimbal_status(payload_gb_heartbeat_v1_t *payload);


#ifdef __cplusplus
}
#endif

#endif