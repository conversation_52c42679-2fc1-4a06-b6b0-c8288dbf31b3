#include "ive_zoom.h"

#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"
#include <sys/time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static vs_int32_t sample_ive_mmz_malloc(vs_char_t *p_zone_name, vs_char_t *p_mmb_name, vs_uint32_t len,	vs_uint64_t *p_phys_addr, vs_void_t **pp_virt_addr)
{
	vs_int32_t ret = 0;

	ret = vs_mal_mmz_alloc(p_zone_name, p_mmb_name, len, p_phys_addr);
	if (0 != ret) {
		vs_sample_trace("phys_addr failed to alloc mmz! ret = 0x%x len = %d\n", ret, len);
		return VS_FAILED;
	}
	*pp_virt_addr = vs_mal_sys_mmap(*p_phys_addr, len);
	if (NULL == *pp_virt_addr) {
		vs_sample_trace("virt address failed to alloc mmz!\n");
		return VS_FAILED;
	}
	memset(*pp_virt_addr, 0, len);

    return VS_SUCCESS;
}

static vs_void_t sample_ive_mmz_free(vs_uint64_t phys_addr, vs_void_t *p_virt_addr, vs_uint32_t len)
{
	vs_int32_t ret;

    if (0 == phys_addr || NULL == p_virt_addr) {
        return;
    }
	ret = vs_mal_sys_unmap(p_virt_addr, len);
	if (0 != ret) {
        vs_sample_trace("sys_unmap ret = 0x%x\n", ret);
    }
	ret = vs_mal_mmz_free(phys_addr);
	if (0 != ret) {
        vs_sample_trace("free ext ret = 0x%x\n", ret);
    }
    return;

}


uint64_t ive_rgb_zoom(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr)
{

    //STARTCLOCK
    //static int is_first_frame = 1;
    // if (zoom_factor <= 1.0 || zoom_factor > 10.0)
    if (zoom_factor <= 1.0 || zoom_factor > 15.0)
    {
        //printf("zoom factor not support\n");
        return 0;
    }
    else{
        printf("zoom factor:%f\n",zoom_factor);
    }

    //
    vs_int32_t ret = VS_FAILED;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;

    /* resize factor, range 1.0 - 8.0 */
    vs_float_t ive_resize_factor = zoom_factor;///10.0;

    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.width;
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
    vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
    vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

    printf("###1 zoom_factor:%f ive_image_dst_width:%d ive_image_dst_height:%d\n",ive_resize_factor, ive_image_dst_width, ive_image_dst_height);
    //vs_mal_ive_dma input width must be multiple of 32. 
    ive_image_dst_width = ive_image_dst_width % 2 == 0 ? ive_image_dst_width : ive_image_dst_width -1;
    ive_image_dst_width = (ive_image_dst_width/2 % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width/2 % 32)) *2;
    //ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width  - (ive_image_dst_width % 32);
    ive_image_dst_height = ive_image_dst_width * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;// 0.8 scale
    ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
    ive_image_dst_stride =  ive_image_dst_width;

    
    printf("###2 ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_dst_width, ive_image_dst_height);
    //start point
    vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2;
    vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2;

    // resize
    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    vs_uint32_t resize_with = ive_image_dst_width * ive_resize_factor;
    resize_with = resize_with % 2 == 0 ? resize_with : resize_with - 1;
    resize_with = (resize_with/2 % 32) == 0 ? resize_with : resize_with + (32 - (resize_with/2 % 32)) * 2 ;
    //resize_with = (resize_with % 32) == 0 ? resize_with : resize_with - (resize_with % 32);
    vs_uint32_t resize_height = resize_with * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;
    resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
    vs_uint32_t resize_stride = resize_with;
    
    printf("###3 resize_stride :%d resize_height:%d\n", resize_stride, resize_height);
    

    
    vs_uint32_t resize_size = resize_stride * resize_height * 2;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {

        printf("width:%d height:%d factor:%d\n", frame_info->frame.width, frame_info->frame.height, (float)frame_info->frame.height/(float)frame_info->frame.width);
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
        //goto exit_ive_mmz_free;
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    }
    
    vs_ive_image_s ive_image_src[4];
    vs_ive_image_s ive_image_dst[4];
    vs_ive_resize_cfg_s resize_cfg;

    printf("###### ive_image_origin_stride:%d ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_origin_stride, ive_image_dst_width/2, ive_image_dst_height/2);
    printf("###### resize_stride:%d resize_with:%d resize_height:%d\n", resize_stride, resize_with/2, resize_height/2);
    //////senction 0
    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    resize_cfg.image_num = 4;
    ive_image_src[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[0].stride[0] = ive_image_origin_stride;
    ive_image_src[0].stride[1] = ive_image_origin_stride;
    ive_image_src[0].width = ive_image_dst_width/2;
    ive_image_src[0].height = ive_image_dst_height/2;

    
    ive_image_dst[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[0].stride[0] = resize_stride;
    ive_image_dst[0].stride[1] = resize_stride;
    ive_image_dst[0].width = resize_with/2;
    ive_image_dst[0].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[0].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    
    ive_image_src[0].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;

    ive_image_dst[0].phys_addr[0] = resize_phys_addr;
    ive_image_dst[0].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    
    ive_image_dst[0].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[0].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;



    /////senction 1
    ive_image_src[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[1].stride[0] = ive_image_origin_stride;
    ive_image_src[1].stride[1] = ive_image_origin_stride;
    ive_image_src[1].width = ive_image_dst_width/2;
    ive_image_src[1].height = ive_image_dst_height/2;

    
    ive_image_dst[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[1].stride[0] = resize_stride;
    ive_image_dst[1].stride[1] = resize_stride;
    ive_image_dst[1].width = resize_with/2;
    ive_image_dst[1].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[1].phys_addr[0] = resize_phys_addr + resize_with/2;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2;



    //////senction 2
    ive_image_src[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[2].stride[0] = ive_image_origin_stride;
    ive_image_src[2].stride[1] = ive_image_origin_stride;
    ive_image_src[2].width = ive_image_dst_width/2;
    ive_image_src[2].height = ive_image_dst_height/2;

    
    ive_image_dst[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[2].stride[0] = resize_stride;
    ive_image_dst[2].stride[1] = resize_stride;
    ive_image_dst[2].width = resize_with/2;
    ive_image_dst[2].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[2].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    ive_image_src[2].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    
    //+ ive_image_dst_height/2  * ive_image_origin_stride
    ive_image_src[2].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;
    ive_image_src[2].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;

    ive_image_dst[2].phys_addr[0] = resize_phys_addr + (resize_stride * resize_height/2);
    ive_image_dst[2].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + (resize_stride * resize_height/2);
    
    ive_image_dst[2].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + (resize_stride * resize_height/4);
    ive_image_dst[2].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + (resize_stride * resize_height/4);


    //////senction 3
    ive_image_src[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[3].stride[0] = ive_image_origin_stride;
    ive_image_src[3].stride[1] = ive_image_origin_stride;
    ive_image_src[3].width = ive_image_dst_width/2;
    ive_image_src[3].height = ive_image_dst_height/2;

    
    ive_image_dst[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[3].stride[0] = resize_stride;
    ive_image_dst[3].stride[1] = resize_stride;
    ive_image_dst[3].width = resize_with/2;
    ive_image_dst[3].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[3].phys_addr[0] = resize_phys_addr + resize_with/2 + (resize_height/2) * resize_stride;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[3].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2 + (resize_height/2) * resize_stride;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[3].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[3].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;

    


    ret = vs_mal_ive_resize(&handle, ive_image_src, ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride :%d resize_height:%d, zoom_factor:%f\n", resize_stride, resize_height, zoom_factor);
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    // static int run_once = 1;
    // if (run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, resize_stride, resize_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }
    
    vs_uint32_t resize_src_x_point = resize_with/2 - ive_image_origin_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -ive_image_origin_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = ive_image_origin_width;
    ive_copy_src_data.height = ive_image_origin_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = ive_image_origin_stride;
    ive_copy_dst_data.width = ive_image_origin_width;
    ive_copy_dst_data.height = ive_image_origin_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, ive_image_origin_stride, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = ive_image_origin_width ;
    ive_copy_src_data.height = ive_image_origin_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = ive_image_origin_stride;
    ive_copy_dst_data.width = ive_image_origin_width;
    ive_copy_dst_data.height = ive_image_origin_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + ive_image_origin_stride * ive_image_origin_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }
    
    
exit_ive_mmz_free:

    //free mm
    sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //ENDCLOCK("ive copy and resize")
    
    return 0;

}





uint64_t ive_rgb_zoom_4k(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, vs_bool_t is_4k_input)
{

    //STARTCLOCK
    //static int is_first_frame = 1;
    // if (zoom_factor <= 1.0 || zoom_factor > 10.0)
    if (zoom_factor <= 1.0 || zoom_factor > 15.0)
    {
        //printf("zoom factor not support\n");
        return 0;
    }
    else{
        //printf("* zoom factor:%f *\n",zoom_factor);
    }

    
    //
    vs_int32_t ret = VS_FAILED;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;

    /* resize factor, range 1.0 - 8.0 */
    vs_float_t ive_resize_factor = zoom_factor;///10.0;

    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.width;
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
    vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
    vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

    //printf("###1 zoom_factor:%f ive_image_dst_width:%d ive_image_dst_height:%d\n",ive_resize_factor, ive_image_dst_width, ive_image_dst_height);
    //vs_mal_ive_dma input width must be multiple of 32. 
    ive_image_dst_width = ive_image_dst_width % 2 == 0 ? ive_image_dst_width : ive_image_dst_width -1;
    ive_image_dst_width = (ive_image_dst_width/2 % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width/2 % 32)) *2;
    //ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width  - (ive_image_dst_width % 32);
    ive_image_dst_height = ive_image_dst_width * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;// 0.8 scale
    ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
    ive_image_dst_stride =  ive_image_dst_width;

    
    //printf("###2 ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_dst_width, ive_image_dst_height);
    //start point
    vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2;
    vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2;

    // resize
    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    
    float tmp_resize_factor = ive_resize_factor;
    if(is_4k_input)
    {
        tmp_resize_factor = zoom_factor*0.5;
    }
    
    vs_uint32_t resize_with = ive_image_dst_width * tmp_resize_factor;
    resize_with = resize_with % 2 == 0 ? resize_with : resize_with - 1;
    resize_with = (resize_with/2 % 32) == 0 ? resize_with : resize_with + (32 - (resize_with/2 % 32)) * 2 ;
    //resize_with = (resize_with % 32) == 0 ? resize_with : resize_with - (resize_with % 32);
    vs_uint32_t resize_height = resize_with * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;
    resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
    vs_uint32_t resize_stride = resize_with;
    
    //printf("###3 resize_stride :%d resize_height:%d\n", resize_stride, resize_height);
    

    
    vs_uint32_t resize_size = resize_stride * resize_height * 2;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {

        printf("width:%d height:%d factor:%d\n", frame_info->frame.width, frame_info->frame.height, (float)frame_info->frame.height/(float)frame_info->frame.width);
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
        //goto exit_ive_mmz_free;
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    }
    
    vs_ive_image_s ive_image_src[4];
    vs_ive_image_s ive_image_dst[4];
    vs_ive_resize_cfg_s resize_cfg;

    //printf("###### ive_image_origin_stride:%d ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_origin_stride, ive_image_dst_width/2, ive_image_dst_height/2);
    //printf("###### resize_stride:%d resize_with:%d resize_height:%d\n", resize_stride, resize_with/2, resize_height/2);
    //////senction 0
    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    resize_cfg.image_num = 4;
    ive_image_src[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[0].stride[0] = ive_image_origin_stride;
    ive_image_src[0].stride[1] = ive_image_origin_stride;
    ive_image_src[0].width = ive_image_dst_width/2;
    ive_image_src[0].height = ive_image_dst_height/2;

    
    ive_image_dst[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[0].stride[0] = resize_stride;
    ive_image_dst[0].stride[1] = resize_stride;
    ive_image_dst[0].width = resize_with/2;
    ive_image_dst[0].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[0].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    
    ive_image_src[0].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;

    ive_image_dst[0].phys_addr[0] = resize_phys_addr;
    ive_image_dst[0].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    
    ive_image_dst[0].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[0].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;



    /////senction 1
    ive_image_src[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[1].stride[0] = ive_image_origin_stride;
    ive_image_src[1].stride[1] = ive_image_origin_stride;
    ive_image_src[1].width = ive_image_dst_width/2;
    ive_image_src[1].height = ive_image_dst_height/2;

    
    ive_image_dst[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[1].stride[0] = resize_stride;
    ive_image_dst[1].stride[1] = resize_stride;
    ive_image_dst[1].width = resize_with/2;
    ive_image_dst[1].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[1].phys_addr[0] = resize_phys_addr + resize_with/2;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2;



    //////senction 2
    ive_image_src[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[2].stride[0] = ive_image_origin_stride;
    ive_image_src[2].stride[1] = ive_image_origin_stride;
    ive_image_src[2].width = ive_image_dst_width/2;
    ive_image_src[2].height = ive_image_dst_height/2;

    
    ive_image_dst[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[2].stride[0] = resize_stride;
    ive_image_dst[2].stride[1] = resize_stride;
    ive_image_dst[2].width = resize_with/2;
    ive_image_dst[2].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[2].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    ive_image_src[2].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    
    //+ ive_image_dst_height/2  * ive_image_origin_stride
    ive_image_src[2].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;
    ive_image_src[2].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;

    ive_image_dst[2].phys_addr[0] = resize_phys_addr + (resize_stride * resize_height/2);
    ive_image_dst[2].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + (resize_stride * resize_height/2);
    
    ive_image_dst[2].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + (resize_stride * resize_height/4);
    ive_image_dst[2].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + (resize_stride * resize_height/4);


    //////senction 3
    ive_image_src[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[3].stride[0] = ive_image_origin_stride;
    ive_image_src[3].stride[1] = ive_image_origin_stride;
    ive_image_src[3].width = ive_image_dst_width/2;
    ive_image_src[3].height = ive_image_dst_height/2;

    
    ive_image_dst[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[3].stride[0] = resize_stride;
    ive_image_dst[3].stride[1] = resize_stride;
    ive_image_dst[3].width = resize_with/2;
    ive_image_dst[3].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[3].phys_addr[0] = resize_phys_addr + resize_with/2 + (resize_height/2) * resize_stride;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[3].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2 + (resize_height/2) * resize_stride;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[3].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[3].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;

    


    ret = vs_mal_ive_resize(&handle, ive_image_src, ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride :%d resize_height:%d, zoom_factor:%f\n", resize_stride, resize_height, zoom_factor);
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    // static int run_once = 1;
    // if (run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, resize_stride, resize_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }
    

    vs_uint32_t venc_input_width = 1920;
    vs_uint32_t venc_input_height = 1080;
    vs_uint32_t resize_src_x_point = resize_with/2 - venc_input_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width;
    ive_copy_src_data.height = venc_input_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, venc_input_width, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width ;
    ive_copy_src_data.height = venc_input_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + venc_input_width * venc_input_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }   
    

    
exit_ive_mmz_free:

    //free mm
    sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //ENDCLOCK("ive copy and resize")
    
    return 0;

}



#if 0
uint64_t ive_rgb_zoom_4k_ex(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, vs_bool_t is_4k_input, int dx, int dy)
{

    //STARTCLOCK
    //static int is_first_frame = 1;
    // if (zoom_factor <= 1.0 || zoom_factor > 10.0)
    if (zoom_factor <= 1.0 || zoom_factor > 15.0)
    {
        //printf("zoom factor not support\n");
        return 0;
    }
    else{
        //printf("* zoom factor:%f *\n",zoom_factor);
    }

    
    //
    vs_int32_t ret = VS_FAILED;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;

    /* resize factor, range 1.0 - 8.0 */
    vs_float_t ive_resize_factor = zoom_factor;///10.0;

    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.width;
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
    vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
    vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

    //printf("###1 zoom_factor:%f ive_image_dst_width:%d ive_image_dst_height:%d\n",ive_resize_factor, ive_image_dst_width, ive_image_dst_height);
    //vs_mal_ive_dma input width must be multiple of 32. 
    ive_image_dst_width = ive_image_dst_width % 2 == 0 ? ive_image_dst_width : ive_image_dst_width -1;
    ive_image_dst_width = (ive_image_dst_width/2 % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width/2 % 32)) *2;
    //ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width  - (ive_image_dst_width % 32);
    ive_image_dst_height = ive_image_dst_width * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;// 0.8 scale
    ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
    ive_image_dst_stride =  ive_image_dst_width;

    
    //printf("###2 ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_dst_width, ive_image_dst_height);
    //start point
    //vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2 - 96 - 16;
    //vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2 - 64 - 16;
    vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2 - dx;
    vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2 - dy;

    // resize
    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    
    float tmp_resize_factor = ive_resize_factor;
    if(is_4k_input)
    {   
        tmp_resize_factor = zoom_factor*0.5;
        //printf("long focal lens resize factor :%f \n", tmp_resize_factor);
        src_x_point = src_x_point - dx;
        src_y_point = src_y_point - dy;
    }
    
    vs_uint32_t resize_with = ive_image_dst_width * tmp_resize_factor;
    resize_with = resize_with % 2 == 0 ? resize_with : resize_with - 1;
    resize_with = (resize_with/2 % 32) == 0 ? resize_with : resize_with + (32 - (resize_with/2 % 32)) * 2 ;
    //resize_with = (resize_with % 32) == 0 ? resize_with : resize_with - (resize_with % 32);
    vs_uint32_t resize_height = resize_with * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;
    resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
    vs_uint32_t resize_stride = resize_with;
    
    //printf("###3 resize_stride :%d resize_height:%d\n", resize_stride, resize_height);
    

    
    vs_uint32_t resize_size = resize_stride * resize_height * 2;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {

        printf("width:%d height:%d factor:%d\n", frame_info->frame.width, frame_info->frame.height, (float)frame_info->frame.height/(float)frame_info->frame.width);
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
        //goto exit_ive_mmz_free;
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    }
    
    vs_ive_image_s ive_image_src[4];
    vs_ive_image_s ive_image_dst[4];
    vs_ive_resize_cfg_s resize_cfg;

    //printf("###### ive_image_origin_stride:%d ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_origin_stride, ive_image_dst_width/2, ive_image_dst_height/2);
    //printf("###### resize_stride:%d resize_with:%d resize_height:%d\n", resize_stride, resize_with/2, resize_height/2);
    //////senction 0
    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    resize_cfg.image_num = 4;
    ive_image_src[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[0].stride[0] = ive_image_origin_stride;
    ive_image_src[0].stride[1] = ive_image_origin_stride;
    ive_image_src[0].width = ive_image_dst_width/2;
    ive_image_src[0].height = ive_image_dst_height/2;

    
    ive_image_dst[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[0].stride[0] = resize_stride;
    ive_image_dst[0].stride[1] = resize_stride;
    ive_image_dst[0].width = resize_with/2;
    ive_image_dst[0].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[0].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    
    ive_image_src[0].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;

    ive_image_dst[0].phys_addr[0] = resize_phys_addr;
    ive_image_dst[0].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    
    ive_image_dst[0].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[0].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;



    /////senction 1
    ive_image_src[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[1].stride[0] = ive_image_origin_stride;
    ive_image_src[1].stride[1] = ive_image_origin_stride;
    ive_image_src[1].width = ive_image_dst_width/2;
    ive_image_src[1].height = ive_image_dst_height/2;

    
    ive_image_dst[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[1].stride[0] = resize_stride;
    ive_image_dst[1].stride[1] = resize_stride;
    ive_image_dst[1].width = resize_with/2;
    ive_image_dst[1].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[1].phys_addr[0] = resize_phys_addr + resize_with/2;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2;



    //////senction 2
    ive_image_src[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[2].stride[0] = ive_image_origin_stride;
    ive_image_src[2].stride[1] = ive_image_origin_stride;
    ive_image_src[2].width = ive_image_dst_width/2;
    ive_image_src[2].height = ive_image_dst_height/2;

    
    ive_image_dst[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[2].stride[0] = resize_stride;
    ive_image_dst[2].stride[1] = resize_stride;
    ive_image_dst[2].width = resize_with/2;
    ive_image_dst[2].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[2].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    ive_image_src[2].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    
    //+ ive_image_dst_height/2  * ive_image_origin_stride
    ive_image_src[2].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;
    ive_image_src[2].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;

    ive_image_dst[2].phys_addr[0] = resize_phys_addr + (resize_stride * resize_height/2);
    ive_image_dst[2].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + (resize_stride * resize_height/2);
    
    ive_image_dst[2].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + (resize_stride * resize_height/4);
    ive_image_dst[2].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + (resize_stride * resize_height/4);


    //////senction 3
    ive_image_src[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[3].stride[0] = ive_image_origin_stride;
    ive_image_src[3].stride[1] = ive_image_origin_stride;
    ive_image_src[3].width = ive_image_dst_width/2;
    ive_image_src[3].height = ive_image_dst_height/2;

    
    ive_image_dst[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[3].stride[0] = resize_stride;
    ive_image_dst[3].stride[1] = resize_stride;
    ive_image_dst[3].width = resize_with/2;
    ive_image_dst[3].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[3].phys_addr[0] = resize_phys_addr + resize_with/2 + (resize_height/2) * resize_stride;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[3].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2 + (resize_height/2) * resize_stride;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[3].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[3].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;

    


    ret = vs_mal_ive_resize(&handle, ive_image_src, ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride :%d resize_height:%d, zoom_factor:%f\n", resize_stride, resize_height, zoom_factor);
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    // static int run_once = 1;
    // if (run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, resize_stride, resize_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }
    
    vs_uint32_t venc_input_width = 1920;
    vs_uint32_t venc_input_height = 1080;
    vs_uint32_t resize_src_x_point = resize_with/2 - venc_input_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width;
    ive_copy_src_data.height = venc_input_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, venc_input_width, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width ;
    ive_copy_src_data.height = venc_input_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + venc_input_width * venc_input_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }   
    

    
exit_ive_mmz_free:

    //free mm
    sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //ENDCLOCK("ive copy and resize")
    
    return 0;

}
#endif


uint64_t resize_with_factor_and_output_1080P(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, vs_bool_t is_4k_input, int dx, int dy)
{

    //STARTCLOCK
    //static int is_first_frame = 1;
    // if (zoom_factor <= 1.0 || zoom_factor > 10.0)
    if (zoom_factor <= 1.0 || zoom_factor > 15.0)
    {
        //printf("zoom factor not support\n");
        return 0;
    }
    else{
        //printf("* zoom factor:%f *\n",zoom_factor);
    }

    
    //
    vs_int32_t ret = VS_FAILED;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;

    /* resize factor, range 1.0 - 8.0 */
    vs_float_t ive_resize_factor = zoom_factor;///10.0;

    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.width;
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
    vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
    vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

    //printf("###1 zoom_factor:%f ive_image_dst_width:%d ive_image_dst_height:%d\n",ive_resize_factor, ive_image_dst_width, ive_image_dst_height);
    //vs_mal_ive_dma input width must be multiple of 32. 
    ive_image_dst_width = ive_image_dst_width % 2 == 0 ? ive_image_dst_width : ive_image_dst_width -1;
    ive_image_dst_width = (ive_image_dst_width/2 % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width/2 % 32)) *2;
    //ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width  - (ive_image_dst_width % 32);
    ive_image_dst_height = ive_image_dst_width * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;// 0.8 scale
    ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
    ive_image_dst_stride =  ive_image_dst_width;

    
    //printf("###2 ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_dst_width, ive_image_dst_height);
    //start point
    //vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2 - 96 - 16;
    //vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2 - 64 - 16;
    vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2 - dx;
    vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2 - dy;

    // resize
    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    
    float tmp_resize_factor = ive_resize_factor;
    if(is_4k_input)
    {   
        tmp_resize_factor = zoom_factor*0.5;
        //printf("long focal lens resize factor :%f \n", tmp_resize_factor);
        src_x_point = src_x_point - dx;
        src_y_point = src_y_point - dy;
    }
    
    vs_uint32_t resize_with = ive_image_dst_width * tmp_resize_factor;
    resize_with = resize_with % 2 == 0 ? resize_with : resize_with - 1;
    resize_with = (resize_with/2 % 32) == 0 ? resize_with : resize_with + (32 - (resize_with/2 % 32)) * 2 ;
    //resize_with = (resize_with % 32) == 0 ? resize_with : resize_with - (resize_with % 32);
    vs_uint32_t resize_height = resize_with * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;
    resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
    vs_uint32_t resize_stride = resize_with;
    
    //printf("###3 resize_stride :%d resize_height:%d\n", resize_stride, resize_height);
    

    
    vs_uint32_t resize_size = resize_stride * resize_height * 2;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {

        printf("width:%d height:%d factor:%d\n", frame_info->frame.width, frame_info->frame.height, (float)frame_info->frame.height/(float)frame_info->frame.width);
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
        //goto exit_ive_mmz_free;
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    }
    
    vs_ive_image_s ive_image_src[4];
    vs_ive_image_s ive_image_dst[4];
    vs_ive_resize_cfg_s resize_cfg;

    //printf("###### ive_image_origin_stride:%d ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_origin_stride, ive_image_dst_width/2, ive_image_dst_height/2);
    //printf("###### resize_stride:%d resize_with:%d resize_height:%d\n", resize_stride, resize_with/2, resize_height/2);
    //////senction 0
    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    resize_cfg.image_num = 4;
    ive_image_src[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[0].stride[0] = ive_image_origin_stride;
    ive_image_src[0].stride[1] = ive_image_origin_stride;
    ive_image_src[0].width = ive_image_dst_width/2;
    ive_image_src[0].height = ive_image_dst_height/2;

    
    ive_image_dst[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[0].stride[0] = resize_stride;
    ive_image_dst[0].stride[1] = resize_stride;
    ive_image_dst[0].width = resize_with/2;
    ive_image_dst[0].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[0].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    
    ive_image_src[0].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;

    ive_image_dst[0].phys_addr[0] = resize_phys_addr;
    ive_image_dst[0].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    
    ive_image_dst[0].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[0].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;



    /////senction 1
    ive_image_src[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[1].stride[0] = ive_image_origin_stride;
    ive_image_src[1].stride[1] = ive_image_origin_stride;
    ive_image_src[1].width = ive_image_dst_width/2;
    ive_image_src[1].height = ive_image_dst_height/2;

    
    ive_image_dst[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[1].stride[0] = resize_stride;
    ive_image_dst[1].stride[1] = resize_stride;
    ive_image_dst[1].width = resize_with/2;
    ive_image_dst[1].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[1].phys_addr[0] = resize_phys_addr + resize_with/2;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2;



    //////senction 2
    ive_image_src[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[2].stride[0] = ive_image_origin_stride;
    ive_image_src[2].stride[1] = ive_image_origin_stride;
    ive_image_src[2].width = ive_image_dst_width/2;
    ive_image_src[2].height = ive_image_dst_height/2;

    
    ive_image_dst[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[2].stride[0] = resize_stride;
    ive_image_dst[2].stride[1] = resize_stride;
    ive_image_dst[2].width = resize_with/2;
    ive_image_dst[2].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[2].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    ive_image_src[2].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    
    //+ ive_image_dst_height/2  * ive_image_origin_stride
    ive_image_src[2].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;
    ive_image_src[2].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;

    ive_image_dst[2].phys_addr[0] = resize_phys_addr + (resize_stride * resize_height/2);
    ive_image_dst[2].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + (resize_stride * resize_height/2);
    
    ive_image_dst[2].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + (resize_stride * resize_height/4);
    ive_image_dst[2].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + (resize_stride * resize_height/4);


    //////senction 3
    ive_image_src[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[3].stride[0] = ive_image_origin_stride;
    ive_image_src[3].stride[1] = ive_image_origin_stride;
    ive_image_src[3].width = ive_image_dst_width/2;
    ive_image_src[3].height = ive_image_dst_height/2;

    
    ive_image_dst[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[3].stride[0] = resize_stride;
    ive_image_dst[3].stride[1] = resize_stride;
    ive_image_dst[3].width = resize_with/2;
    ive_image_dst[3].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[3].phys_addr[0] = resize_phys_addr + resize_with/2 + (resize_height/2) * resize_stride;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[3].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2 + (resize_height/2) * resize_stride;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[3].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[3].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;

    


    ret = vs_mal_ive_resize(&handle, ive_image_src, ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride :%d resize_height:%d, zoom_factor:%f\n", resize_stride, resize_height, zoom_factor);
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }

    //printf("actual zoom factor : %f\n", (float)resize_with/ive_image_dst_width);
    // static int run_once = 1;
    // if (run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, resize_stride, resize_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }
    
    vs_uint32_t venc_input_width = 1920;
    vs_uint32_t venc_input_height = 1080;
    vs_uint32_t resize_src_x_point = resize_with/2 - venc_input_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width;
    ive_copy_src_data.height = venc_input_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, venc_input_width, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width ;
    ive_copy_src_data.height = venc_input_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + venc_input_width * venc_input_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }   
    

    
exit_ive_mmz_free:

    //free mm
    sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //ENDCLOCK("ive copy and resize")
    

    frame_info->frame.stride[0] = 1920;
    frame_info->frame.stride[1] = 1920;
    frame_info->frame.stride[2] = 1920;
    frame_info->frame.width = 1920;
    frame_info->frame.height = 1080; 



    return 0;

}


uint64_t resize_with_factor_and_output_1080P_ex(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, vs_bool_t is_4k_input, int dx, int dy)
{

    //STARTCLOCK
    //static int is_first_frame = 1;
    // if (zoom_factor <= 1.0 || zoom_factor > 10.0)
    if (zoom_factor <= 1.0 || zoom_factor > 15.0)
    {
        //printf("zoom factor not support\n");
        return 0;
    }
    else{
        //printf("* zoom factor:%f *\n",zoom_factor);
    }

    
    //
    vs_int32_t ret = VS_FAILED;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;

    /* resize factor, range 1.0 - 8.0 */
    vs_float_t ive_resize_factor = zoom_factor;///10.0;

    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.width;
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
    vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
    vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

    //printf("###1 zoom_factor:%f ive_image_dst_width:%d ive_image_dst_height:%d\n",ive_resize_factor, ive_image_dst_width, ive_image_dst_height);
    //vs_mal_ive_dma input width must be multiple of 32. 
    ive_image_dst_width = ive_image_dst_width % 2 == 0 ? ive_image_dst_width : ive_image_dst_width -1;
    ive_image_dst_width = (ive_image_dst_width/2 % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width/2 % 32)) *2;
    //ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width  - (ive_image_dst_width % 32);
    ive_image_dst_height = ive_image_dst_width * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;// 0.8 scale
    ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
    ive_image_dst_stride =  ive_image_dst_width;

    
    //printf("###2 ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_dst_width, ive_image_dst_height);
    //start point
    //vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2 - 96 - 16;
    //vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2 - 64 - 16;
    vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2 - dx;
    vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2 - dy;

    // resize
    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    
    float tmp_resize_factor = ive_resize_factor;
    if(is_4k_input)
    {   
        tmp_resize_factor = zoom_factor*0.5;
        //printf("long focal lens resize factor :%f \n", tmp_resize_factor);
        src_x_point = src_x_point - dx;
        src_y_point = src_y_point - dy;
    }
    
    vs_uint32_t resize_with = ive_image_dst_width * tmp_resize_factor;
    resize_with = resize_with % 2 == 0 ? resize_with : resize_with - 1;
    resize_with = (resize_with/2 % 32) == 0 ? resize_with : resize_with + (32 - (resize_with/2 % 32)) * 2 ;
    //resize_with = (resize_with % 32) == 0 ? resize_with : resize_with - (resize_with % 32);
    vs_uint32_t resize_height = resize_with * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;
    resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
    vs_uint32_t resize_stride = resize_with;
    
    //printf("###3 resize_stride :%d resize_height:%d\n", resize_stride, resize_height);
    

    
    vs_uint32_t resize_size = resize_stride * resize_height * 2;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {

        printf("width:%d height:%d factor:%d\n", frame_info->frame.width, frame_info->frame.height, (float)frame_info->frame.height/(float)frame_info->frame.width);
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
        //goto exit_ive_mmz_free;
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    }
    
    vs_ive_image_s ive_image_src[4];
    vs_ive_image_s ive_image_dst[4];
    vs_ive_resize_cfg_s resize_cfg;

    //printf("###### ive_image_origin_stride:%d ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_origin_stride, ive_image_dst_width/2, ive_image_dst_height/2);
    //printf("###### resize_stride:%d resize_with:%d resize_height:%d\n", resize_stride, resize_with/2, resize_height/2);
    //////senction 0
    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    resize_cfg.image_num = 4;
    ive_image_src[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[0].stride[0] = ive_image_origin_stride;
    ive_image_src[0].stride[1] = ive_image_origin_stride;
    ive_image_src[0].width = ive_image_dst_width/2;
    ive_image_src[0].height = ive_image_dst_height/2;

    
    ive_image_dst[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[0].stride[0] = resize_stride;
    ive_image_dst[0].stride[1] = resize_stride;
    ive_image_dst[0].width = resize_with/2;
    ive_image_dst[0].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[0].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    
    ive_image_src[0].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;

    ive_image_dst[0].phys_addr[0] = resize_phys_addr;
    ive_image_dst[0].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    
    ive_image_dst[0].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[0].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;



    /////senction 1
    ive_image_src[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[1].stride[0] = ive_image_origin_stride;
    ive_image_src[1].stride[1] = ive_image_origin_stride;
    ive_image_src[1].width = ive_image_dst_width/2;
    ive_image_src[1].height = ive_image_dst_height/2;

    
    ive_image_dst[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[1].stride[0] = resize_stride;
    ive_image_dst[1].stride[1] = resize_stride;
    ive_image_dst[1].width = resize_with/2;
    ive_image_dst[1].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[1].phys_addr[0] = resize_phys_addr + resize_with/2;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2;



    //////senction 2
    ive_image_src[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[2].stride[0] = ive_image_origin_stride;
    ive_image_src[2].stride[1] = ive_image_origin_stride;
    ive_image_src[2].width = ive_image_dst_width/2;
    ive_image_src[2].height = ive_image_dst_height/2;

    
    ive_image_dst[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[2].stride[0] = resize_stride;
    ive_image_dst[2].stride[1] = resize_stride;
    ive_image_dst[2].width = resize_with/2;
    ive_image_dst[2].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[2].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    ive_image_src[2].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    
    //+ ive_image_dst_height/2  * ive_image_origin_stride
    ive_image_src[2].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;
    ive_image_src[2].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;

    ive_image_dst[2].phys_addr[0] = resize_phys_addr + (resize_stride * resize_height/2);
    ive_image_dst[2].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + (resize_stride * resize_height/2);
    
    ive_image_dst[2].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + (resize_stride * resize_height/4);
    ive_image_dst[2].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + (resize_stride * resize_height/4);


    //////senction 3
    ive_image_src[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[3].stride[0] = ive_image_origin_stride;
    ive_image_src[3].stride[1] = ive_image_origin_stride;
    ive_image_src[3].width = ive_image_dst_width/2;
    ive_image_src[3].height = ive_image_dst_height/2;

    
    ive_image_dst[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[3].stride[0] = resize_stride;
    ive_image_dst[3].stride[1] = resize_stride;
    ive_image_dst[3].width = resize_with/2;
    ive_image_dst[3].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[3].phys_addr[0] = resize_phys_addr + resize_with/2 + (resize_height/2) * resize_stride;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[3].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2 + (resize_height/2) * resize_stride;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[3].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[3].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;

    


    ret = vs_mal_ive_resize(&handle, ive_image_src, ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride :%d resize_height:%d, zoom_factor:%f\n", resize_stride, resize_height, zoom_factor);
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    // static int run_once = 1;
    // if (run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, resize_stride, resize_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }
    
    vs_uint32_t venc_input_width = 1920;
    vs_uint32_t venc_input_height = 1080;
    vs_uint32_t resize_src_x_point = resize_with/2 - venc_input_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width;
    ive_copy_src_data.height = venc_input_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, venc_input_width, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width ;
    ive_copy_src_data.height = venc_input_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + venc_input_width * venc_input_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }   
    

    
exit_ive_mmz_free:

    //free mm
    sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //ENDCLOCK("ive copy and resize")
    

    frame_info->frame.stride[0] = 1920;
    frame_info->frame.stride[1] = 1920;
    frame_info->frame.stride[2] = 1920;
    frame_info->frame.width = 1920;
    frame_info->frame.height = 1080; 



    return 0;

}



uint64_t resize_with_facotr_ir(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, vs_bool_t is_4k_input, int dx, int dy)
{

    //STARTCLOCK
    //static int is_first_frame = 1;
    // if (zoom_factor <= 1.0 || zoom_factor > 10.0)
    if (zoom_factor <= 1.0 || zoom_factor > 15.0)
    {
        //printf("zoom factor not support\n");
        return 0;
    }
    else{
        //printf("* zoom factor:%f *\n",zoom_factor);
    }

    
    //
    vs_int32_t ret = VS_FAILED;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;

    /* resize factor, range 1.0 - 8.0 */
    vs_float_t ive_resize_factor = zoom_factor;///10.0;

    

    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.width;
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    //printf("ive_image_origin_stride: %d ive_image_origin_width: %d ive_image_origin_height: %d", ive_image_origin_stride, ive_image_origin_width, ive_image_origin_height);
    //printf("ive_resize_factor : %f\n", ive_resize_factor);

    vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
    vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
    vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

    //printf("###1 zoom_factor:%f ive_image_dst_width:%d ive_image_dst_height:%d\n",ive_resize_factor, ive_image_dst_width, ive_image_dst_height);
    //vs_mal_ive_dma input width must be multiple of 32. 
    ive_image_dst_width = ive_image_dst_width % 2 == 0 ? ive_image_dst_width : ive_image_dst_width -1;
    ive_image_dst_width = (ive_image_dst_width/2 % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width/2 % 32)) *2;
    //ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width  - (ive_image_dst_width % 32);
    ive_image_dst_height = ive_image_dst_width * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;// 0.8 scale
    ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
    ive_image_dst_stride =  ive_image_dst_width;

    
    //printf("###2 ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_dst_width, ive_image_dst_height);
    //start point
    //vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2 - 96 - 16;
    //vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2 - 64 - 16;
    vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2 - dx;
    vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2 - dy;

    // resize
    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    
    float tmp_resize_factor = ive_resize_factor;
    if(is_4k_input)
    {   
        tmp_resize_factor = zoom_factor*0.5;
        //printf("long focal lens resize factor :%f \n", tmp_resize_factor);
        src_x_point = src_x_point - dx;
        src_y_point = src_y_point - dy;
    }
    
    vs_uint32_t resize_with = ive_image_dst_width * tmp_resize_factor;
    resize_with = resize_with % 2 == 0 ? resize_with : resize_with - 1;
    resize_with = (resize_with/2 % 32) == 0 ? resize_with : resize_with + (32 - (resize_with/2 % 32)) * 2 ;
    //resize_with = (resize_with % 32) == 0 ? resize_with : resize_with - (resize_with % 32);
    vs_uint32_t resize_height = resize_with * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;
    resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
    vs_uint32_t resize_stride = resize_with;
    
    //printf("###3 resize_stride :%d resize_height:%d\n", resize_stride, resize_height);
    

    
    vs_uint32_t resize_size = resize_stride * resize_height * 2;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {

        printf("width:%d height:%d factor:%d\n", frame_info->frame.width, frame_info->frame.height, (float)frame_info->frame.height/(float)frame_info->frame.width);
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
        //goto exit_ive_mmz_free;
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    }
    
    vs_ive_image_s ive_image_src[4];
    vs_ive_image_s ive_image_dst[4];
    vs_ive_resize_cfg_s resize_cfg;

    //printf("###### ive_image_origin_stride:%d ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_origin_stride, ive_image_dst_width/2, ive_image_dst_height/2);
    //printf("###### resize_stride:%d resize_with:%d resize_height:%d\n", resize_stride, resize_with/2, resize_height/2);
    //////senction 0
    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    resize_cfg.image_num = 4;
    ive_image_src[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[0].stride[0] = ive_image_origin_stride;
    ive_image_src[0].stride[1] = ive_image_origin_stride;
    ive_image_src[0].width = ive_image_dst_width/2;
    ive_image_src[0].height = ive_image_dst_height/2;

    
    ive_image_dst[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[0].stride[0] = resize_stride;
    ive_image_dst[0].stride[1] = resize_stride;
    ive_image_dst[0].width = resize_with/2;
    ive_image_dst[0].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[0].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    
    ive_image_src[0].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;

    ive_image_dst[0].phys_addr[0] = resize_phys_addr;
    ive_image_dst[0].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    
    ive_image_dst[0].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[0].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;



    /////senction 1
    ive_image_src[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[1].stride[0] = ive_image_origin_stride;
    ive_image_src[1].stride[1] = ive_image_origin_stride;
    ive_image_src[1].width = ive_image_dst_width/2;
    ive_image_src[1].height = ive_image_dst_height/2;

    
    ive_image_dst[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[1].stride[0] = resize_stride;
    ive_image_dst[1].stride[1] = resize_stride;
    ive_image_dst[1].width = resize_with/2;
    ive_image_dst[1].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[1].phys_addr[0] = resize_phys_addr + resize_with/2;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2;



    //////senction 2
    ive_image_src[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[2].stride[0] = ive_image_origin_stride;
    ive_image_src[2].stride[1] = ive_image_origin_stride;
    ive_image_src[2].width = ive_image_dst_width/2;
    ive_image_src[2].height = ive_image_dst_height/2;

    
    ive_image_dst[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[2].stride[0] = resize_stride;
    ive_image_dst[2].stride[1] = resize_stride;
    ive_image_dst[2].width = resize_with/2;
    ive_image_dst[2].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[2].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    ive_image_src[2].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    
    //+ ive_image_dst_height/2  * ive_image_origin_stride
    ive_image_src[2].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;
    ive_image_src[2].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;

    ive_image_dst[2].phys_addr[0] = resize_phys_addr + (resize_stride * resize_height/2);
    ive_image_dst[2].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + (resize_stride * resize_height/2);
    
    ive_image_dst[2].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + (resize_stride * resize_height/4);
    ive_image_dst[2].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + (resize_stride * resize_height/4);


    //////senction 3
    ive_image_src[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[3].stride[0] = ive_image_origin_stride;
    ive_image_src[3].stride[1] = ive_image_origin_stride;
    ive_image_src[3].width = ive_image_dst_width/2;
    ive_image_src[3].height = ive_image_dst_height/2;

    
    ive_image_dst[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[3].stride[0] = resize_stride;
    ive_image_dst[3].stride[1] = resize_stride;
    ive_image_dst[3].width = resize_with/2;
    ive_image_dst[3].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[3].phys_addr[0] = resize_phys_addr + resize_with/2 + (resize_height/2) * resize_stride;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[3].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2 + (resize_height/2) * resize_stride;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[3].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[3].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;

    


    ret = vs_mal_ive_resize(&handle, ive_image_src, ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride :%d resize_height:%d, zoom_factor:%f\n", resize_stride, resize_height, zoom_factor);
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    // static int run_once = 1;
    // if (run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, resize_stride, resize_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }
    
    vs_uint32_t venc_input_width = 1920;
    vs_uint32_t venc_input_height = 1080;
    vs_uint32_t resize_src_x_point = resize_with/2 - venc_input_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width;
    ive_copy_src_data.height = venc_input_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, venc_input_width, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width ;
    ive_copy_src_data.height = venc_input_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + venc_input_width * venc_input_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }   
    

    
exit_ive_mmz_free:

    //free mm
    sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //ENDCLOCK("ive copy and resize")
    
    return 0;

}


#if 0
uint64_t ive_rgb_zoom_4k(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, vs_bool_t is_4k_input)
{

    //STARTCLOCK
    //static int is_first_frame = 1;
    // if (zoom_factor <= 1.0 || zoom_factor > 10.0)
    if (zoom_factor <= 1.0 || zoom_factor > 15.0)
    {
        //printf("zoom factor not support\n");
        return 0;
    }
    else{
        //printf("* zoom factor:%f *\n",zoom_factor);
    }

    
    //
    vs_int32_t ret = VS_FAILED;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;

    /* resize factor, range 1.0 - 8.0 */
    vs_float_t ive_resize_factor = zoom_factor;///10.0;

    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.width;
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
    vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
    vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

    //printf("###1 zoom_factor:%f ive_image_dst_width:%d ive_image_dst_height:%d\n",ive_resize_factor, ive_image_dst_width, ive_image_dst_height);
    //vs_mal_ive_dma input width must be multiple of 32. 
    ive_image_dst_width = ive_image_dst_width % 2 == 0 ? ive_image_dst_width : ive_image_dst_width -1;
    ive_image_dst_width = (ive_image_dst_width/2 % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width/2 % 32)) *2;
    //ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width  - (ive_image_dst_width % 32);
    ive_image_dst_height = ive_image_dst_width * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;// 0.8 scale
    ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
    ive_image_dst_stride =  ive_image_dst_width;

    
    //printf("###2 ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_dst_width, ive_image_dst_height);
    //start point
    vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2;
    vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2;

    // resize
    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    
    float tmp_resize_factor = ive_resize_factor;
    if(zoom_factor >= 2.1)
    {
        tmp_resize_factor = zoom_factor*0.5;
    }
    
    vs_uint32_t resize_with = ive_image_dst_width * tmp_resize_factor;
    resize_with = resize_with % 2 == 0 ? resize_with : resize_with - 1;
    resize_with = (resize_with/2 % 32) == 0 ? resize_with : resize_with + (32 - (resize_with/2 % 32)) * 2 ;
    //resize_with = (resize_with % 32) == 0 ? resize_with : resize_with - (resize_with % 32);
    vs_uint32_t resize_height = resize_with * ((float)frame_info->frame.height/(float)frame_info->frame.width);//0.5625;
    resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
    vs_uint32_t resize_stride = resize_with;
    
    //printf("###3 resize_stride :%d resize_height:%d\n", resize_stride, resize_height);
    

    
    vs_uint32_t resize_size = resize_stride * resize_height * 2;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {

        printf("width:%d height:%d factor:%d\n", frame_info->frame.width, frame_info->frame.height, (float)frame_info->frame.height/(float)frame_info->frame.width);
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
        //goto exit_ive_mmz_free;
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    }
    
    vs_ive_image_s ive_image_src[4];
    vs_ive_image_s ive_image_dst[4];
    vs_ive_resize_cfg_s resize_cfg;

    //printf("###### ive_image_origin_stride:%d ive_image_dst_width:%d ive_image_dst_height:%d\n", ive_image_origin_stride, ive_image_dst_width/2, ive_image_dst_height/2);
    //printf("###### resize_stride:%d resize_with:%d resize_height:%d\n", resize_stride, resize_with/2, resize_height/2);
    //////senction 0
    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    resize_cfg.image_num = 4;
    ive_image_src[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[0].stride[0] = ive_image_origin_stride;
    ive_image_src[0].stride[1] = ive_image_origin_stride;
    ive_image_src[0].width = ive_image_dst_width/2;
    ive_image_src[0].height = ive_image_dst_height/2;

    
    ive_image_dst[0].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[0].stride[0] = resize_stride;
    ive_image_dst[0].stride[1] = resize_stride;
    ive_image_dst[0].width = resize_with/2;
    ive_image_dst[0].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[0].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    
    ive_image_src[0].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[0].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;

    ive_image_dst[0].phys_addr[0] = resize_phys_addr;
    ive_image_dst[0].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    
    ive_image_dst[0].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[0].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;



    /////senction 1
    ive_image_src[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[1].stride[0] = ive_image_origin_stride;
    ive_image_src[1].stride[1] = ive_image_origin_stride;
    ive_image_src[1].width = ive_image_dst_width/2;
    ive_image_src[1].height = ive_image_dst_height/2;

    
    ive_image_dst[1].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[1].stride[0] = resize_stride;
    ive_image_dst[1].stride[1] = resize_stride;
    ive_image_dst[1].width = resize_with/2;
    ive_image_dst[1].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[1].phys_addr[0] = resize_phys_addr + resize_with/2;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2;



    //////senction 2
    ive_image_src[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[2].stride[0] = ive_image_origin_stride;
    ive_image_src[2].stride[1] = ive_image_origin_stride;
    ive_image_src[2].width = ive_image_dst_width/2;
    ive_image_src[2].height = ive_image_dst_height/2;

    
    ive_image_dst[2].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[2].stride[0] = resize_stride;
    ive_image_dst[2].stride[1] = resize_stride;
    ive_image_dst[2].width = resize_with/2;
    ive_image_dst[2].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    ive_image_src[2].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    ive_image_src[2].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point;
    
    //+ ive_image_dst_height/2  * ive_image_origin_stride
    ive_image_src[2].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;
    ive_image_src[2].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_height/4  * ive_image_origin_stride;

    ive_image_dst[2].phys_addr[0] = resize_phys_addr + (resize_stride * resize_height/2);
    ive_image_dst[2].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + (resize_stride * resize_height/2);
    
    ive_image_dst[2].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + (resize_stride * resize_height/4);
    ive_image_dst[2].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + (resize_stride * resize_height/4);


    //////senction 3
    ive_image_src[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_src[3].stride[0] = ive_image_origin_stride;
    ive_image_src[3].stride[1] = ive_image_origin_stride;
    ive_image_src[3].width = ive_image_dst_width/2;
    ive_image_src[3].height = ive_image_dst_height/2;

    
    ive_image_dst[3].type = E_IVE_IMAGE_TYPE_YUV420SP;//E_IVE_IMAGE_TYPE_U8C1;
    ive_image_dst[3].stride[0] = resize_stride;
    ive_image_dst[3].stride[1] = resize_stride;
    ive_image_dst[3].width = resize_with/2;
    ive_image_dst[3].height = resize_height/2;



    //printf("resize_stride :%d ive_image_dst_height:%d\n", ive_image_origin_stride ,ive_image_dst_height);
    //phy addr mmp virt addr 
    //ive_image_src[1].phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[0] = frame_info->frame.phys_addr[0] + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    //ive_image_src[1].virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[0] = src_virt_addr + (src_y_point + ive_image_dst_height/2) * ive_image_origin_stride + src_x_point + ive_image_dst_width/2;
    
    //ive_image_src[1].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;
    //ive_image_src[1].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src[3].virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride * ive_image_origin_stride + src_x_point + ive_image_dst_width/2 + ive_image_dst_height/4  * ive_image_origin_stride;

    //ive_image_dst[1].phys_addr[0] = resize_phys_addr;
    ive_image_dst[3].phys_addr[0] = resize_phys_addr + resize_with/2 + (resize_height/2) * resize_stride;
    //ive_image_dst[1].virt_addr[0] = (vs_void_t*)p_resize_virt_addr;
    ive_image_dst[3].virt_addr[0] = (vs_void_t*)p_resize_virt_addr + resize_with/2 + (resize_height/2) * resize_stride;
    
    //ive_image_dst[1].phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst[3].phys_addr[1] = resize_phys_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;
    //ive_image_dst[1].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;
    ive_image_dst[3].virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_with/2 + (resize_height/4) * resize_stride;

    


    ret = vs_mal_ive_resize(&handle, ive_image_src, ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride :%d resize_height:%d, zoom_factor:%f\n", resize_stride, resize_height, zoom_factor);
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        return -1;
    }
    // static int run_once = 1;
    // if (run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, resize_stride, resize_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }
    

    vs_uint32_t venc_input_width = 1920;
    vs_uint32_t venc_input_height = 1080;
    vs_uint32_t resize_src_x_point = resize_with/2 - venc_input_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width;
    ive_copy_src_data.height = venc_input_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, venc_input_width, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width ;
    ive_copy_src_data.height = venc_input_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + venc_input_width * venc_input_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }   
    

    
exit_ive_mmz_free:

    //free mm
    sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //ENDCLOCK("ive copy and resize")
    
    return 0;

}

#endif






/// @brief resize 4k frame to 1080P frame
/// @param frame_info 
/// @param src_virt_addr 
/// @param resize_phys_addr 
/// @param p_resize_virt_addr 
/// @param resize_width 
/// @param resize_height 
/// @return 
uint64_t resize_4K_to_1080P_frame(vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, unsigned int resize_width, unsigned int resize_height)
{

    //STARTCLOCK
    int ret = 0;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;


    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.stride[0];
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    //start point
    vs_int32_t src_x_point = 0;
    vs_int32_t src_y_point = 0;

    vs_uint32_t resize_stride = resize_width;
    vs_ive_image_s ive_image_src;
    vs_ive_image_s ive_image_dst;
    vs_ive_resize_cfg_s resize_cfg;


    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    if (resize_width < ive_image_origin_width)
    {
        resize_cfg.mode = E_IVE_RESIZE_MODE_AREA;
    }
    else{
        resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    }
    
    resize_cfg.image_num = 1;
    ive_image_src.type = E_IVE_IMAGE_TYPE_YUV420SP;
    ive_image_src.stride[0] = ive_image_origin_stride;
    ive_image_src.stride[1] = ive_image_origin_stride;
    ive_image_src.width = ive_image_origin_width;
    ive_image_src.height = ive_image_origin_height;

    
    ive_image_dst.type = E_IVE_IMAGE_TYPE_YUV420SP;
    ive_image_dst.stride[0] = resize_stride;
    ive_image_dst.stride[1] = resize_stride;
    ive_image_dst.width = resize_width;
    ive_image_dst.height = resize_height;

    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    vs_uint32_t resize_size = resize_stride * resize_height * 1.5;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
    }


    //phy addr mmp virt addr 
    ive_image_src.phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src.virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;

    ive_image_src.phys_addr[1] = frame_info->frame.phys_addr[0] + ive_image_origin_stride * ive_image_origin_height + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src.virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;


    ive_image_dst.phys_addr[0] = resize_phys_addr;
    ive_image_dst.virt_addr[0] = (vs_void_t*)p_resize_virt_addr;


    ive_image_dst.phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst.virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;


    ret = vs_mal_ive_resize(&handle, &ive_image_src, &ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
    }



   
    vs_uint32_t venc_input_width = 1920;
    vs_uint32_t venc_input_height = 1080;
    vs_uint32_t resize_src_x_point = resize_width/2 - venc_input_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width;
    ive_copy_src_data.height = venc_input_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, venc_input_width, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width ;
    ive_copy_src_data.height = venc_input_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = venc_input_width;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + venc_input_width * venc_input_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }   
    

    frame_info->frame.stride[0] = resize_width;
    frame_info->frame.stride[1] = resize_width;
    frame_info->frame.stride[2] = resize_width;
    frame_info->frame.width = resize_width;
    frame_info->frame.height = resize_height; 



    // static int is_save = 1;
    // is_save ++;
    // if (is_save == 100)
    // {
    //     FILE *p_fout = fopen("/tmp/zhx/ive_zoom_out_background9.yuv", "wb");
    //     if (NULL == p_fout) {
    //         perror("Failed to open output file");
    //     } else {
    //         size_t written = fwrite((vs_char_t *)src_virt_addr, 1, 1920*1080*1.5, p_fout);
    //         if (written != 1920*1080*1.5) {
    //             perror("File write incomplete");
    //         }
    //         fclose(p_fout);
    //     }

    // }

    
    // for (int i = 0; i < 1080; i++)
    // {
    //     memcpy(src_background_virt_addr + background_frame_width * sizeof(unsigned char) * ( i + starty) + startx, src_pip_virt_addr + pip_frame_width * sizeof(unsigned char) * i, pip_frame_width * sizeof(unsigned char));
    // }
    
    // for (int i = 0; i < pip_frame_height / 2; i++)
    // {
    //     memcpy(src_background_virt_addr + background_frame_width * background_frame_height * sizeof(unsigned char) + background_frame_width * sizeof(unsigned char) *(i + starty/2) + startx, src_pip_virt_addr + pip_frame_width * pip_frame_height + pip_frame_width * i * sizeof(unsigned char), pip_frame_width * sizeof(unsigned char));
    // }
        

    
    //memcpy(src_virt_addr, p_resize_virt_addr , 1920*1080*1.5);

    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //printf("sample_ive_mmz_free \n");
    //ENDCLOCK("------END RESIZE-------")
    
}


#if 0
/// @brief resize 4k frame to 1080P frame
/// @param frame_info 
/// @param src_virt_addr 
/// @param resize_phys_addr 
/// @param p_resize_virt_addr 
/// @param resize_width 
/// @param resize_height 
/// @return 
uint64_t resize_4K_to_1080P_frame(vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, unsigned int resize_width, unsigned int resize_height)
{

    //STARTCLOCK
    int ret = 0;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;


    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.stride[0];
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    //start point
    vs_int32_t src_x_point = 0;
    vs_int32_t src_y_point = 0;

    vs_uint32_t resize_stride = ive_image_origin_stride;
    vs_ive_image_s ive_image_src;
    vs_ive_image_s ive_image_dst;
    vs_ive_resize_cfg_s resize_cfg;


    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    if (resize_width < ive_image_origin_width)
    {
        resize_cfg.mode = E_IVE_RESIZE_MODE_AREA;
    }
    else{
        resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    }
    
    resize_cfg.image_num = 1;
    ive_image_src.type = E_IVE_IMAGE_TYPE_YUV420SP;
    ive_image_src.stride[0] = ive_image_origin_stride;
    ive_image_src.stride[1] = ive_image_origin_stride;
    ive_image_src.width = ive_image_origin_width;
    ive_image_src.height = ive_image_origin_height;

    
    ive_image_dst.type = E_IVE_IMAGE_TYPE_YUV420SP;
    ive_image_dst.stride[0] = resize_stride;
    ive_image_dst.stride[1] = resize_stride;
    ive_image_dst.width = resize_width;
    ive_image_dst.height = resize_height;

    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    vs_uint32_t resize_size = resize_stride * resize_height * 1.5;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
    }


    //phy addr mmp virt addr 
    ive_image_src.phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src.virt_addr[0] = src_virt_addr + src_y_point * ive_image_origin_stride + src_x_point;

    ive_image_src.phys_addr[1] = frame_info->frame.phys_addr[0] + ive_image_origin_stride * ive_image_origin_height + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src.virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;


    ive_image_dst.phys_addr[0] = resize_phys_addr;
    ive_image_dst.virt_addr[0] = (vs_void_t*)p_resize_virt_addr;


    ive_image_dst.phys_addr[1] = resize_phys_addr + resize_stride * resize_height;
    ive_image_dst.virt_addr[1] = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height;


    ret = vs_mal_ive_resize(&handle, &ive_image_src, &ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
    }


    memset(src_virt_addr, 0, 3840*2160*1.5);

   
    vs_uint32_t venc_input_width = 1920;
    vs_uint32_t venc_input_height = 1080;
    vs_uint32_t resize_src_x_point = resize_width/2 - venc_input_width/2 ;
    vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width;
    ive_copy_src_data.height = venc_input_height;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = resize_stride;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[0];
    ive_copy_dst_data.virt_addr = src_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",resize_stride, venc_input_width, ive_image_origin_width, ive_image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = resize_stride;//resize_stride;
    ive_copy_src_data.width = venc_input_width ;
    ive_copy_src_data.height = venc_input_height ;
    ive_copy_src_data.phys_addr = resize_phys_addr + resize_stride * resize_height  + resize_src_y_point/2 * resize_stride + resize_src_x_point;
    ive_copy_src_data.virt_addr = (vs_void_t*)p_resize_virt_addr + resize_stride * resize_height + resize_src_y_point/2 * resize_stride + resize_src_x_point;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = resize_stride;
    ive_copy_dst_data.width = venc_input_width;
    ive_copy_dst_data.height = venc_input_height;
    ive_copy_dst_data.phys_addr = frame_info->frame.phys_addr[1];
    ive_copy_dst_data.virt_addr = src_virt_addr + venc_input_width * venc_input_height;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }   
    

    frame_info->frame.stride[0] = resize_stride;
    frame_info->frame.stride[1] = resize_stride;
    frame_info->frame.stride[2] = resize_stride;
    frame_info->frame.width = resize_width;
    frame_info->frame.height = resize_height; 



    // static int is_save = 1;
    // is_save ++;
    // if (is_save == 300)
    // {
    //     FILE *p_fout = fopen("/tmp/zhx/ive_zoom_out_background7.yuv", "wb");
    //     if (NULL == p_fout) {
    //         perror("Failed to open output file");
    //     } else {
    //         size_t written = fwrite((vs_char_t *)src_virt_addr, 1, 1920*1080*1.5, p_fout);
    //         if (written != 1920*1080*1.5) {
    //             perror("File write incomplete");
    //         }
    //         fclose(p_fout);
    //     }

    // }

    
    // for (int i = 0; i < 1080; i++)
    // {
    //     memcpy(src_background_virt_addr + background_frame_width * sizeof(unsigned char) * ( i + starty) + startx, src_pip_virt_addr + pip_frame_width * sizeof(unsigned char) * i, pip_frame_width * sizeof(unsigned char));
    // }
    
    // for (int i = 0; i < pip_frame_height / 2; i++)
    // {
    //     memcpy(src_background_virt_addr + background_frame_width * background_frame_height * sizeof(unsigned char) + background_frame_width * sizeof(unsigned char) *(i + starty/2) + startx, src_pip_virt_addr + pip_frame_width * pip_frame_height + pip_frame_width * i * sizeof(unsigned char), pip_frame_width * sizeof(unsigned char));
    // }
        

    
    //memcpy(src_virt_addr, p_resize_virt_addr , 1920*1080*1.5);

    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
    //printf("sample_ive_mmz_free \n");
    //ENDCLOCK("------END RESIZE-------")
    
}
#endif


/// @brief acquire resize image of FHD, USE FOR PIP
/// @param frame_info 
/// @param src_virt_addr 
/// @param resize_phys_addr 
/// @param p_resize_virt_addr 
/// @param resize_width 
/// @param resize_height 
/// @return 
uint64_t acquire_fhd_resize_frame(vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, vs_uint64_t *resize_phys_addr, vs_void_t **p_resize_virt_addr, unsigned int resize_width, unsigned int resize_height)
{

    //STARTCLOCK
    int ret = 0;
    vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
    vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
    vs_uint32_t ive_dst_size;
    vs_ive_dma_cfg_s dma_cfg;
    vs_uint32_t handle = 0;
    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;


    /* image origin size */
    vs_uint32_t ive_image_origin_stride = frame_info->frame.stride[0];
    vs_uint32_t ive_image_origin_width = frame_info->frame.width;
    vs_uint32_t ive_image_origin_height = frame_info->frame.height;

    //start point
    vs_int32_t src_x_point = 0;
    vs_int32_t src_y_point = 0;

    vs_uint32_t resize_stride = resize_width;
    vs_ive_image_s ive_image_src;
    vs_ive_image_s ive_image_dst;
    vs_ive_resize_cfg_s resize_cfg;


    resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    if (resize_width < ive_image_origin_width)
    {
        resize_cfg.mode = E_IVE_RESIZE_MODE_AREA;
    }
    else{
        resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
    }
    
    resize_cfg.image_num = 1;
    ive_image_src.type = E_IVE_IMAGE_TYPE_YUV420SP;
    ive_image_src.stride[0] = ive_image_origin_stride;
    ive_image_src.stride[1] = ive_image_origin_stride;
    ive_image_src.width = ive_image_origin_width;
    ive_image_src.height = ive_image_origin_height;

    
    ive_image_dst.type = E_IVE_IMAGE_TYPE_YUV420SP;
    ive_image_dst.stride[0] = resize_stride;
    ive_image_dst.stride[1] = resize_stride;
    ive_image_dst.width = resize_width;
    ive_image_dst.height = resize_height;

    // vs_uint64_t resize_phys_addr = 0;
    // vs_void_t *p_resize_virt_addr = NULL;
    vs_uint32_t resize_size = resize_stride * resize_height * 1.5;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, resize_phys_addr, p_resize_virt_addr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
    }

    //phy addr mmp virt addr 
    ive_image_src.phys_addr[0] = frame_info->frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
    ive_image_src.virt_addr[0] = (vs_char_t *)frame_info->frame.virt_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;

    ive_image_src.phys_addr[1] = frame_info->frame.phys_addr[1] + src_y_point/2 * ive_image_origin_stride + src_x_point;
    ive_image_src.virt_addr[1] = src_virt_addr + ive_image_origin_stride * ive_image_origin_height  + src_y_point/2 * ive_image_origin_stride + src_x_point;


    ive_image_dst.phys_addr[0] = *resize_phys_addr;
    ive_image_dst.virt_addr[0] = (vs_void_t*)*p_resize_virt_addr;


    ive_image_dst.phys_addr[1] = *resize_phys_addr + resize_stride * resize_height;
    ive_image_dst.virt_addr[1] = (vs_void_t*)*p_resize_virt_addr + resize_stride * resize_height;
        
    printf("mode:%d resize_with:%d resize_height:%d resize_stride:%d src_x_point: %d src_y_point: %d  \n",
        resize_cfg.mode, resize_width, resize_height, resize_stride, src_x_point, src_y_point);

    ret = vs_mal_ive_resize(&handle, &ive_image_src, &ive_image_dst, &resize_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
    }


    // static int run_once = 1;
    // if (run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, resize_stride, resize_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }

    // vs_uint32_t copy_width = BEIHANG_PIP_WIDHT;
    // vs_uint32_t copy_height = BEIHANG_PIP_HEIGHT;


    // vs_uint32_t resize_src_x_point = resize_width/2 - BEIHANG_PIP_WIDHT/2;
    // vs_uint32_t resize_src_y_point = resize_height/2 - BEIHANG_PIP_HEIGHT/2;
    

    // printf("resize_src_x_point:%d resize_src_y_point:%d\n", resize_src_x_point, resize_src_y_point);
    // resize_src_x_point = resize_src_x_point % 2 == 0 ? resize_src_x_point : resize_src_x_point + 1; 
    


    // for (int i = 0; i < BEIHANG_PIP_HEIGHT; i++)
    // {
    //     //memcpy(src_virt_addr + ive_image_origin_stride * sizeof(unsigned char) * i, p_resize_virt_addr + BEIHANG_PIP_WIDHT * sizeof(unsigned char) * i, BEIHANG_PIP_WIDHT * sizeof(unsigned char));
    //     memcpy(src_virt_addr + (BEIHANG_PIP_Y_OFFSET + i) * ive_image_origin_stride + BEIHANG_PIP_X_OFFSET, p_resize_virt_addr + (resize_src_y_point + i) * resize_stride + resize_src_x_point, BEIHANG_PIP_WIDHT * sizeof(unsigned char));
    // }


    // for (int i = 0; i < BEIHANG_PIP_HEIGHT / 2; i++)
    // {
    //     memcpy(src_virt_addr + ive_image_origin_stride * ive_image_origin_height + (BEIHANG_PIP_Y_OFFSET/2 + i) * ive_image_origin_stride + BEIHANG_PIP_X_OFFSET, 
    //     p_resize_virt_addr + resize_stride * resize_height + (resize_src_y_point/2 + i) * resize_stride + resize_src_x_point, 
    //     BEIHANG_PIP_WIDHT * sizeof(unsigned char));
    // }

    

// exit_ive_mmz_free:  

//     //free mm
//     sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
//     //ENDCLOCK("ive copy and resize")

}



/// @brief acquire resize image of FHD, USE FOR PIP
/// @param frame_info 
/// @param src_virt_addr 
/// @param resize_phys_addr 
/// @param p_resize_virt_addr 
/// @param resize_width 
/// @param resize_height 
/// @return 
uint64_t release_fhd_resize_frame(vs_uint64_t resize_phys_addr, vs_void_t *p_resize_virt_addr, unsigned int resize_width, unsigned int resize_height)
{
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_width * resize_height * 1.5);
}





#if 0
uint64_t ive_rgb_zoom_4k_ex(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, vs_bool_t is_4k_input, int dx, int dy)

{

    

    //printf("p_camera->zoom_factor :%d\n",p_camera->zoom_factor);
    if (zoom_factor > 10 && zoom_factor <= 80)
    {
        //STARTCLOCK
        int ret = 0;
        vs_uint64_t phys_addr1 = 0, phys_dst_addr = 0;
        vs_void_t *p_virt_addr1 = NULL, *p_virt_dst_addr = NULL;
        vs_uint32_t ive_dst_size;
        vs_ive_dma_cfg_s dma_cfg;
        vs_uint32_t handle = 0;
        vs_bool_t block = VS_TRUE;
        vs_bool_t finish = VS_FALSE;

        /* resize factor, range 1.0 - 8.0 */
        vs_float_t ive_resize_factor = zoom_factor/10.0;

        /* image origin size */
        // vs_uint32_t ive_image_origin_stride = frame_info->frame.;
        // vs_uint32_t ive_image_origin_width = UVC_WIDTH;
        // vs_uint32_t ive_image_origin_height = UVC_HEIGHT;
        vs_uint32_t ive_image_origin_stride = frame_info->frame.stride[0];
        vs_uint32_t ive_image_origin_width = frame_info->frame.width;
        vs_uint32_t ive_image_origin_height = frame_info->frame.height;



        vs_uint32_t ive_image_dst_stride = ive_image_origin_stride / ive_resize_factor;
        vs_uint32_t ive_image_dst_width =  ive_image_origin_width / ive_resize_factor;
        vs_uint32_t ive_image_dst_height = ive_image_origin_height / ive_resize_factor;

        //vs_mal_ive_dma input width must be multiple of 32. 
        ive_image_dst_width = (ive_image_dst_width % 32) == 0 ? ive_image_dst_width : ive_image_dst_width + (32 - (ive_image_dst_width % 32));
        ive_image_dst_height = ive_image_dst_width * 0.8;// 0.8 scale
        ive_image_dst_height = ive_image_dst_height % 2 == 0 ? ive_image_dst_height : ive_image_dst_height + 1; 
        ive_image_dst_stride =  ive_image_dst_width;

        //start point
        vs_uint32_t src_x_point = ive_image_origin_width/2 - ive_image_dst_width/2;
        vs_uint32_t src_y_point = ive_image_origin_height/2 - ive_image_dst_height/2;

        // resize
        vs_uint64_t resize_phys_addr = 0;
        vs_void_t *p_resize_virt_addr = NULL;

        vs_uint32_t resize_with = ive_image_dst_width * ive_resize_factor;
        resize_with = (resize_with % 32) == 0 ? resize_with : resize_with + (32 - (resize_with % 32));
        vs_uint32_t resize_height = resize_with * 0.8;
        resize_height = resize_height % 2 == 0 ? resize_height : resize_height + 1; 
        vs_uint32_t resize_stride = resize_with;
        
        vs_ive_image_s ive_image_src;
        vs_ive_image_s ive_image_dst;
        vs_ive_resize_cfg_s resize_cfg;

        resize_cfg.mode = E_IVE_RESIZE_MODE_BILINEAR;
        resize_cfg.image_num = 1;
        ive_image_src.type = E_IVE_IMAGE_TYPE_U8C1;
        ive_image_src.stride[0] = ive_image_origin_stride;
        ive_image_src.stride[1] = ive_image_origin_stride;
        ive_image_src.width = ive_image_dst_width;
        ive_image_src.height = ive_image_dst_height;

        
        ive_image_dst.type = E_IVE_IMAGE_TYPE_U8C1;
        ive_image_dst.stride[0] = resize_stride;
        ive_image_dst.stride[1] = resize_stride;
        ive_image_dst.width = resize_with;
        ive_image_dst.height = resize_height;

        vs_uint32_t resize_size = resize_stride * resize_height;
        ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
            goto exit_ive_mmz_free;
        }

        //phy addr mmp virt addr 
        ive_image_src.phys_addr[0] = p_camera->buf[p_camera->buf_cur_index].frame_info.frame.phys_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;
        ive_image_src.virt_addr[0] = (vs_char_t *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0] + src_y_point * ive_image_origin_stride + src_x_point;

        ive_image_dst.phys_addr[0] = resize_phys_addr;
        ive_image_dst.virt_addr[0] = (vs_void_t*)p_resize_virt_addr;


        ret = vs_mal_ive_resize(&handle, &ive_image_src, &ive_image_dst, &resize_cfg, block);
        if(ret != VS_SUCCESS){
            vs_sample_trace("ive resize ret = 0x%x handle = 0x%x\n", ret, handle);
        }
        ret = vs_mal_ive_query(handle, &finish, block);
        if(ret != VS_SUCCESS){
            vs_sample_trace("ive resize query ret = 0x%x  finish = %d\n", ret, finish);
        }


        //copy again
        vs_uint32_t resize_src_x_point = resize_with/2 - ive_image_origin_width/2 ;
        vs_uint32_t resize_src_y_point = resize_height/2 -ive_image_origin_height/2;

        vs_ive_data_s ive_copy_src_data;
        ive_copy_src_data.stride = resize_stride;
        ive_copy_src_data.width = ive_image_origin_width;
        ive_copy_src_data.height = ive_image_origin_height;
        ive_copy_src_data.phys_addr = resize_phys_addr + resize_src_y_point * resize_stride + resize_src_x_point;
        ive_copy_src_data.virt_addr = (vs_void_t *)p_resize_virt_addr + resize_src_y_point * resize_stride + resize_src_x_point;


        vs_ive_data_s ive_copy_dst_data;
        ive_copy_dst_data.stride = ive_image_origin_stride;
        ive_copy_dst_data.width = ive_image_origin_width;
        ive_copy_dst_data.height = ive_image_origin_height;
        ive_copy_dst_data.phys_addr = p_camera->buf[p_camera->buf_cur_index].frame_info.frame.phys_addr[0];
        ive_copy_dst_data.virt_addr = (vs_void_t *)p_camera->buf[p_camera->buf_cur_index].frame_info.frame.virt_addr[0];

        dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


        ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
        if(ret != VS_SUCCESS){
            vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
        }
        ret = vs_mal_ive_query(handle, &finish, block);
        if(ret != VS_SUCCESS){
            vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
        }
        
exit_ive_mmz_free:

        //free mm
        sample_ive_mmz_free(phys_dst_addr, p_virt_dst_addr, ive_dst_size);
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);
        //ENDCLOCK("ive copy and resize")
    }
    


}
#endif




vs_int32_t sample_gpe_scale(float zoom_factor, vs_video_frame_info_s *frame_info, vs_void_t * src_virt_addr, int x_offset, int y_offset)
{

    //printf("zoom_factor: %f\n", zoom_factor);
    vs_int32_t ret = VS_SUCCESS;
    vs_uint32_t image_origin_stride = frame_info->frame.stride[0];
    vs_uint32_t image_origin_width = frame_info->frame.width;
    vs_uint32_t image_origin_height = frame_info->frame.height;

    vs_uint32_t image_scale_src_width = frame_info->frame.width/zoom_factor;
    //vs_uint32_t image_scale_src_height = frame_info->frame.height/zoom_factor;
    

    if (image_scale_src_width % 2 != 0)
    {
        image_scale_src_width += 1; 
    }
    if ((image_scale_src_width / 2) % 2 != 0)
    {
        image_scale_src_width += 2;
    }
    //image_scale_src_width = image_scale_src_width % 2 == 0 ? image_scale_src_width : image_scale_src_width + 1; 
    vs_uint32_t image_scale_src_height = image_scale_src_width * 0.5625;
    image_scale_src_height = image_scale_src_height % 2 == 0 ? image_scale_src_height : image_scale_src_height + 1;

    vs_int32_t job_handle = 0;
    ret = vs_mal_gpe_job_start(&job_handle);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_job_start ret[0x%x], job_handle: %d\n", ret, job_handle);
        return VS_FALSE;
    }

    //image_scale_src_width = 3490;
    //image_scale_src_height = 1980;

    //printf("image_scale_src_width:%d image_scale_src_height:%d\n", image_scale_src_width, image_scale_src_height);
    vs_task_attr_s* p_task_attr = (vs_task_attr_s*) malloc(sizeof(vs_task_attr_s));
    memset(p_task_attr, 0, sizeof(vs_task_attr_s));
    p_task_attr->src_frame.frame.width = image_scale_src_width;
    p_task_attr->src_frame.frame.height = image_scale_src_height;
    p_task_attr->src_frame.frame.pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    p_task_attr->src_frame.frame.stride[0] = image_origin_stride;
    p_task_attr->src_frame.frame.stride[1] = image_origin_stride;
    p_task_attr->src_frame.frame.video_format = E_VIDEO_FORMAT_LINEAR;

    p_task_attr->dst_frame.frame.width = 1920;
    p_task_attr->dst_frame.frame.height = 1080;
    p_task_attr->dst_frame.frame.pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    p_task_attr->dst_frame.frame.stride[0] = image_origin_stride;
    p_task_attr->dst_frame.frame.stride[1] = image_origin_stride;
    p_task_attr->dst_frame.frame.video_format = E_VIDEO_FORMAT_LINEAR;


    // resize
    vs_uint64_t resize_phys_addr = 0;
    vs_void_t *p_resize_virt_addr = NULL;

    vs_uint32_t resize_size = image_origin_stride * image_origin_height * 2;
    ret = sample_ive_mmz_malloc(NULL, (vs_char_t*)"resize_dst", resize_size, &resize_phys_addr, &p_resize_virt_addr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("ive resize sample_ive_mmz_malloc failed \n");
    }



    //copy
    vs_uint32_t start_x = image_origin_width/2 - image_scale_src_width/2 - x_offset;
    vs_uint32_t start_y = image_origin_height/2 - image_scale_src_height/2 - y_offset;
    
    start_x = start_x % 2 == 0 ? start_x : start_x + 1; 
    
    //vs_uint32_t resize_src_x_point = image_origin_stride/2 - venc_input_width/2 ;
    //vs_uint32_t resize_src_y_point = resize_height/2 -venc_input_height/2;

    vs_ive_dma_cfg_s dma_cfg;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;

    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = image_origin_stride;//resize_stride;
    ive_copy_src_data.width = image_scale_src_width;
    ive_copy_src_data.height = image_scale_src_height;
    ive_copy_src_data.phys_addr = frame_info->frame.phys_addr[0] + start_y * image_origin_stride + start_x;
    ive_copy_src_data.virt_addr = (vs_void_t *)src_virt_addr + start_y * image_origin_stride + start_x;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = image_origin_stride;
    ive_copy_dst_data.width = image_scale_src_width;
    ive_copy_dst_data.height = image_scale_src_height;
    ive_copy_dst_data.phys_addr = resize_phys_addr;
    ive_copy_dst_data.virt_addr = p_resize_virt_addr;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;

    vs_bool_t block = VS_TRUE;
    vs_bool_t finish = VS_FALSE;
    vs_uint32_t handle = 0;
    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("resize_stride:%d ive_image_origin_stride:%d ive_image_origin_width:%d ive_image_origin_height:%d\n",image_origin_stride, image_scale_src_width, image_origin_width, image_origin_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //printf("start_y/2 * image_origin_stride + start_x : %d  beili:%f\n", start_y/2 * image_origin_stride + start_x , (start_y/2 * image_origin_stride + start_x)/64.0);
    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = image_origin_stride;//resize_stride;
    ive_copy_src_data.width = image_scale_src_width ;
    ive_copy_src_data.height = image_scale_src_height/2;
    ive_copy_src_data.phys_addr = frame_info->frame.phys_addr[1] + start_y/2 * image_origin_stride + start_x;
    ive_copy_src_data.virt_addr = (vs_void_t*)src_virt_addr + image_origin_stride * image_origin_height + start_y/2 * image_origin_stride + start_x;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = image_origin_stride;
    ive_copy_dst_data.width = image_scale_src_width;
    ive_copy_dst_data.height = image_scale_src_height/2;
    ive_copy_dst_data.phys_addr = resize_phys_addr + image_scale_src_height * image_origin_stride;
    ive_copy_dst_data.virt_addr = p_resize_virt_addr + image_scale_src_height * image_origin_stride;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    // static int run_once = 1;
    // if (zoom_factor == 1.1 && run_once == 1)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(p_resize_virt_addr, image_origin_stride, image_scale_src_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }


    //vs_uint32_t start_x = image_origin_width/2 - image_scale_src_width/2;
    //vs_uint32_t start_y = image_origin_height/2 - image_scale_src_height/2;

    // p_task_attr->src_frame.frame.phys_addr[0] = frame_info->frame.phys_addr[0] + start_y * image_origin_stride + start_x;
    // p_task_attr->src_frame.frame.virt_addr[0] =  (vs_uint64_t)(src_virt_addr +  start_x + start_y * image_origin_stride);//src[0] + 200*sizeof(vs_uint8_t) * GPE_RESIZE_SRC_STRIDE;
   
    // printf("p_phys_addr: %p, %p\n", frame_info->frame.phys_addr[0], frame_info->frame.phys_addr[0] + start_y * image_origin_stride + start_x);
    // printf("p_virt_addr: %p, %p\n", src_virt_addr, src_virt_addr +  start_x + start_y * image_origin_stride);


    // p_task_attr->src_frame.frame.phys_addr[1] = frame_info->frame.phys_addr[1] + start_y/2 * image_origin_stride + start_x;
    // p_task_attr->src_frame.frame.virt_addr[1] =  (vs_uint64_t)(src_virt_addr + image_origin_stride * image_origin_height +  start_x + start_y/2 * image_origin_stride);//p_virt_addr[1] + 100*sizeof(vs_uint8_t) * GPE_RESIZE_SRC_STRIDE;
    p_task_attr->src_frame.frame.phys_addr[0] = resize_phys_addr;
    p_task_attr->src_frame.frame.virt_addr[0] =  (vs_uint64_t)p_resize_virt_addr;//src[0] + 200*sizeof(vs_uint8_t) * GPE_RESIZE_SRC_STRIDE;
   
    //printf("p_phys_addr: %p, %p\n", frame_info->frame.phys_addr[0], frame_info->frame.phys_addr[0] + start_y * image_origin_stride + start_x);
    //printf("p_virt_addr: %p, %p\n", src_virt_addr, src_virt_addr +  start_x + start_y * image_origin_stride);

    p_task_attr->src_frame.frame.phys_addr[1] = resize_phys_addr + image_scale_src_height * image_origin_stride;
    p_task_attr->src_frame.frame.virt_addr[1] =  (vs_uint64_t)(p_resize_virt_addr + image_scale_src_height * image_origin_stride);//p_virt_addr[1] + 100*sizeof(vs_uint8_t) * GPE_RESIZE_SRC_STRIDE;


    p_task_attr->dst_frame.frame.phys_addr[0] = frame_info->frame.phys_addr[0];
    p_task_attr->dst_frame.frame.virt_addr[0] = (vs_uint64_t)(src_virt_addr);
    p_task_attr->dst_frame.frame.phys_addr[1] = frame_info->frame.phys_addr[1];
    p_task_attr->dst_frame.frame.virt_addr[1] = (vs_uint64_t)(src_virt_addr + image_origin_stride * image_origin_height);

    vs_gpe_filter_type_e filter_type = E_GPE_FILTER_TYPE_3_TAP;
    
    //WALLTIMETRACKING
    ret = vs_mal_gpe_scale_task_add(job_handle, p_task_attr, filter_type);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_scale_task_add ret[0x%x], job_handle: %d\n", ret, job_handle);
        vs_mal_gpe_job_cancel(job_handle);

    }

    ret = vs_mal_gpe_job_finish(job_handle);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_job_finish ret[0x%x], job_handle: %d\n", ret, job_handle);
        vs_mal_gpe_job_cancel(job_handle);

    }


    //WALLTIMESTAT("gpe times cost: ")

    vs_uint32_t imgsize[3] = {p_task_attr->dst_frame.frame.stride[0] * p_task_attr->dst_frame.frame.height,
                              p_task_attr->dst_frame.frame.stride[1] * p_task_attr->dst_frame.frame.height / 2, 0};
    vs_void_t* p_virt_addr[3] = {(vs_void_t*) p_task_attr->dst_frame.frame.virt_addr[0],
                                 (vs_void_t*) p_task_attr->dst_frame.frame.virt_addr[1], VS_NULL};
    //save_yuv(SAMPLE_GPE_OUTPUT_SCALE, p_virt_addr, imgsize);
    //printf("width:%d,height:%d,stride[0]:%d,stride[1]:%d\n", p_task_attr->dst_frame.frame.width, p_task_attr->dst_frame.frame.height, p_task_attr->dst_frame.frame.stride[0], p_task_attr->dst_frame.frame.stride[1]);
    free(p_task_attr);

    frame_info->frame.stride[0] = image_origin_stride;
    frame_info->frame.stride[1] = image_origin_stride;
    frame_info->frame.stride[2] = image_origin_stride;
    frame_info->frame.width = 1920;
    frame_info->frame.height = 1080; 
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size);


    return ret;
}

