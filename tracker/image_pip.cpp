
#include "image_pip.h"


int get_pipinfo_with_image_mode(IMAGE_MODE_E mode, image_mode_pipinfo_s *pipeinfo)
{
    switch (mode)
    {

    //4K BACKGROUND
    case E_4K_FULL_SCREEN:
        pipeinfo->background.grp = 0;
        pipeinfo->background.chn = 3;
        pipeinfo->background.origin_width = 3840;
        pipeinfo->background.origin_height = 2160;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 0;
        break;

    case E_4K_PIP_1080P:
        pipeinfo->background.grp = 0;
        pipeinfo->background.chn = 3;
        pipeinfo->background.origin_width = 3840;
        pipeinfo->background.origin_height = 2160;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;

        pipeinfo->pipe_count = 1;
        pipeinfo->pip[0].grp = 1;
        pipeinfo->pip[0].chn = 3;
        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 360;
        pipeinfo->pip[0].origin_width = 1920;
        pipeinfo->pip[0].origin_height = 1080;
        pipeinfo->pip[0].startx = 0;
        pipeinfo->pip[0].starty = 0;
        break;

    case E_4K_PIP_INFRA:
        pipeinfo->background.grp = 0;
        pipeinfo->background.chn = 3;
        pipeinfo->background.origin_width = 3840;
        pipeinfo->background.origin_height = 2160;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;

        pipeinfo->pipe_count = 1;
        pipeinfo->pip[0].grp = 3;
        pipeinfo->pip[0].chn = 3;
        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 512;
        pipeinfo->pip[0].origin_width = 640;
        pipeinfo->pip[0].origin_height = 512;

        pipeinfo->pip[0].startx = 1280;
        pipeinfo->pip[0].starty = 568;
        // pipeinfo->pip[0].startx = 0;
        // pipeinfo->pip[0].starty = 0;
        break;
    
    case E_4K_PIP_1080P_INFRA:
        pipeinfo->background.grp = 0;
        pipeinfo->background.chn = 3;
        pipeinfo->background.origin_width = 3840;
        pipeinfo->background.origin_height = 2160;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;

        pipeinfo->pipe_count = 2;
        pipeinfo->pip[0].grp = 1;
        pipeinfo->pip[0].chn = 3;
        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 360;
        pipeinfo->pip[0].origin_width = 1920;
        pipeinfo->pip[0].origin_height = 1080;
        pipeinfo->pip[0].startx = 0;
        pipeinfo->pip[0].starty = 0;

        pipeinfo->pip[1].grp = 3;
        pipeinfo->pip[1].chn = 3;
        pipeinfo->pip[1].width = 640;
        pipeinfo->pip[1].height = 512;
        pipeinfo->pip[1].origin_width = 640;
        pipeinfo->pip[1].origin_height = 512;
        pipeinfo->pip[1].startx = 0;
        pipeinfo->pip[1].starty = 360;
        
        break;


    case E_1080P_FULL_SCREEN:
        pipeinfo->background.grp = 1;
        pipeinfo->background.chn = 3;
        pipeinfo->background.origin_width = 3840;
        pipeinfo->background.origin_height = 2160;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 0;
        break;

    case E_1080P_PIP_4K:
        pipeinfo->background.grp = 1;
        pipeinfo->background.chn = 3;
        pipeinfo->background.origin_width = 3840;
        pipeinfo->background.origin_height = 2160;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 1;
        pipeinfo->pip[0].grp = 0;
        pipeinfo->pip[0].chn = 3;
        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 360;
        pipeinfo->pip[0].origin_width = 1920;
        pipeinfo->pip[0].origin_height = 1080;
        pipeinfo->pip[0].startx = 0;
        pipeinfo->pip[0].starty = 0;
        break;        

    case E_1080P_PIP_INFRA:
        pipeinfo->background.grp = 1;
        pipeinfo->background.chn = 3;
        pipeinfo->background.origin_width = 3840;
        pipeinfo->background.origin_height = 2160;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 1;
        pipeinfo->pip[0].grp = 3;
        pipeinfo->pip[0].chn = 3;
        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 512;
        pipeinfo->pip[0].origin_width = 640;
        pipeinfo->pip[0].origin_height = 512;
        // pipeinfo->pip[0].startx = 0;
        // pipeinfo->pip[0].starty = 0;
        pipeinfo->pip[0].startx = 1280;
        pipeinfo->pip[0].starty = 568;
        break;
    
    case E_1080P_PIP_4K_INFRA:
        pipeinfo->background.grp = 1;
        pipeinfo->background.chn = 3;
        pipeinfo->background.origin_width = 3840;
        pipeinfo->background.origin_height = 2160;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 2;
        pipeinfo->pip[0].grp = 0;
        pipeinfo->pip[0].chn = 3;

        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 360;
        pipeinfo->pip[0].origin_width = 1920;
        pipeinfo->pip[0].origin_height = 1080;
        pipeinfo->pip[0].startx = 0;
        pipeinfo->pip[0].starty = 0;

        pipeinfo->pip[1].grp = 3;
        pipeinfo->pip[1].chn = 3;
        pipeinfo->pip[1].width = 640;
        pipeinfo->pip[1].height = 512;
        pipeinfo->pip[1].origin_width = 640;
        pipeinfo->pip[1].origin_height = 512;
        pipeinfo->pip[1].startx = 0;
        pipeinfo->pip[1].starty = 360;
        break;


    case E_INFRA_FULL_SCREEN:
        pipeinfo->background.grp = 3;
        pipeinfo->background.chn = 4;
        pipeinfo->background.origin_width = 1920;
        pipeinfo->background.origin_height = 1080;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 0;
        break;

    case E_INFRA_PIP_4K:
        pipeinfo->background.grp = 3;
        pipeinfo->background.chn = 4;
        pipeinfo->background.origin_width = 1920;
        pipeinfo->background.origin_height = 1080;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 1;
        pipeinfo->pip[0].grp = 0;
        pipeinfo->pip[0].chn = 3;
        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 360;
        pipeinfo->pip[0].origin_width = 1920;
        pipeinfo->pip[0].origin_height = 1080;  
        pipeinfo->pip[0].startx = 0;
        pipeinfo->pip[0].starty = 0; 
        break;        

    case E_INFRA_PIP_1080P:
        pipeinfo->background.grp = 3;
        pipeinfo->background.chn = 4;
        pipeinfo->background.origin_width = 1920;
        pipeinfo->background.origin_height = 1080;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 1;
        pipeinfo->pip[0].grp = 1;
        pipeinfo->pip[0].chn = 3;
        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 360;
        pipeinfo->pip[0].origin_width = 1920;
        pipeinfo->pip[0].origin_height = 1080;  
        pipeinfo->pip[0].startx = 0;
        pipeinfo->pip[0].starty = 0;
        break;
    
    case E_INFRA_PIP_4K_1080P:
        pipeinfo->background.grp = 3;
        pipeinfo->background.chn = 4;
        pipeinfo->background.origin_width = 1920;
        pipeinfo->background.origin_height = 1080;
        pipeinfo->background.width = 1920;
        pipeinfo->background.height = 1080;
        pipeinfo->pipe_count = 2;
        pipeinfo->pip[0].grp = 0;
        pipeinfo->pip[0].chn = 3;
        pipeinfo->pip[0].width = 640;
        pipeinfo->pip[0].height = 360;
        pipeinfo->pip[0].origin_width = 1920;
        pipeinfo->pip[0].origin_height = 1080;  
        pipeinfo->pip[0].startx = 0;
        pipeinfo->pip[0].starty = 0;

        pipeinfo->pip[1].grp = 1;
        pipeinfo->pip[1].chn = 3;
        pipeinfo->pip[1].width = 640;
        pipeinfo->pip[1].height = 360;
        pipeinfo->pip[1].origin_width = 1920;
        pipeinfo->pip[1].origin_height = 1080;  
        pipeinfo->pip[1].startx = 0;
        pipeinfo->pip[1].starty = 360;
        break;

    default:
        break;
    }
}





int accquire_pip_frame(vs_video_frame_info_s *vpp_frame, vs_void_t **src_virt_addr, int grp, int chn,  unsigned int frame_width, unsigned int frame_height)
{
    int ret = vs_mal_vpp_chn_frame_acquire(grp, chn, vpp_frame, VPP_ACQUIRE_TIMEOUT);
    if (ret != VS_SUCCESS)
    {
        vs_sample_trace("vpp_chn_frame_acquire failed with err 0x%x, group_id %d, chn_id %d\n", ret, grp, chn);
        usleep(10 * 1000);
        return VS_FAILED;
    }

    vs_uint64_t src_phys_addr;
    //vs_void_t *src_virt_addr = NULL;
    src_phys_addr = vpp_frame->frame.phys_addr[0];

#ifdef TRACKING_BUF_USE_CACHED_MEM
    *src_virt_addr = vs_mal_sys_mmap_cached(src_phys_addr, frame_width * frame_height * 3 / 2);
#else
    *src_virt_addr = vs_mal_sys_mmap(src_phys_addr, frame_width * frame_height * 3 / 2);
#endif // TRACKING_BUF_USE_CACHED_MEM
    if (*src_virt_addr == NULL)
    {
        vs_sample_trace("vs_mal_sys_mmap failed with err 0x%x\n", ret);
        return VS_FAILED;
    }
#ifdef TRACKING_BUF_USE_CACHED_MEM
    /* invalidate cached mmz, cache mmz must be invalidate before read */
    ret = vs_mal_sys_cache_invalidate(src_phys_addr, *src_virt_addr, frame_width * frame_height * 3 / 2);
    if (ret != VS_SUCCESS)
    {
        vs_sample_trace("vs_mal_sys_cache_invalidate failed with err 0x%x\n", ret);
    }
#endif // TRACKING_BUF_USE_CACHED_MEM

    // static int run_once = 1;
    // run_once ++;
    // if (run_once == 100)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out_tt.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(*src_virt_addr, frame_width, frame_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }

    return VS_SUCCESS;
}



void voerlay_frame_on_background(vs_void_t *src_background_virt_addr, 
                unsigned int background_frame_width, 
                unsigned int background_frame_height, 
                vs_void_t *src_pip_virt_addr,
                unsigned int pip_frame_width, unsigned int pip_frame_height, unsigned int startx, unsigned int starty)
{
    // for (int i = 0; i < pip_frame_height; i++)
    // {
    //     memcpy(src_background_virt_addr + background_frame_width * sizeof(unsigned char) * i, src_pip_virt_addr + pip_frame_width * sizeof(unsigned char) * i, pip_frame_width * sizeof(unsigned char));
    // }
    
    // PRINTLINE
    // for (int i = 0; i < pip_frame_height / 2; i++)
    // {
    //     memcpy(src_background_virt_addr + background_frame_width * background_frame_height * sizeof(unsigned char) + background_frame_width * sizeof(unsigned char) * i, src_pip_virt_addr + pip_frame_width * pip_frame_height + pip_frame_width * i * sizeof(unsigned char), pip_frame_width * sizeof(unsigned char));
    // }

    for (int i = 0; i < pip_frame_height; i++)
    {
        memcpy(src_background_virt_addr + background_frame_width * sizeof(unsigned char) * ( i + starty) + startx, src_pip_virt_addr + pip_frame_width * sizeof(unsigned char) * i, pip_frame_width * sizeof(unsigned char));
    }
    
    for (int i = 0; i < pip_frame_height / 2; i++)
    {
        memcpy(src_background_virt_addr + background_frame_width * background_frame_height * sizeof(unsigned char) + background_frame_width * sizeof(unsigned char) *(i + starty/2) + startx, src_pip_virt_addr + pip_frame_width * pip_frame_height + pip_frame_width * i * sizeof(unsigned char), pip_frame_width * sizeof(unsigned char));
    }

    // static int run_once = 1;
    // run_once ++;
    // if (run_once == 100)
    // {
    //     run_once = 0;
    //     FILE *p_fout = fopen("/tmp/ive_zoom_out_background.yuv", "wb");
    //     if (NULL == p_fout) {
    //         vs_sample_trace("failed to open output file\n");
    //         //goto exit1;
    //     }
    //     else{
    //         fwrite(src_background_virt_addr, background_frame_width, background_frame_height * 1.5, p_fout);
    //         fclose(p_fout);
    //     }
    // }
}


void voerlay_frame_on_background_ive(vs_void_t *src_background_virt_addr, 
                vs_uint64_t  background_phys_addr_0,
                vs_uint64_t  background_phys_addr_1,
                unsigned int background_frame_width, 
                unsigned int background_frame_height, 
                vs_void_t *src_pip_virt_addr,
                vs_uint64_t  pip_phys_addr,
                unsigned int pip_frame_width, unsigned int pip_frame_height, unsigned int startx, unsigned int starty)
{
    vs_bool_t finish = VS_FALSE;
    vs_bool_t block = VS_TRUE;
    vs_uint32_t handle = 0;
    vs_ive_dma_cfg_s dma_cfg;
    vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = pip_frame_width;//resize_stride;
    ive_copy_src_data.width = pip_frame_width;
    ive_copy_src_data.height = pip_frame_height;
    ive_copy_src_data.phys_addr = pip_phys_addr;
    ive_copy_src_data.virt_addr = (vs_void_t *)src_pip_virt_addr;


    vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = background_frame_width;
    ive_copy_dst_data.width = pip_frame_width;
    ive_copy_dst_data.height = pip_frame_height;
    ive_copy_dst_data.phys_addr = background_phys_addr_0 + background_frame_width  * ( starty) + startx;
    ive_copy_dst_data.virt_addr = src_background_virt_addr + background_frame_width * ( starty) + startx;
    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    int ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        printf("src_width:%d src_height:%d\n",pip_frame_width, pip_frame_height);
        vs_sample_trace("dma copy 1 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 1 query ret = 0x%x  finish = %d\n", ret, finish);
    }


    //vs_ive_data_s ive_copy_src_data;
    ive_copy_src_data.stride = pip_frame_width;//resize_stride;
    ive_copy_src_data.width = pip_frame_width ;
    ive_copy_src_data.height = pip_frame_height/2 ;
    ive_copy_src_data.phys_addr = pip_phys_addr + pip_frame_width * pip_frame_height;
    ive_copy_src_data.virt_addr = (vs_void_t*)src_pip_virt_addr + pip_frame_width * pip_frame_height;


    //vs_ive_data_s ive_copy_dst_data;
    ive_copy_dst_data.stride = background_frame_width;
    ive_copy_dst_data.width = pip_frame_width;
    ive_copy_dst_data.height = pip_frame_height/2;
    ive_copy_dst_data.phys_addr = background_phys_addr_1 + background_frame_width *(starty/2) + startx;
    ive_copy_dst_data.virt_addr = src_background_virt_addr + background_frame_width * background_frame_height + background_frame_width *(starty/2) + startx;

    dma_cfg.mode = E_IVE_DMA_MODE_DIRECT_COPY;//E_IVE_DMA_MODE_INTERVAL_COPY;


    ret = vs_mal_ive_dma(&handle, &ive_copy_src_data, &ive_copy_dst_data, &dma_cfg, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 ret = 0x%x handle = 0x%x\n", ret, handle);
    }
    ret = vs_mal_ive_query(handle, &finish, block);
    if(ret != VS_SUCCESS){
        vs_sample_trace("dma copy 2 query ret = 0x%x  finish = %d\n", ret, finish);
    }  
        
}


int release_pip_frame(vs_video_frame_info_s *vpp_pip_frame, vs_void_t *src_pip_virt_addr, int grp, int chn, unsigned int pip_frame_width, unsigned int pip_frame_height)
{
    vs_mal_sys_unmap(src_pip_virt_addr, pip_frame_height * pip_frame_width * 3 / 2);
    int ret = vs_mal_vpp_chn_frame_release(grp, chn, vpp_pip_frame);
    if (ret != VS_SUCCESS)
    {
        vs_sample_trace("vpp_chn_frame_release failed with err 0x%x, group_id %d, chn_id %d\n", ret, grp, chn);
    }
}



int background_pip_with_mode(IMAGE_MODE_E mode,vs_video_frame_info_s *background_frame, vs_void_t *background_virt_addr, bool is_record_infra)
{

    int ret = VS_SUCCESS;
    //vs_video_frame_info_s vpp_frame;
    //vs_void_t *src_virt_addr = NULL;
    image_mode_pipinfo_s pipeline;
    get_pipinfo_with_image_mode(mode, &pipeline);


    if (mode == E_4K_FULL_SCREEN || mode == E_1080P_FULL_SCREEN || mode == E_INFRA_FULL_SCREEN)
    {
        //do nothing
        //printf("do nothing\\n");
        return 0;
    }
    

    for (size_t i = 0; i < pipeline.pipe_count; i++)
    {

        //acquire frame
        ret = accquire_pip_frame(&pipeline.pip[i].vpp_frame, &pipeline.pip[i].src_virt_addr, pipeline.pip[i].grp, pipeline.pip[i].chn, pipeline.pip[i].origin_width, pipeline.pip[i].origin_height);
        if (ret != VS_SUCCESS)
        {
            // vs_sample_trace("vpp_chn_frame_acquire failed with err 0x%x, group_id %d, chn_id %d\n", ret, VII_VPP_GRP_ID, IR_VPP_CHN_ID);
            return 0;
        }

        // printf("origin_width:%d, origin_height:%d\n",pipeline.pip[i].origin_width, pipeline.pip[i].origin_height);
        // PRINTLINE
        // printf("width:%d, height:%d\n",pipeline.pip[i].width, pipeline.pip[i].height);
        // PRINTLINE
        //todo
        if(pipeline.pip[i].origin_width != pipeline.pip[i].width || pipeline.pip[i].origin_height != pipeline.pip[i].height)
        {
            //ZOOM IN
            vs_uint64_t resize_phys_addr = 0;
            vs_void_t *p_resize_virt_addr = NULL;
            // printf("p_resize_virt_addr 1: %p\n",p_resize_virt_addr);
            // printf("resize_phys_addr : %d\n",resize_phys_addr);
            // acquire_fhd_resize_frame(background_frame, 
            //                     background_virt_addr, 
            //                     &resize_phys_addr, 
            //                     &p_resize_virt_addr,
            //                     pipeline.pip[i].width, 
            //                     pipeline.pip[i].height);

            acquire_fhd_resize_frame(&pipeline.pip[i].vpp_frame, 
                                pipeline.pip[i].src_virt_addr, 
                                &resize_phys_addr, 
                                &p_resize_virt_addr,
                                pipeline.pip[i].width, 
                                pipeline.pip[i].height);                               

            // printf("p_resize_virt_addr 2: %p\n",p_resize_virt_addr);
            // printf("resize_phys_addr : %d\n",resize_phys_addr);

            // printf("background_virt_addr 3: %p\n",background_virt_addr);
            // printf("background_frame->frame.width: %d background_frame->frame.height:%d\n", background_frame->frame.width, background_frame->frame.height);
            // printf("pipeline.pip[i].width: %d pipeline.pip[i].height:%d\n", pipeline.pip[i].width, pipeline.pip[i].height);
            // printf("pipeline.pip[i].startx: %d pipeline.pip[i].starty:%d\n", pipeline.pip[i].startx, pipeline.pip[i].starty);
            //PIP
            // voerlay_frame_on_background(background_virt_addr, 
            //                             background_frame->frame.width, 
            //                             background_frame->frame.height, 
            //                             p_resize_virt_addr, 
            //                             pipeline.pip[i].width, 
            //                             pipeline.pip[i].height,
            //                             pipeline.pip[i].startx, 
            //                             pipeline.pip[i].starty);


            voerlay_frame_on_background_ive(background_virt_addr, background_frame->frame.phys_addr[0],
                background_frame->frame.phys_addr[1],
                                        background_frame->frame.stride[0], 
                                        background_frame->frame.height, 
                                        p_resize_virt_addr, 
                                        resize_phys_addr,
                                        pipeline.pip[i].width, 
                                        pipeline.pip[i].height,
                                        pipeline.pip[i].startx, 
                                        pipeline.pip[i].starty);
            
            //release
            release_fhd_resize_frame(resize_phys_addr, 
                                p_resize_virt_addr, 
                                pipeline.pip[i].width, 
                                pipeline.pip[i].height);

            
        }
        else
        {
            //PIP
            // voerlay_frame_on_background(background_virt_addr, 
            //                             background_frame->frame.width, 
            //                             background_frame->frame.height,
            //                             pipeline.pip[i].src_virt_addr, 
            //                             pipeline.pip[i].width, 
            //                             pipeline.pip[i].height,
            //                             pipeline.pip[i].startx, 
            //                             pipeline.pip[i].starty);
            voerlay_frame_on_background_ive(background_virt_addr, background_frame->frame.phys_addr[0],
                background_frame->frame.phys_addr[1],
                                        background_frame->frame.stride[0], 
                                        background_frame->frame.height, 
                                        pipeline.pip[i].src_virt_addr, 
                                        pipeline.pip[i].vpp_frame.frame.phys_addr[0],
                                        pipeline.pip[i].width, 
                                        pipeline.pip[i].height,
                                        pipeline.pip[i].startx, 
                                        pipeline.pip[i].starty);
            

        }
        
        //if in pip infra mode, send infra frame to venc
        /////////
        if (mode == E_4K_PIP_1080P_INFRA || mode == E_4K_PIP_INFRA || mode == E_1080P_PIP_4K_INFRA || mode == E_1080P_PIP_INFRA)
        {
            if (is_record_infra)
            {
                //send to venc
                ret = vs_mal_venc_frame_send(3, &pipeline.pip[i].vpp_frame, 0);
                if (ret != VS_SUCCESS)
                {
                    vs_sample_trace("vpp_chn_frame_send failed with err 0x%x\n", ret);
                }
            }
            
        }
        ////////


        //printf("pipeline.pip[i].src_virt_addr: %p  pipeline.pip[i].origin_width:%d pipeline.pip[i].origin_height:%d\n",pipeline.pip[i].src_virt_addr, pipeline.pip[i].origin_width, pipeline.pip[i].origin_height);
        //release
        release_pip_frame(&pipeline.pip[i].vpp_frame, 
                                pipeline.pip[i].src_virt_addr, 
                                pipeline.pip[i].grp, 
                                pipeline.pip[i].chn, 
                                pipeline.pip[i].origin_width, 
                                pipeline.pip[i].origin_height);
        
    }

    
}

