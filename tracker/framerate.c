#include "framerate.h"
#include "sample_common.h"

int set_vii_pipe_framerate(unsigned int vii_pipeid, unsigned int src_fps, unsigned int dst_fps)
{
    //vs_int32_t vii_pipeid;
    vs_vii_pipe_attr_s p_pipe_attr;
    vs_int32_t ret = vs_mal_vii_pipe_attr_get(vii_pipeid, &p_pipe_attr);
    if(ret != VS_SUCCESS){
        printf("failed to get vii pipe attr\n");
        return -1;
    }

    p_pipe_attr.framerate.src_framerate = src_fps;
    p_pipe_attr.framerate.dst_framerate = dst_fps;
    ret = vs_mal_vii_pipe_attr_set(vii_pipeid, &p_pipe_attr);
    if(ret != VS_SUCCESS){
        printf("failed to set vii pipe attr\n");
        return -1;
    }
    return 0;
}