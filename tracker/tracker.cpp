/*!
    @Description : https://github.com/shaoshengsong/
    <AUTHOR> shaoshengsong
    @Date        : 2022-09-23 02:52:22
*/
#include <fstream>
#include <sstream>
#include <iostream>
#include <opencv2/imgproc.hpp>
#include <opencv2/opencv.hpp>
#include "internal_defs.h"
#ifdef TRACKER_ENABLED
#ifdef AIRVISEN_RGN_ENABLED
#include "rgn.h"
#endif

extern int b_drawTrackingBox;
#define clamp(x, a, b) x = min(max(x, a), b)
#endif

#include "tracker.h"
#include <stdint.h>
#include <cstdint>
#include <stdlib.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <signal.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <errno.h>
#include <dlfcn.h>
#include <pthread.h>
#include <linux/videodev2.h>
#include <sys/socket.h>
#include <arpa/inet.h>

#include "sample_common.h"
#include "sample_common_infra.h"
#include "configparser.h"
#include "nn.h"
#include "denoise.h"
#include <opencv2/highgui/highgui_c.h>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include "image_file_util.h"
#include "algorithm_message.h"
#include <sys/prctl.h>
#include "store_jpeg_message.h"
#include "video_record_message.h"
#include "video_zoom_message.h"
#include "ive_zoom.h"
#include "zoom.h"
#include "heq_rgn.h"
#include "config_tool.h"
#include "image_pip.h"
#include "dronepod_k40t_protocol.h"
#include "dronepod_k40t_message.h"
#include "ir_ctrl_message.h"
#include "color_table.h"
#include "snap_photo.h"
#include "gimbal_protocol.h"
#include "report_message.h"
#define LOG_LEVEL LOG_LEVEL_DEBUG
#include "log_utils.h"

using namespace cv;
using namespace std;

extern int detectenable;
extern int npuenable;
extern int g_uppercommtype;
extern float g_tracksampledivisor;
extern float g_long_lens_tracksampledivisor;
extern char g_strSerialName[256];

extern int g_bTrackingIpcExit;
extern int g_tracker_state;
extern sample_nn_post_result_s g_track_results;

bool g_bTrackingExit = false;
bool g_gpio_trigger_track = false;
bool g_gpio_long_press_trigger = false;
bool g_gpio_short_press_trigger = false;
bool g_current_track_state = false;


static int g_protocol_type = HEQ_PROTOCOL_TYPE;

static int is_4k_record = 0;
static int is_1080P_record = 0;
static int is_infra_record = 0;

#define GPIO_TRIGGER_STATE '0'
#define GPIO_NORMAL_STATE '1'

//The current input source of the algorithm
int algo_trakcer_source = -1;

typedef enum TrackingStates
{
    NORMAL_STATE = 0,
    DETECT_STATE = 1,
    // TRACK_STATE = 2
    START_TRACK_STATE,
    STOP_TRACK_STATE,
    TRACKING_STATE,
} TRACKING_STATE_T;



TRACKING_STATE_T g_TrackingState = NORMAL_STATE;
pthread_mutex_t tracking_mutex;
Point originalPoint; // 矩形框起点
Point processPoint;  // 矩形框终点
Rect box;
Rect box2; // 目标框信息


//0 visible light full screen, 1  ir full screen, 2 visible light pip, 3  ir pip visible

// current background sensor , 0 short focal, 1 long focal, 2 ir 
//int display_sensor_type;

//< ret: 0, success; -1, no detected obj rect contains the x, y set from serial
int parse_detect_result(sample_nn_post_result_s *p_post_ret, cv::Rect *trackingBox)
{
#ifdef CVBS_VO_ENABLED
#define RGB_WIDTH CVBS_WIDTH
#define RGB_HEIGHT CVBS_HEIGHT
#endif
    vs_point_s p0 = {0};
    vs_int32_t box_w = 0, box_h = 0;
    // float scale_ratio_w = (float)RGB_WIDTH / NN_CHN_WIDTH;
    // float scale_ratio_h = (float)RGB_HEIGHT / NN_CHN_HEIGHT;
    float scale_ratio_w = 1.0;
    float scale_ratio_h = 1.0;

    float nearestDist = 1000000000.f;
    int nearestRectIndex = -1;

    sample_nn_detection_result_s *p_result_objs = &p_post_ret->detection;

    for (int i = 0; i < p_result_objs->obj_num; i++)
    {
        box_w = p_result_objs->objs[i].w * scale_ratio_w;
        box_h = p_result_objs->objs[i].h * scale_ratio_h;
        p0.x = p_result_objs->objs[i].x * scale_ratio_w;
        p0.y = p_result_objs->objs[i].y * scale_ratio_h;

        if (p0.x < 0)
        {
            p0.x = 0;
        }
        if (p0.y < 0)
        {
            p0.y = 0;
        }
        if (p0.y + box_h >= RGB_HEIGHT)
        {
            box_h = RGB_HEIGHT - p0.y - 1;
        }
        if (p0.x + box_w >= RGB_WIDTH)
        {
            box_w = RGB_WIDTH - p0.x - 1;
        }
        cv::Point center(trackingBox->x + trackingBox->width / 2, trackingBox->y + trackingBox->height / 2);
#ifdef GPIO_TRIGGER_MODE
        // find the nearest rect
        float dist = pow(((p0.x + box_w / 2) - center.x), 2) + pow((p0.y + box_h / 2) - center.y, 2);
        if (dist < nearestDist)
        {
            nearestDist = dist;
            nearestRectIndex = i;
        }

#else  // GPIO_TRIGGER_MODE
        if ((center.x > p0.x) && (center.y > p0.y) && (center.x < box_w + p0.x) && (center.y < box_h + p0.y))
        {
            float dist = pow((center.x - (p0.x + box_w / 2)), 2) + pow(center.y - (p0.y + box_h / 2), 2);
            if (dist < nearestDist)
            {
                nearestDist = dist;
                nearestRectIndex = i;
            }
        }
#endif // GPIO_TRIGGER_MODE
    }
    if (nearestRectIndex != -1)
    {
        trackingBox->x = p_result_objs->objs[nearestRectIndex].x * scale_ratio_w;
        trackingBox->y = p_result_objs->objs[nearestRectIndex].y * scale_ratio_h;
        trackingBox->width = p_result_objs->objs[nearestRectIndex].w * scale_ratio_w;
        trackingBox->height = p_result_objs->objs[nearestRectIndex].h * scale_ratio_h;

        printf("trackingBox->width:%d, trackingBox->height:%d\n", trackingBox->width, trackingBox->height);
        return 0;
    }

    return -1;
}


short videoMode = 0;

static cv::Rect g_SendRect;
static clock_t pre_frame_clock_t = 0, current_frame_clock_t;

static sample_nn_post_result_s nn_post_results, nn_post_results_draw;
airvisen_track_message_tracking_result_s nn_post_track_ipc;
detection_result_ipc_s  nn_post_detect_ipc[3] = {0};
short show_detect_in_track = 0;

bool is_video_record = false;

unsigned int video_record_frame_count = 0;

int video_zoom_factor_arr_index = 10;

trackState tracker_state = TRACKER_LOST;

// return ns from system running
uint64_t get_sys_uptime_in_ns(void)
{
    struct timespec tp;
    clock_gettime(CLOCK_MONOTONIC, &tp);
    return (tp.tv_sec * 1000000000ULL + tp.tv_nsec);
}

void *tracking_comm_proc(void *args)
{
    char proc_name[32] = "TrackingCommProc";
    prctl(PR_SET_NAME, proc_name, 0, 0, 0);

    int ret = -1;
    int client_sock = -1;

    cv::Rect showRect;


    static unsigned long g_SendCount = 0;

    extern char *g_strConfigIni;
    float d_send_time = 33.3;
    int FPS = GetIniKeyInt("serial", "sendfps", g_strConfigIni);
    if (FPS != 0)
        d_send_time = 1000.0 / (float)FPS;
    airvisen_trace("delta send time: %f\n", d_send_time);

    while (!g_bTrackingExit)
    {
        // printf("time(ns):%ld, command send count: %ld\n", get_sys_uptime_in_ns(), g_SendCount);
        // usleep(3000);
        //< send nn result out from serial/net

        static double total_t = 0;
        current_frame_clock_t = clock();
        total_t = (double)(current_frame_clock_t - pre_frame_clock_t) / CLOCKS_PER_SEC * 1000;

        if (total_t < d_send_time)
        {
            int sleep_us = int((d_send_time - total_t) * 1000);
            // printf("will sleep %d us\n", sleep_us);
            usleep(sleep_us);
        }
        pre_frame_clock_t = clock();

        g_SendCount++;

        unsigned char out_buffer[16];
        out_buffer[0] = 0x91;          // 消息头1
        out_buffer[1] = 0x0D;          // 帧长
        out_buffer[2] = 0x3f;          // tracking enable
        out_buffer[3] = tracker_state; // status 1.tracking, 2. lost & re-tracking 3. losted

        if (tracker_state == TRACKER_LOST)
        {
            out_buffer[4] = 960 & 0xFF;
            out_buffer[5] = (960 >> 8) & 0xFF;
            out_buffer[6] = 540 & 0xFF;
            ;
            out_buffer[7] = (540 >> 8) & 0xFF;

            out_buffer[8] = 0;  // w
            out_buffer[9] = 0;  // w
            out_buffer[10] = 0; // h
            out_buffer[11] = 0; // h



            //todo
            //StatusFrame_Box_Select_Track_0x000324_t track_data = {0};
            //track_data.detect_status
            //handle_state_ai_tracking(); 
            //camera_track_result(2, 0 , 0, 0, 0);
        }
        else
        {
            float divisor = g_tracksampledivisor;
            if (algo_trakcer_source == 1)
            {
                divisor = g_long_lens_tracksampledivisor;
            }
            
            cv::Rect tempRect = g_SendRect;
            tempRect.x = (tempRect.x - 960) * divisor + 960;
            tempRect.y = (tempRect.y - 540) * divisor + 540;
            tempRect.width = tempRect.width * divisor;
            tempRect.height = tempRect.height * divisor;

            out_buffer[4] = tempRect.x & 0xFF;        // x
            out_buffer[5] = (tempRect.x >> 8) & 0xFF; // x
            out_buffer[6] = tempRect.y & 0xFF;        // y
            out_buffer[7] = (tempRect.y >> 8) & 0xFF; // y

            out_buffer[8] = tempRect.width & 0xFF;          // w
            out_buffer[9] = (tempRect.width >> 8) & 0xFF;   // w
            out_buffer[10] = tempRect.height & 0xFF;        // h
            out_buffer[11] = (tempRect.height >> 8) & 0xFF; // h

            //todo
            //handle_state_ai_tracking(); 
            //camera_track_result(1, tempRect.x, tempRect.y, tempRect.width, tempRect.height);
        }

        out_buffer[12] = 0x01; // crc8

    }

}

int rect_short2long(cv::Rect& input_rect)
{
    airvisen_trace("short to long\n");
    input_rect.x = DUAL_LIGHT_SWITCH_FACTOR * (input_rect.x - PROTOCOL_WIDTH / 2) + PROTOCOL_WIDTH / 2 - get_long_focal_lens_x_offset() * DUAL_LIGHT_LONG_LENS_BASE_FACTOR;
    input_rect.x = (input_rect.x - PROTOCOL_WIDTH / 2) / DUAL_LIGHT_LONG_LENS_BASE_FACTOR + PROTOCOL_WIDTH / 2;
    input_rect.y = DUAL_LIGHT_SWITCH_FACTOR * (input_rect.y - PROTOCOL_HEIGHT / 2) + PROTOCOL_HEIGHT / 2 - get_long_focal_lens_y_offset() * DUAL_LIGHT_LONG_LENS_BASE_FACTOR;
    input_rect.y = (input_rect.y - PROTOCOL_HEIGHT / 2) / DUAL_LIGHT_LONG_LENS_BASE_FACTOR + PROTOCOL_HEIGHT / 2;
    input_rect.width = DUAL_LIGHT_SWITCH_FACTOR * input_rect.width / DUAL_LIGHT_LONG_LENS_BASE_FACTOR;
    input_rect.height = DUAL_LIGHT_SWITCH_FACTOR * input_rect.height / DUAL_LIGHT_LONG_LENS_BASE_FACTOR;
    return 0;
}
int rect_long2short(cv::Rect& input_rect)
{
    airvisen_trace("long to short\n");
    input_rect.x = DUAL_LIGHT_LONG_LENS_BASE_FACTOR * (input_rect.x - PROTOCOL_WIDTH / 2) + PROTOCOL_WIDTH / 2 + get_long_focal_lens_x_offset() * DUAL_LIGHT_LONG_LENS_BASE_FACTOR;
    input_rect.x = (input_rect.x - PROTOCOL_WIDTH / 2) / DUAL_LIGHT_SWITCH_FACTOR + PROTOCOL_WIDTH / 2;
    input_rect.y = DUAL_LIGHT_LONG_LENS_BASE_FACTOR * (input_rect.y - PROTOCOL_HEIGHT / 2) + PROTOCOL_HEIGHT / 2 + get_long_focal_lens_y_offset() * DUAL_LIGHT_LONG_LENS_BASE_FACTOR;
    input_rect.y = (input_rect.y - PROTOCOL_HEIGHT / 2) / DUAL_LIGHT_SWITCH_FACTOR + PROTOCOL_HEIGHT / 2;
    input_rect.width = DUAL_LIGHT_LONG_LENS_BASE_FACTOR * input_rect.width / DUAL_LIGHT_SWITCH_FACTOR;
    input_rect.height = DUAL_LIGHT_LONG_LENS_BASE_FACTOR * input_rect.height / DUAL_LIGHT_SWITCH_FACTOR;
    return 0;
}


#if 0
vs_int32_t uvc_framebuf_vb_acquire(vs_void_t *p_camera_info)
{
    vs_video_frame_info_s p_frame_info = {0};
    //vs_uvc_camera_info_s *p_camera = p_camera_info;
    vs_video_frame_s *p_frame = &p_frame_info.frame;;//&p_camera->buf[p_camera->buf_cur_index].frame_info.frame;
    vs_uint32_t blk_size = p_camera->buf_max_size;
    vs_uint32_t luma_stride = 0, chroma_stride = 0;
    VB_BLK vb_blk = VS_NULL;

    p_frame_info.modid = E_MOD_ID_USER;
    p_frame_info.poolid = p_camera->buf_poolid;
    p_frame->width = p_camera->width;
    p_frame->height = p_camera->height;
    p_frame->pixel_format = E_PIXEL_FORMAT_YUV_420SP;//uvc_framebuf_get_pixel_format_by_v4l2_fix_fmt(p_camera->pixelformat);

    vb_blk = VS_INVALID_VB_HANDLE;
    if (VS_INVALID_VB_HANDLE == (vb_blk = vs_mal_vb_block_get(p_camera->buf_poolid, blk_size, VS_NULL))) {
        printf("get blk_size[%u] of block pool[%u] failed.\n", blk_size, p_camera->buf_poolid);
        return VS_FAILED;
    }

    if (0 == (p_frame->phys_addr[0] = vs_mal_vb_handle2physaddr(vb_blk))) {
        printf("get phys of block[%u] failed.\n", vb_blk);
        vs_mal_vb_block_release(vb_blk);
        return VS_FAILED;
    }
//    VS_UVC_LOG_I("get phys[%llu][%llx] of block[%u] ok.\n", p_frame->phys_addr[0], p_frame->phys_addr[0], blk);
    if (0 == (p_frame->virt_addr[0] = (vs_uint64_t)vs_mal_sys_mmap(p_frame->phys_addr[0], blk_size))) {
        printf("get map phys[%llu] blk_size[%u] to virt failed.\n", p_frame->phys_addr[0], blk_size);
        vs_mal_vb_block_release(vb_blk);
        return VS_FAILED;
    }
    // uvc_get_aligned_byte_stride(p_camera->width, p_frame->pixel_format, &luma_stride, &chroma_stride, 64);
    // p_frame->stride[0] = luma_stride;
    // p_frame->stride[1] = 0;
    // p_frame->stride[2] = 0;
    // p_frame->virt_addr[1] = 0;
    // p_frame->virt_addr[2] = 0;
    // p_frame->phys_addr[1] = 0;
    // p_frame->phys_addr[2] = 0;
    // p_frame->compress_header_virt_addr[0] = 0;
    // p_frame->compress_header_virt_addr[1] = 0;
    // p_frame->compress_header_virt_addr[2] = 0;
    // p_frame->compress_header_phys_addr[0] = 0;
    // p_frame->compress_header_phys_addr[1] = 0;
    // p_frame->compress_header_phys_addr[2] = 0;
    // g_frame_id += 2;
    // g_frame_pts += 1000 / p_camera->fps;
    // p_frame->pts += g_frame_pts;
    // p_frame->id += g_frame_id;


    // p_frame->virt_addr[1] = p_frame->virt_addr[0] + luma_stride * p_camera->height;
    // p_frame->virt_addr[2] = p_frame->virt_addr[0] + luma_stride * p_camera->height;
    // p_frame->phys_addr[1] = p_frame->phys_addr[0] + luma_stride * p_camera->height;
    // p_frame->phys_addr[2] = p_frame->phys_addr[0] + luma_stride * p_camera->height;
    // p_frame->stride[1] = chroma_stride;
    // p_frame->stride[2] = chroma_stride;
    

    return VS_SUCCESS;
}
#endif

void *tracking_proc(void *args)
{
    char proc_name[32] = "TrackingProc";
    prctl(PR_SET_NAME, proc_name, 0, 0, 0);
    // PRINTLINE
    sample_nn_producer_info_s *p_producer_info = (sample_nn_producer_info_s *)args;
    vs_sample_trace("args：sensor_id=%d vii_pipeid=%d vii_chnid=%d vpp_grpid=%d vpp_chnid_vo=%d vpp_chnid_nn=%d\n",
                    p_producer_info->sensor_id, p_producer_info->vii_pipeid, p_producer_info->vii_chnid,
                    p_producer_info->vpp_grpid, p_producer_info->vpp_chnid_vo, p_producer_info->vpp_chnid_nn);

    static sample_nn_post_result_s tracking_results;
    short videoMode = 0;

    // PRINTLINE
    p_producer_info->vpp_grpid = VII_VPP_GRP_ID;
    p_producer_info->vpp_chnid_vo = IR_VPP_CHN_ID;
    p_producer_info->vo_chnid = IR_VO_CHN_ID;

    // PRINTLINE
    vs_sample_trace("tracking_proc--> nn--detection enabled.\n");

    cv::Rect showRect;
    //vs_video_frame_info_s background_vpp_frame = {0};
#ifdef SOFT_PIP_ENABLED
    vs_video_frame_info_s vpp_ir_frame = {0};
#endif
    static const cv::Scalar color[3] = {cv::Scalar(0, 0, 255), cv::Scalar(0, 255, 0), cv::Scalar(0, 0, 255)};

    bool HOG = true;          // true
    bool FIXEDWINDOW = false; // false
    bool MULTISCALE = true;   // true
    bool SILENT = true;
    bool LAB = false;
    bool is_show_video_record_bmp = false;

    bool frame_generate = false;
    int count = 1;
    int ret = -1;
    int client_sock = -1;
    int last_algo_tracker_source = -1;
    int display_sensor_type;

    cv::Rect initRect;
    originalPoint.x = 0;
    originalPoint.y = 0;
    processPoint.x = 0;
    processPoint.y = 0;

    vs_bool_t is_4k_input = VS_FALSE; 
    char ipc_detection_data[4096];
    char ipc_tracking_data[256];

    //airvisen_send_camera_mode(display_sensor_type);

    while (!g_bTrackingExit)
    {
        // airvisen_trace("....%d\n", g_bTrackingExit);
        // usleep(3000);
        // video record frame count, reset when start video record.
#if 0
        if (is_video_record == true && video_record_frame_count % 30 == 0)
        {
            is_show_video_record_bmp = !is_show_video_record_bmp;
#ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
            airvisen_draw_tracker_video_record_second(video_record_frame_count / 30);
#endif
            // airvisen_video_record_overlay_display_on_off(is_show_video_record_bmp);

            // recv notify message from rtsprec, if video record stop auto or unexpected halt, hide osd(video record).
            char ipc_data[256] = {0};
            int command = airvisen_recv_video_record_message(ipc_data, 256, 1);
            if (command == VIDEO_RECORD_STOP_EVENT)
            {
                vs_sample_trace("recv video record stop event\n");
#ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
                airvisen_video_record_overlay_display_on_off(false);
                airvisen_draw_tracker_video_record_second(0);
#endif
                is_video_record = false;
            }
        }
#endif

        display_sensor_type = get_sensor_type();
        //FPS_MEASURE(proc_name, 5)
        
        //int grp_id = VIS_VPP_GRP_ID;
        //int chn_id = VIS_VPP_CHN_ID;
        // get frame

        //IMAGE_MODE_E mode = E_1080P_PIP_4K_INFRA;//E_4K_PIP_1080P_INFRA;
        
        //PRINTLINE
        image_mode_pipinfo_s pipelineinfo = {0};
        get_pipinfo_with_image_mode(get_pip_mode(), &pipelineinfo);
        ret = vs_mal_vpp_chn_frame_acquire(pipelineinfo.background.grp, pipelineinfo.background.chn, &pipelineinfo.background.vpp_frame, VPP_ACQUIRE_TIMEOUT);
        if (ret != VS_SUCCESS)
        {
            // vs_sample_trace("vpp_chn_frame_acquire failed with err 0x%x, group_id %d, chn_id %d\n", ret, VII_VPP_GRP_ID, IR_VPP_CHN_ID);
            usleep(10 * 1000);
            continue;
        }
        //printf("pipelineinfo.background.vpp_frame.frame.id : %d\n", pipelineinfo.background.vpp_frame.frame.id);

        video_record_frame_count++;

        if(video_record_frame_count % 1 == 0)
        {
            continuous_zoom();
        }

        if(get_refresh_status()){
            float factor = get_zoom_factor();
                update_zoom_factor(factor);
            #ifdef DRAW_RGB_ZOOM_FACTOR
                char tempBuf[256] = {0};
                sprintf(tempBuf, "ZOOM:%1.1fX", factor);
            #ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
                airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 3, tempBuf);
            #endif
            #endif

            update_vo_mode();
        }

        //PRINTLINE
        vs_uint64_t src_phys_addr;
        //vs_void_t *src_virt_addr = NULL;
        src_phys_addr = pipelineinfo.background.vpp_frame.frame.phys_addr[0];

        //PRINTLINE
        // printf("pipelineinfo.background.vpp_frame.width: %d pipelineinfo.background.vpp_frame.frame.height: %d\n", pipelineinfo.background.vpp_frame.frame.width, pipelineinfo.background.vpp_frame.frame.height);
        // printf("pipelineinfo.background.vpp_frame.frame.height: %d pipelineinfo.background.vpp_frame.frame.width: %d\n",pipelineinfo.background.vpp_frame.frame.height, pipelineinfo.background.vpp_frame.frame.width);
#ifdef TRACKING_BUF_USE_CACHED_MEM
        //src_virt_addr = vs_mal_sys_mmap_cached(src_phys_addr, RGB_HEIGHT * RGB_WIDTH * 3 / 2);
        pipelineinfo.background.src_virt_addr = vs_mal_sys_mmap_cached(src_phys_addr, pipelineinfo.background.vpp_frame.frame.height * pipelineinfo.background.vpp_frame.frame.width * 3 / 2);
#else
        pipelineinfo.background.src_virt_addr = vs_mal_sys_mmap(src_phys_addr, pipelineinfo.background.vpp_frame.frame.height * pipelineinfo.background.vpp_frame.frame.width * 3 / 2);
#endif
        static auto draw_detect = [&]() -> int
        {
            // //PRINTLINE
            // draw postproc res
            p_producer_info->scale_ratio_nn2vo_w = (vs_float_t)pipelineinfo.background.vpp_frame.frame.width / get_nn_chn_width();
            p_producer_info->scale_ratio_nn2vo_h = (vs_float_t)pipelineinfo.background.vpp_frame.frame.height / get_nn_chn_height();
            // PRINTLINE
            if (p_producer_info->nn_info.nb_info.model_type == E_NN_MODE_DETECTION)
            {

#ifdef UVC_PIC_SEQUENCE
                ret = nn_gpe_frame_boxes_draw(&pipelineinfo.background.vpp_frame, p_producer_info->scale_ratio_nn2vo_w, p_producer_info->scale_ratio_nn2vo_h, &nn_post_results_draw);
#else
                // ret = nn_gpe_frame_boxes_draw(&pipelineinfo.background.vpp_frame, p_producer_info->scale_ratio_nn2vo_w, p_producer_info->scale_ratio_nn2vo_h, &nn_post_results);
                ret = nn_gpe_tracking_boxes_draw(&pipelineinfo.background.vpp_frame, 1.0f, 1.0f, &nn_post_results_draw);
#endif
                if (ret != VS_SUCCESS)
                {
                    vs_sample_trace("gpe_draw_boxes_toframe failed with err 0x%x\n", ret);
                    return -1;
                }
            }
            else
            {
                vs_sample_trace("p_producer_info->nn_info.nb_info.model_type != E_NN_MODE_DETECTION\n");
            }
            return 0;
        };

        switch (g_TrackingState)
        {
        case NORMAL_STATE:
            // break; //< @Aaron, deliberate go to DETECT_STATE
        case DETECT_STATE:
            if ((npuenable == 1) && (detectenable == 1))
            {
                // ret = sample_common_nn_result_get(&p_producer_info->nn_info, &nn_post_results);
                // ret = airvisen_recv_detection_result(ipc_detection_data, 4096, 1, &nn_post_results);
                //TODO NOTICE!!! SWITCH DETECTION SOURCE;
                // while(nn_post_detect_ipc.frame_id < pipelineinfo.background.vpp_frame.frame.id)
                // {
                //     airvisen_detector_update_result(&nn_post_detect_ipc);
                //     //printf("nn_post_detect_ipc.frame_id: %d, pipelineinfo.background.vpp_frame.frame.id:%d \n",nn_post_detect_ipc.frame_id,pipelineinfo.background.vpp_frame.frame.id );
                //     usleep(500);
                // }
                                // while(nn_post_detect_ipc.frame_id < pipelineinfo.background.vpp_frame.frame.id)
                // {
                //     airvisen_detector_update_result(&nn_post_detect_ipc);
                //     //printf("nn_post_detect_ipc.frame_id: %d, pipelineinfo.background.vpp_frame.frame.id:%d \n",nn_post_detect_ipc.frame_id,pipelineinfo.background.vpp_frame.frame.id );
                //     usleep(500);
                // }
                //PRINTLINE
                struct timespec start, current;
                clock_gettime(CLOCK_MONOTONIC, &start); // 记录循环开始时间

                //printf("nn_post_detect_ipc[display_sensor_type].frame_id: %u pipelineinfo.background.vpp_frame.frame.id:%u\n",nn_post_detect_ipc[display_sensor_type].frame_id, pipelineinfo.background.vpp_frame.frame.id);
                while (nn_post_detect_ipc[display_sensor_type].frame_id < pipelineinfo.background.vpp_frame.frame.id) 
                {
                    //PRINTLINE("waiting for nn result");
                    detection_result_ipc_s tmp_result = {0};
                    airvisen_detector_update_result(&tmp_result);
                    memcpy(&nn_post_detect_ipc[tmp_result.cam_source], &tmp_result, sizeof(detection_result_ipc_s));
                    
                    // 计算已耗时
                    clock_gettime(CLOCK_MONOTONIC, &current);
                    long long elapsed_ns = (current.tv_sec - start.tv_sec) * 1000000000LL + 
                                        (current.tv_nsec - start.tv_nsec);
                    
                    // 超过5ms则超时退出（5ms = 5,000,000ns）
                    if (elapsed_ns > 2 * 1000000) {
                        break;
                    }
                    
                    // 将剩余时间转换为微秒，并取最小值防止过度等待
                    long long remaining_us = (2 * 1000000 - elapsed_ns) / 1000;
                    usleep(remaining_us > 1000 ? 1000 : (useconds_t)remaining_us);
                }

                //printf("nn_post_detect_ipc.frame_id: %d pipelineinfo.background.vpp_frame.frame.id:%d\n",nn_post_detect_ipc.frame_id, pipelineinfo.background.vpp_frame.frame.id);
                //PRINTLINE("nn result ready");
                ai_result_t ai_result = {0};
                ai_result.type = 0;
                ai_result.detect_result_num = nn_post_detect_ipc[display_sensor_type].obj_num;

                nn_post_results.detection.obj_num = nn_post_detect_ipc[display_sensor_type].obj_num;
                for(int i=0; i<nn_post_detect_ipc[display_sensor_type].obj_num; i++){
                    nn_post_results.detection.objs[i].x = (float)nn_post_detect_ipc[display_sensor_type].objs[i].x_norm / IPC_INT_RANGE * VENC_CHN_WIDTH;
                    nn_post_results.detection.objs[i].y = (float)nn_post_detect_ipc[display_sensor_type].objs[i].y_norm / IPC_INT_RANGE * VENC_CHN_HEIGHT;
                    nn_post_results.detection.objs[i].w = (float)nn_post_detect_ipc[display_sensor_type].objs[i].w_norm / IPC_INT_RANGE * VENC_CHN_WIDTH;
                    nn_post_results.detection.objs[i].h = (float)nn_post_detect_ipc[display_sensor_type].objs[i].h_norm / IPC_INT_RANGE * VENC_CHN_HEIGHT;
                    nn_post_results.detection.objs[i].color = nn_post_detect_ipc[display_sensor_type].objs[i].color;
                    nn_post_results.detection.objs[i].cid = nn_post_detect_ipc[display_sensor_type].objs[i].cid;
                    nn_post_results.detection.objs[i].cprob = nn_post_detect_ipc[display_sensor_type].objs[i].cprob;

                    ai_result.detect_result[i].top_left_x = nn_post_results.detection.objs[i].x;
                    ai_result.detect_result[i].top_left_y = nn_post_results.detection.objs[i].y;
                    ai_result.detect_result[i].width = nn_post_results.detection.objs[i].w;
                    ai_result.detect_result[i].height = nn_post_results.detection.objs[i].h;
                    ai_result.detect_result[i].confidence = nn_post_results.detection.objs[i].cprob * 100;
                    ai_result.detect_result[i].class_id = nn_post_results.detection.objs[i].cid;
                }
                nn_post_results_draw.detection.obj_num = nn_post_detect_ipc[display_sensor_type].obj_num;
                for(int i=0; i<nn_post_detect_ipc[display_sensor_type].obj_num; i++){
                    nn_post_results_draw.detection.objs[i].x = nn_post_results.detection.objs[i].x;
                    nn_post_results_draw.detection.objs[i].y = nn_post_results.detection.objs[i].y;
                    nn_post_results_draw.detection.objs[i].w = nn_post_results.detection.objs[i].w;
                    nn_post_results_draw.detection.objs[i].h = nn_post_results.detection.objs[i].h;
                    nn_post_results_draw.detection.objs[i].color = nn_post_detect_ipc[display_sensor_type].objs[i].color;
                    nn_post_results_draw.detection.objs[i].cid = nn_post_detect_ipc[display_sensor_type].objs[i].cid;
                    nn_post_results_draw.detection.objs[i].cprob = nn_post_detect_ipc[display_sensor_type].objs[i].cprob;
                    // airvisen_trace("detect box: [%d, %d, %d, %d]\n", nn_post_results.detection.objs[i].x, nn_post_results.detection.objs[i].y, nn_post_results.detection.objs[i].w, nn_post_results.detection.objs[i].h);
                    // airvisen_trace("detect box: [%d, %d, %d, %d]\n", nn_post_detect_ipc.objs[i].x_norm, nn_post_detect_ipc.objs[i].y_norm, nn_post_detect_ipc.objs[i].w_norm, nn_post_detect_ipc.objs[i].h_norm);
                }

                set_ai_result(&ai_result);

#ifdef RGB_4K_TRACKING_ENABLED
                if(display_sensor_type != 2)
                {
                    //if(is_zoom_factor_on_4k() == VS_TRUE){
                    for(int i=0; i<nn_post_detect_ipc[display_sensor_type].obj_num; i++){
                        nn_post_results_draw.detection.objs[i].x *= 2;
                        nn_post_results_draw.detection.objs[i].y *= 2;
                        nn_post_results_draw.detection.objs[i].w *= 2;
                        nn_post_results_draw.detection.objs[i].h *= 2;
                    }
                    //}
                }

                
#endif //RGB_4K_TRACKING_ENABLED

                if (ret != VS_SUCCESS)
                {
                    vs_sample_trace("sample_common_nn_result_get failed with ret 0x%x\n", ret);
                    break;
                }
#ifdef SHOW_DETECTION
                ret = draw_detect();
#endif
                if (ret != 0)
                {
                    vs_sample_trace("draw_detect failed!\n");
                    break;
                }
            }
            break;
        case START_TRACK_STATE:
            PRINTLINE
            pthread_mutex_lock(&tracking_mutex);
#ifdef TRACKING_BUF_USE_CACHED_MEM
            /* invalidate cached mmz, cache mmz must be invalidate before read */
            ret = vs_mal_sys_cache_invalidate(src_phys_addr, pipelineinfo.background.src_virt_addr, pipelineinfo.background.vpp_frame.frame.height * pipelineinfo.background.vpp_frame.frame.width * 3 / 2);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vs_mal_sys_cache_invalidate failed with err 0x%x\n", ret);
            }
#endif
            // memcpy(processImg.ptr<unsigned char>(0), pipelineinfo.background.src_virt_addr, RGB_HEIGHT * RGB_WIDTH);
            printf("box:[%d,%d,%d,%d]\n", box.x, box.y, box.width, box.height);
            // if (tracker->init_first_frame(box, processImg) == -1) {
            // 	airvisen_trace("tracker init failed!\n");
            // 	g_TrackingState = NORMAL_STATE;
            // 	pthread_mutex_unlock(&tracking_mutex);
            // 	break;
            // }
            airvisen_send_detector_stop();
            printf("display_sensor_type : %d\n", display_sensor_type);
            if (airvisen_send_tracker_initbox(  (float)box.x / PROTOCOL_WIDTH, (float)box.y / PROTOCOL_HEIGHT, 
                                                (float)box.width / PROTOCOL_WIDTH, (float)box.height / PROTOCOL_HEIGHT, display_sensor_type) != 0)
            {
                airvisen_trace("tracker init failed!\n");
                g_TrackingState = NORMAL_STATE;
                pthread_mutex_unlock(&tracking_mutex);
                break;
            }
            // airvisen_recv_tracker_update_result(ipc_tracking_data, 256, 1, &tracking_results, (int*)&tracker_state); //clear ipc queue
            g_TrackingState = TRACKING_STATE;
            showRect = box;
            // airvisen_trace("showRect:[%d,%d,%d,%d]\n", showRect.x, showRect.y, showRect.width, showRect.height);
            pthread_mutex_unlock(&tracking_mutex);
            last_algo_tracker_source -1;
            break;
        case STOP_TRACK_STATE:
            //< @Aaron, add more clear operation here, if needed
            // tracker->stop();
            printf("Will STOP Tracking!\n");
            airvisen_send_tracker_stop();
            g_TrackingState = NORMAL_STATE;
            clear_zoom_factor_limit();
            last_algo_tracker_source = -1;
            algo_trakcer_source = -1;
            airvisen_send_detector_start();
            break;
        case TRACKING_STATE:
        {
#if 1

            WALLTIMETRACKING
            // PRINTLINEgit 
            //  WALLTIMETRACKING
            //PRINTLINE
#ifdef TRACKING_BUF_USE_CACHED_MEM

            //PRINTLINE
            /* invalidate cached mmz, cache mmz must be invalidate before read */
            ret = vs_mal_sys_cache_invalidate(src_phys_addr, pipelineinfo.background.src_virt_addr, pipelineinfo.background.origin_height  * pipelineinfo.background.origin_width * 3 / 2);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vs_mal_sys_cache_invalidate failed with err 0x%x\n", ret);
            }
#endif
            // memcpy(processImg.ptr<unsigned char>(0), pipelineinfo.background.src_virt_addr, RGB_HEIGHT * RGB_WIDTH);
            //					WALLTIMESTAT("[Tracker] get image from pipelineinfo.background.src_virt_addr timecost: ")

            WALLTIMETRACKING
            
            //PRINTLINE
            static track_result_ipc_s tracking_results_ipc;
            airvisen_tracker_update_result(&tracking_results_ipc, (int *)&tracker_state, &algo_trakcer_source);
            if(tracker_state == TRACKER_NOT_UPDATED)
            {
                airvisen_trace("tracker not updated!\n");
                vs_mal_sys_unmap(pipelineinfo.background.src_virt_addr, pipelineinfo.background.vpp_frame.frame.height * pipelineinfo.background.vpp_frame.frame.width * 3 / 2);
                ret = vs_mal_vpp_chn_frame_release(pipelineinfo.background.grp, pipelineinfo.background.chn, &pipelineinfo.background.vpp_frame);
                continue;
            }
                

            tracking_results.track.target_pos_x = (float)tracking_results_ipc.target_pos_x_norm / IPC_INT_RANGE * PROTOCOL_WIDTH;
            tracking_results.track.target_pos_y = (float)tracking_results_ipc.target_pos_y_norm / IPC_INT_RANGE * PROTOCOL_HEIGHT;
            tracking_results.track.target_sz_x = (float)tracking_results_ipc.target_sz_x_norm / IPC_INT_RANGE * PROTOCOL_WIDTH;
            tracking_results.track.target_sz_y = (float)tracking_results_ipc.target_sz_y_norm / IPC_INT_RANGE * PROTOCOL_HEIGHT;

            ai_result_t ai_result = {0};
            ai_result.type = 1;
            ai_result.track_source = algo_trakcer_source;
            ai_result.track_state = tracker_state;
            ai_result.track_result.top_left_x = tracking_results.track.target_pos_x;
            ai_result.track_result.top_left_y = tracking_results.track.target_pos_y;
            ai_result.track_result.width = tracking_results.track.target_sz_x;
            ai_result.track_result.height = tracking_results.track.target_sz_y;
            set_ai_result(&ai_result);
            //printf("pos_x = %d, pos_y = %d, sz_x = %d, sz_y = %d \n", tracking_results.track.target_pos_x, tracking_results.track.target_pos_y, tracking_results.track.target_sz_x, tracking_results.track.target_sz_y);

#ifdef ADD_ID_PTS_IN_NN_POST_RESULT_S
            // printf("cur frame id:%d, pts:%ld, nano_track id:%d, pts:%ld\n", pipelineinfo.background.vpp_frame.frame.id, pipelineinfo.background.vpp_frame.frame.pts, tracking_results.id, tracking_results.pts);
#endif
            // PRINTLINE
            cv::Rect rect = cv::Rect(tracking_results.track.target_pos_x, tracking_results.track.target_pos_y, tracking_results.track.target_sz_x, tracking_results.track.target_sz_y);

            if (rect.empty())
            {
                //airvisen_trace("tracker update failed!\n");
            }
            else if (rect.x < 0 || rect.y < 0 || rect.width < 0 || rect.height < 0 || rect.x + rect.width > PROTOCOL_WIDTH || rect.y + rect.height > PROTOCOL_HEIGHT)
            {
                airvisen_trace("tracker update failed! rect:[%d,%d,%d,%d]\n", rect.x, rect.y, rect.width, rect.height);
            }
            else
            {

#if 1
                //PRINTLINE
                if(algo_trakcer_source == 0 && display_sensor_type == USE_LONG_FOCAL_SENSOR) //short to long
                {
                     PRINTLINE
                    rect_short2long(rect);
                }
                else if(algo_trakcer_source == 1 && display_sensor_type == USE_SHORT_FOCAL_SENSOR) //long to short
                {
                    PRINTLINE
                    rect_long2short(rect);
                }
                // #ifndef HEQI_XY
                showRect = rect;
                // 电子变倍
#endif

                
                
                //draw on 4K FRAME
#ifdef RGB_4K_TRACKING_ENABLED

                if(display_sensor_type != 2)
                {
                    //if(is_zoom_factor_on_4k() == VS_TRUE){
                    showRect.x = showRect.x * 2;
                    showRect.y = showRect.y * 2;
                    showRect.width = showRect.width * 2;
                    showRect.height = showRect.height * 2;
                    //}
                }
#endif

                //printf("showRect.x=%d,showRect.y=%d,showRect.width=%d,showRect.height=%d \n", showRect.x, showRect.y, showRect.width, showRect.height );

                g_SendRect = rect;
                // #else
                //                         showRect.x = (showRect.x + rect.x)/2;
                //                         showRect.y = (showRect.y + rect.y)/2;
                //                         showRect.width = rect.width;
                //                         showRect.height = rect.height;
                // #endif
            }
            // PRINTLINE

            if ((showRect.x < 0 || showRect.y < 0 || showRect.width < 0 || showRect.height < 0 || showRect.x + showRect.width > RGB_WIDTH || showRect.y + showRect.height > RGB_HEIGHT))
            {
                airvisen_trace("tracker update failed!\n");
                g_TrackingState = NORMAL_STATE;
                break;
            }
            // assert(!(showRect.x < 0 || showRect.y < 0 || showRect.width < 0 || showRect.height < 0 || showRect.x+showRect.width > RGB_WIDTH || showRect.y+showRect.height > RGB_HEIGHT));

            WALLTIMESTAT("[Tracker] Tracking update timecost: ")

            // printf("showRect:x:%d, y:%d,width:%d, height:%d\n", showRect.x, showRect.y, showRect.width, showRect.height);

#ifdef TRACKER_DRAW_DETECT
            if (show_detect_in_track == 1)
            {
                ret = draw_detect();
                if (ret != 0)
                {
                    vs_sample_trace("draw_detect failed!\n");
                    // break;
                }
            }
            // ret = draw_detect();
            // if (ret != 0) {
            //     vs_sample_trace("draw_detect failed!\n");
            //     // break;
            // }
#endif // TRACKER_DRAW_DETECT
       //  PRINTLINE
       //  if(tracker->cur_state == TRACKER_LOST)
       // airvisen_trace("tracker_state : %d\n", (int)tracker_state);
            if (tracker_state == TRACKER_LOST)
            {
                PRINTLINE
                clear_zoom_factor_limit();
                last_algo_tracker_source = -1;
                algo_trakcer_source = -1;
                g_TrackingState = NORMAL_STATE;
                airvisen_send_detector_start();
                break;
            }
            // PRINTLINE
            tracking_results.detection.obj_num = 1;
            tracking_results.detection.objs[0].x = showRect.x;
            tracking_results.detection.objs[0].y = showRect.y;
            tracking_results.detection.objs[0].w = showRect.width;
            tracking_results.detection.objs[0].h = showRect.height;
            // PRINTLINE
            if (b_drawTrackingBox == 1 && tracker_state == TRACKER_TRACKING)
            {
                // PRINTLINE
                // WALLTIMETRACKING
                // static sample_nn_post_result_s tracking_results;
                tracking_results.detection.objs[0].color = 0xFFFFFFFF;
                ret = nn_gpe_tracking_boxes_draw(&pipelineinfo.background.vpp_frame, 1.0f, 1.0f, &tracking_results);
                // WALLTIMESTAT("Draw box timecost: ")
            }
            // PRINTLINE
#ifdef SHOW_SEARCHING_AREA
            if (b_drawTrackingBox == 1 && tracker_state == TRACKER_FINDING)
            {
                // WALLTIMETRACKING
                // static sample_nn_post_result_s tracking_results;
                tracking_results.detection.objs[0].color = 0xFF0000FF;
                ret = nn_gpe_tracking_boxes_draw(&pipelineinfo.background.vpp_frame, 1.0f, 1.0f, &tracking_results);
                // WALLTIMESTAT("Draw box timecost: ")
            }
#endif // SHOW_SEARCHING_AREA
            // PRINTLINE
            WALLTIMESTAT("[Tracker] Tracking proc total timecost: ")
#endif
        }
        break;
        default:
            break;
        }

        //PRINTLINE
        // vs_uint64_t resize_phys_addr = 0;
        // vs_void_t *p_resize_virt_addr = NULL;
        //float real_zoom_factor = get_real_zoom_factor();
        //printf("real zoom facotr =%f \n", real_zoom_factor);
        //resize_4k_to_fhd_frame(&pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, 1920, 1080);  

        float real_zoom_factor = get_real_zoom_factor();
        if (display_sensor_type == 0 || display_sensor_type == 1)
        {
            // if(display_sensor_type == 0)
            // {
            //     sample_gpe_scale(real_zoom_factor, &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, 0, 0);
            // }else{
            //     sample_gpe_scale(real_zoom_factor, &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, get_long_focal_lens_x_offset(), get_long_focal_lens_y_offset());
            // }
            //sample_gpe_scale(real_zoom_factor, &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr);
            if(real_zoom_factor < 2.0)
            {
                resize_4K_to_1080P_frame(&pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, pipelineinfo.background.width, pipelineinfo.background.height);            
                if(display_sensor_type == 0)
                {
                    resize_with_factor_and_output_1080P(real_zoom_factor, &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, VS_FALSE, 0 ,0);
                }
                else{
                    resize_with_factor_and_output_1080P(real_zoom_factor, &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, VS_FALSE, get_long_focal_lens_x_offset(), get_long_focal_lens_y_offset());
                }
                
            }
            else
            {
                if(display_sensor_type == 0)
                {
                    resize_with_factor_and_output_1080P(real_zoom_factor, &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, VS_TRUE, 0 ,0);
                }else{
                    resize_with_factor_and_output_1080P(real_zoom_factor, &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, VS_TRUE,get_long_focal_lens_x_offset(), get_long_focal_lens_y_offset());
                } 
                
            }
        }
        else
        {
            //ir zoom
            resize_with_facotr_ir(real_zoom_factor, &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, VS_FALSE, 0 ,0);  
            

        }

        apply_pseudo_color();
        //PRINTLINE

        //pip
        background_pip_with_mode(get_pip_mode(), &pipelineinfo.background.vpp_frame, pipelineinfo.background.src_virt_addr, is_infra_record);
#ifdef TRACKING_BUF_USE_CACHED_MEM
        ret = vs_mal_sys_cache_flush(src_phys_addr, pipelineinfo.background.src_virt_addr, pipelineinfo.background.vpp_frame.frame.height * pipelineinfo.background.vpp_frame.frame.width * 3 / 2);
        if (ret != VS_SUCCESS){
            printf("vs_mal_sys_cache_flush with error 0x%x\n\n", ret);
        }
#endif
    
        //printf("pipelineinfo.background.vpp_frame.frame.stride[0] :%d pipelineinfo.background.vpp_frame.frame.width :%d  pipelineinfo.background.vpp_frame.frame.height: %d\n",
        //pipelineinfo.background.vpp_frame.frame.stride[0], pipelineinfo.background.vpp_frame.frame.width,  pipelineinfo.background.vpp_frame.frame.height);

        // static int run_once = 1;
        // run_once ++;
        // if (run_once == 100)
        // {
        //     //run_once = 0;
        //     FILE *p_fout = fopen("/tmp/zhx/ive_zoom_out_background7.yuv", "wb");
        //     if (NULL == p_fout) {
        //         vs_sample_trace("failed to open output file\n");
        //         //goto exit1;
        //     }
        //     else
        //     {
        //         fwrite(pipelineinfo.background.src_virt_addr, 1920, 1080 * 1.5, p_fout);
        //         fclose(p_fout);
        //     }
        // }

        if (get_snap_photo_status())
        {
            ret = vs_mal_venc_frame_send(2, &pipelineinfo.background.vpp_frame, 0);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vpp_chn_frame_send failed with err 0x%x\n", ret);
            }
        }

        //PRINTLINE
        ret = vs_mal_venc_frame_send(SEND_VENC_CHN_ID, &pipelineinfo.background.vpp_frame, 0);
        if (ret != VS_SUCCESS)
        {
            vs_sample_trace("vpp_chn_frame_send failed with err 0x%x\n", ret);
        }

        //PRINTLINE
        //PRINTLINE
        pipelineinfo.background.vpp_frame.frame.stride[0] = pipelineinfo.background.origin_width;
        pipelineinfo.background.vpp_frame.frame.stride[1] = pipelineinfo.background.origin_width;
        pipelineinfo.background.vpp_frame.frame.stride[2] = pipelineinfo.background.origin_width;
        pipelineinfo.background.vpp_frame.frame.width = pipelineinfo.background.origin_width;
        pipelineinfo.background.vpp_frame.frame.height = pipelineinfo.background.origin_height;
        vs_mal_sys_unmap(pipelineinfo.background.src_virt_addr, pipelineinfo.background.origin_height * pipelineinfo.background.origin_width * 3 / 2);
        ret = vs_mal_vpp_chn_frame_release(pipelineinfo.background.grp, pipelineinfo.background.chn, &pipelineinfo.background.vpp_frame);
        if (ret != VS_SUCCESS)
        {
            vs_sample_trace("vpp_chn_frame_release failed with err 0x%x, group_id %d, chn_id %d\n", ret, VIS_VPP_GRP_ID, VIS_VPP_FHD_CHN_ID);
        }


        //PRINTLINE
        if (algo_trakcer_source == 0)
        {   
            //tracker source from long to short
            if(display_sensor_type == 1){
                long_switch_short();
                update_zoom_factor(get_zoom_factor());
                update_vo_mode();
            }
            // last_algo_tracker_source = algo_trakcer_source;
            
            limit_short_lens_zoom_factor();
        }

        if (algo_trakcer_source == 1)
        {
            
            if(display_sensor_type == 0 ){
                short_switch_long();
                update_zoom_factor(get_zoom_factor());
                update_vo_mode();
            }
            // last_algo_tracker_source = algo_trakcer_source;
            limit_long_lens_zoom_factor();
        }

        //PRINTLINE
    }




}



extern "C" {

    void handle_stop_track_cmd()
    {
        //tracking_camera_stop_track_struct *msg = (tracking_camera_stop_track_struct *)ptBuf;
        PRINTLINE
        //stop tracking
        pthread_mutex_lock(&tracking_mutex);
        g_TrackingState = STOP_TRACK_STATE;
        pthread_mutex_unlock(&tracking_mutex);

        //camera_stop_track(msg, 1);
    }



    void handle_start_track_cmd(unsigned int x, unsigned int y, unsigned int w, unsigned int h)
    {
        //PRINTLINE
        //tracking_camera_start_track_struct *msg = (tracking_camera_start_track_struct *)ptBuf;
        cv::Rect rect;
        //start tracking
        rect.height = h;
        rect.width = w;
        rect.x = x;
        rect.y = y;
        printf("x:%d, y:%d, w:%u, h:%u\n",x, y, w, h);
        if (rect.width <= 0 || rect.height <= 0 || rect.x < 0 || rect.x + rect.width > RGB_WIDTH || rect.y < 0 || rect.y + rect.height > RGB_HEIGHT)
        {
            printf("invalid [x, y, w, h]:[%d, %d, %d, %d]\n", rect.x, rect.y, rect.width, rect.height);   
            //start_track_ack(msg, 2);
            return;
        }

        
        float zoom_factor = get_real_zoom_factor();
        //<< nanotracker use fhd size
        airvisen_trace("zoom_factor:%f\n", zoom_factor);
        if(get_sensor_type() == 1)
        {
            rect.x = (rect.x - PROTOCOL_WIDTH / 2) / zoom_factor + PROTOCOL_WIDTH / 2 - get_long_focal_lens_x_offset();
            rect.y = (rect.y - PROTOCOL_HEIGHT / 2) / zoom_factor + PROTOCOL_HEIGHT / 2 - get_long_focal_lens_y_offset();
            rect.width = rect.width / zoom_factor;
            rect.height = rect.height / zoom_factor;
        
        }
        else 
        {
            // PRINTLINE
            rect.x = (rect.x - PROTOCOL_WIDTH / 2) / zoom_factor + PROTOCOL_WIDTH / 2;
            rect.y = (rect.y - PROTOCOL_HEIGHT / 2) / zoom_factor + PROTOCOL_HEIGHT / 2;
            rect.width = rect.width / zoom_factor;
            rect.height = rect.height / zoom_factor;

        }
        

        airvisen_trace("camera mode:%d rect:[%d, %d, %u, %u]\n", get_input_visible_sensor_type(), rect.x, rect.y, rect.width, rect.height);
        int ret = parse_detect_result(&nn_post_results, &rect);
        if (ret == 0)
        {
            airvisen_trace("rect:[%d, %d, %u, %u]\n", rect.x, rect.y, rect.width, rect.height);
        }
        else
        {
            airvisen_trace("no obj detected at the current point[%d, %d]\n", rect.x, rect.y);
        }

        pthread_mutex_lock(&tracking_mutex);
        box.x = rect.x;
        box.y = rect.y;
        box.width = rect.width;
        box.height = rect.height;
        g_TrackingState = START_TRACK_STATE;
        pthread_mutex_unlock(&tracking_mutex);


        //start_track_ack(msg, 1);

    }



    void setting_cam_snap_record_mode(int type)
    {
        if (type == 0)
        {
            LOG_D("setting_cam_snap_record_mode 0\n");
            
        }
        else if (type == 1)
        {
            LOG_D("setting_cam_snap_record_mode 1\n");
            
        }

    }


    
    void start_video_record_with_mode(int mode)
    {
        /*
        0x00：默认录像（红外+可见光一起录像）
        0x01：红外录像（分辨率：640*512）
        0x02：可见光录像（分辨率：4000*3000）
        0x03：红外+可见光录像
        0x04：视频流录屏（分辨率：1920*1080）
        */
        int vl_venc_chn = 1;   //visible light venc chn
        int ir_venc_chn = 3;   //infra red venc chn
        if (mode == 0)
        {
            LOG_D("start_record_with_mode 0\n");
            is_4k_record = 1;
            is_infra_record = 1;
            //airvisen_send_start_video_record_command(1);
            usleep(1000);
            airvisen_send_start_video_record_command(3);
        }
        else if (mode == 1)
        {
            LOG_D("start_record_with_mode 1\n");
            //airvisen_send_stop_video_record_command();d
        }
        else if (mode == 2)
        {
            LOG_D("start_record_with_mode 2\n");
            //airvisen_send_start_video_record_command();
        }
        else if (mode == 3)
        {
            LOG_D("start_record_with_mode 3\n");
            //airvisen_send_start_video_record_command();
        }
        else if (mode == 4)
        {
            LOG_D("start_record_with_mode 4\n");
            //airvisen_send_start_video_record_command();
        }
    }


    void stop_video_record()
    {
        LOG_D("stop_video_record\n");
        is_4k_record = 0;
        is_infra_record = 0;
        //airvisen_send_stop_video_record_command(1);
        usleep(1000);
        airvisen_send_stop_video_record_command(3);
    }



    void handle_set_pseudo_color(pseudo_color_type_e type)
    {
        printf("handle_set_pseudo_color\n");
        //thermal_camera_switch_color_palette_struct *msg = (thermal_camera_switch_color_palette_struct *)ptBuf;
        //airvisen_send_set_pseudo_color_command(msg->pseudo_color);
        //airvisen_control_pseudo_color_command(type);
        //TODO use ive 

        set_pseudo_color_type(type);
    }




}




int start_message_proc() 
{
    //todo
    //start_fpv_message_proc("/dev/ttyAMA0");
    start_fc();
    start_gimbal();

}



void *record_4k_proc(void *args)
{
    int isrecording = 0;
    int ret = 0;
    int grp = 0;
    int chn = 0;
    int venc_chn = 1;
    vs_video_frame_info_s background_vpp_frame = {0};
    while(!g_bTrackingExit)
    {
        if(is_4k_record)
        {
            if(!isrecording){
                isrecording = 1;
                //TODO send ipc
                //start_record_4k();
            }
            //accquire 4k video
            ret = vs_mal_vpp_chn_frame_acquire(grp, chn, &background_vpp_frame, VPP_ACQUIRE_TIMEOUT);
            if (ret != VS_SUCCESS)
            {
                // vs_sample_trace("vpp_chn_frame_acquire failed with err 0x%x, group_id %d, chn_id %d\n", ret, VII_VPP_GRP_ID, IR_VPP_CHN_ID);
                usleep(10 * 1000);
                continue;
            }

            //send to venc
            ret = vs_mal_venc_frame_send(venc_chn, &background_vpp_frame, 0);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vpp_chn_frame_send failed with err 0x%x\n", ret);
            }


            ret = vs_mal_vpp_chn_frame_release(grp, chn, &background_vpp_frame);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vpp_chn_frame_release failed with err 0x%x, group_id %d, chn_id %d\n", ret, VIS_VPP_GRP_ID, VIS_VPP_FHD_CHN_ID);
            }
        }
        else
        {
            if(isrecording){
                isrecording = 0;
                //TODO send ipc
                //stop_record_4k();
            }
            usleep(100*1000);
        }
    }

}

void *record_1080P_proc(void *args)
{
    int isrecording = 0;
    int ret = 0;
    int grp = 1;
    int chn = 0;
    int venc_chn = 1;
    vs_video_frame_info_s background_vpp_frame = {0};
    while(!g_bTrackingExit)
    {
        
        if(is_1080P_record)
        {
            if(!isrecording){
                isrecording = 1;
                //TODO send ipc
                //start_record_1080P();
            }
            //accquire 1080P video, and send to venc
            ret = vs_mal_vpp_chn_frame_acquire(grp, chn, &background_vpp_frame, VPP_ACQUIRE_TIMEOUT);
            if (ret != VS_SUCCESS)
            {
                // vs_sample_trace("vpp_chn_frame_acquire failed with err 0x%x, group_id %d, chn_id %d\n", ret, VII_VPP_GRP_ID, IR_VPP_CHN_ID);
                usleep(10 * 1000);
                continue;
            }

            //send to venc
            ret = vs_mal_venc_frame_send(venc_chn, &background_vpp_frame, 0);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vpp_chn_frame_send failed with err 0x%x\n", ret);
            }


            ret = vs_mal_vpp_chn_frame_release(grp, chn, &background_vpp_frame);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vpp_chn_frame_release failed with err 0x%x, group_id %d, chn_id %d\n", ret, VIS_VPP_GRP_ID, VIS_VPP_FHD_CHN_ID);
            }
        }
        else
        {
            if(isrecording){
                isrecording = 0;
                //TODO send ipc
                //stop_record_1080P();
            }
            usleep(100*1000);
        }
    }
}


void *record_infra_proc(void *args)
{
    int isrecording = 0;
    int ret = 0;
    int grp = 3;
    int chn = 3;
    int venc_chn = 3;
    vs_video_frame_info_s background_vpp_frame = {0};
    while(!g_bTrackingExit)
    {
        if(is_infra_record)
        {
            if(!isrecording){
                isrecording = 1;
                //TODO send ipc
                //start_record_infra();
            }


            if (get_pip_mode() == E_4K_PIP_1080P_INFRA || get_pip_mode() == E_4K_PIP_INFRA || get_pip_mode() == E_1080P_PIP_4K_INFRA || get_pip_mode() == E_1080P_PIP_INFRA)
            {
                usleep(10*1000);
                continue;
            }

            //accquire infra video, and send to venc
            ret = vs_mal_vpp_chn_frame_acquire(grp, chn, &background_vpp_frame, VPP_ACQUIRE_TIMEOUT);
            if (ret != VS_SUCCESS)
            {
                // vs_sample_trace("vpp_chn_frame_acquire failed with err 0x%x, group_id %d, chn_id %d\n", ret, VII_VPP_GRP_ID, IR_VPP_CHN_ID);
                usleep(10 * 1000);
                continue;
            }

            //send to venc
            ret = vs_mal_venc_frame_send(venc_chn, &background_vpp_frame, 0);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vpp_chn_frame_send failed with err 0x%x\n", ret);
            }


            ret = vs_mal_vpp_chn_frame_release(grp, chn, &background_vpp_frame);
            if (ret != VS_SUCCESS)
            {
                vs_sample_trace("vpp_chn_frame_release failed with err 0x%x, group_id %d, chn_id %d\n", ret, VIS_VPP_GRP_ID, VIS_VPP_FHD_CHN_ID);
            }
        }
        else
        {
            if(isrecording){
                isrecording = 0;
                //TODO send ipc
                //stop_record_infra();
            }
            usleep(100*1000);
        }
    }

}


void *recv_all_ipc_proc(void *args)
{
    bool is_show_video_record_bmp = false;
    bool is_measure_temperature = true;
    while (!g_bTrackingExit)
    {
        

        ////show record flag
        if (is_video_record == true && video_record_frame_count % 30 == 0)
        {
            is_show_video_record_bmp = !is_show_video_record_bmp;
#ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
            airvisen_draw_tracker_video_record_second(video_record_frame_count / 30);
#endif
            // airvisen_video_record_overlay_display_on_off(is_show_video_record_bmp);

            // recv notify message from rtsprec, if video record stop auto or unexpected halt, hide osd(video record).
            char ipc_data[256] = {0};
            int command = airvisen_recv_video_record_message(ipc_data, 256, 1);
            if (command == VIDEO_RECORD_STOP_EVENT)
            {
                vs_sample_trace("recv video record stop event\n");
#ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
                airvisen_video_record_overlay_display_on_off(false);
                airvisen_draw_tracker_video_record_second(0);
#endif
                is_video_record = false;
            }
        }




        ///////recv temperature 
        if(is_measure_temperature == true)
        {
            char ir_data[256] = {0};
            int command = airvisen_recv_ir_message(ir_data, 256, 1);
            if (command == IR_MEASURE_TEMPERATURE_RESPONSE)
            {
                vs_sample_trace("recv temperature data\n");
                ir_response_measure_temperature_s *msg = (ir_response_measure_temperature_s *)ir_data;
                printf("temperature:  mode : %d max : %.2f min : %.2f\n",msg->mode,  (msg->frame.max_temp / 16 - 273.15), (msg->frame.min_temp / 16 - 273.15));

#ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
                //show temperature
                //airvisen_video_record_overlay_display_on_off(false);
                //airvisen_draw_tracker_video_record_second(0);
#endif
            }
        }


        //todo recv record status
        usleep(25000);

    }
    
}

int tracker_initialize(void *args)
{
    // PRINTLINE
    pthread_mutex_init(&tracking_mutex, NULL);

    // PRINTLINE
    pthread_t tracker_thread_id1;
    pthread_t tracker_comm_thread_id1;

    //start messge process thread
    start_message_proc();

    pthread_create(&tracker_thread_id1, 0, tracking_proc, args);
    //record

    pthread_t snap_photo_id;
    pthread_t record_4k_thread_id;
    pthread_t record_1080P_thread_id;
    pthread_t record_infra_thread_id;
    pthread_t recv_ipc_thread_id;

    pthread_create(&record_4k_thread_id, 0, record_4k_proc, args);
    pthread_create(&record_1080P_thread_id, 0, record_1080P_proc, args);
    pthread_create(&record_infra_thread_id, 0, record_infra_proc, args);
    pthread_create(&recv_ipc_thread_id, 0, recv_all_ipc_proc, args);



    snap_photo_init();


    return 0;
}


#if 0
void handle_elec_zoom_rgb(const char *ptBuf)
{
    PRINTLINE
    zoom_msg_s msg;
    memset(&msg, 0, sizeof(msg));
    int ret = parse_elec_zoom_rgb(ptBuf, &msg);
    if (ret != 0)
    {
        PRINTLINE

        printf("parse_elec_zoom_rgb failed\n");
        return;
    }
    
    PRINTLINE

    switch (msg.action_type)
    {
    case E_ZOOM_TO_DEFAULT:
        set_zoom_factor_to_default();
        break;
    case E_ZOOM_ADD_1X:
        add_zoom_factor_1x();
        break;
    case E_ZOOM_SUB_1X:
        sub_zoom_factor_1x();
        break;
    case E_ZOOM_ADD_10X:
        add_zoom_factor_10x();
        break;
    case E_ZOOM_SUB_10X:
        sub_zoom_factor_10x();
        break;
    case E_ZOOM_ADD_100X:
        add_zoom_factor_100x();
        break;
    case E_ZOOM_SUB_100X:
        sub_zoom_factor_100x();
        break;
    case E_ZOOM_TO_SPECIFIC:
    {
        unsigned short zoomValue = (unsigned short)ptBuf[5] + ((unsigned short)ptBuf[6] << 8);
        //tmp_zoomValue = zoomValue * 0.1;
        set_zoom_factor(zoomValue * 10);
        break;
    }
    default:
        break;
    }


     float factor = get_zoom_factor();
     update_zoom_factor(factor);
 #ifdef DRAW_RGB_ZOOM_FACTOR
     char tempBuf[256] = {0};
     sprintf(tempBuf, "ZOOM:%1.1fX", factor);
 #ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
     airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 3, tempBuf);
 #endif
 #endif


}
#endif


int tracker_destory()
{
    printf("come to tracker_destory!!!\n");
    g_bTrackingExit = true;
    sleep(1);

    // g_uppercommtype(上位机通讯类型)：-1-未知、0-串口通讯、1-tcp通讯
    // if ((g_uppercommtype == 1 || g_uppercommtype == 3))
    //     net_stop();

    pthread_mutex_destroy(&tracking_mutex);
    printf("come to tracker_destory!!!\n");
    return 0;
}
