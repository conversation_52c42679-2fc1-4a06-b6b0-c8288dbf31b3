#ifndef _ZOOM_H_
#define _ZOOM_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdio.h>
#include <string.h>
#include <stdbool.h>

#define MAX_ZOOM_FACTOR 1500
#define MIN_ZOOM_FACTOR 100


#define IR_MIN_ZOOM_FACTOR 100
#define IR_MAX_ZOOM_FACTOR 800


#define USE_SHORT_FOCAL_SENSOR  0
#define USE_LONG_FOCAL_SENSOR   1

#define DUAL_LIGHT_SWITCH_FACTOR_INFLAT     (420)
#define DUAL_LIGHT_SWITCH_FACTOR            (DUAL_LIGHT_SWITCH_FACTOR_INFLAT/100.0)

#define LONG_FOCAL_BASE_ZOOM_FACTOR_INFLAT  (390)
#define LONG_FOCAL_BASE_ZOOM_FACTOR         (LONG_FOCAL_BASE_ZOOM_FACTOR_INFLAT/100.0)
#define DUAL_LIGHT_LONG_LENS_BASE_FACTOR    (DUAL_LIGHT_SWITCH_FACTOR - LONG_FOCAL_BASE_ZOOM_FACTOR + 1.0) //1.3



typedef enum 
{
    SENSOR_MODE_SHORT_VI = 0,
    SENSOR_MODE_LONG_VI = 1,
    SENSOR_MODE_IR,
}E_camera_mode;

typedef enum 
{
    E_4K_FULL_SCREEN = 0x01,
    E_4K_PIP_1080P = 0x02,
    E_4K_PIP_INFRA = 0x03,
    E_4K_PIP_1080P_INFRA = 0x04,

    E_1080P_FULL_SCREEN = 0x05,
    E_1080P_PIP_4K = 0x06,
    E_1080P_PIP_INFRA = 0x07,
    E_1080P_PIP_4K_INFRA = 0x08,

    E_INFRA_FULL_SCREEN = 0x09,
    E_INFRA_PIP_4K = 0x0A,
    E_INFRA_PIP_1080P = 0x0B,
    E_INFRA_PIP_4K_1080P = 0x0C,

}IMAGE_MODE_E;


void set_sensor_type(int type);
int get_sensor_type();

//0.01 step 
void add_zoom_factor_1x();
//real 0.01 step
void sub_zoom_factor_1x();

//0.1 step 
void add_zoom_factor_10x();
//real 0.1 step
void sub_zoom_factor_10x();

//1 step 
void add_zoom_factor_100x();
//real 1 step
void sub_zoom_factor_100x();

void set_zoom_factor(int factor);
int set_zoom_factor_to_default();

float get_zoom_factor();
float get_real_zoom_factor();
int get_input_visible_sensor_type();
bool is_zoom_factor_on_4k();


void set_min_zoom_factor(int factor);
void set_max_zoom_factor(int factor);


void long_switch_short();
void short_switch_long();


void limit_short_lens_zoom_factor();
void limit_long_lens_zoom_factor();
void clear_zoom_factor_limit();




int continuous_zoom();
int vl_start_continuous_zoom(unsigned int zoom_type);
int vl_stop_continuous_zoom();
int vl_continuous_zoom_single_step(unsigned int zoom_type);

void update_vo_mode();
void handle_set_vo_mode(int type);
IMAGE_MODE_E get_pip_mode();


int get_refresh_status();


int get_zoom_level();

int get_focal_length();

#ifdef __cplusplus
}
#endif

#endif
