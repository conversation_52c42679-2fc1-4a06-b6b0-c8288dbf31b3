#include "report_message.h"
#include "dronepod_k40t_protocol.h"
#include "dronepod_status.h"
#include "zoom.h"
#include "osd_message.h"
#include <math.h>
#include <sys/statvfs.h>

#define LOG_LEVEL LOG_LEVEL_ERROR
#include "log_utils.h"

// 数学常量
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 地球半径 (米)
#define EARTH_RADIUS_M 6378137.0

// 角度转弧度
#define DEG_TO_RAD(deg) ((deg) * M_PI / 180.0)
// 弧度转角度
#define RAD_TO_DEG(rad) ((rad) * 180.0 / M_PI)
static int g_exit_thread = 0;
static clock_t pre_frame_clock_t = 0, current_frame_clock_t;
static ai_result_t g_ai_buffer = {0};
pthread_mutex_t ai_buffer_lock = PTHREAD_MUTEX_INITIALIZER;

/**
 * @brief 根据飞机位置、云台姿态和目标距离计算目标GPS坐标
 * @param plane_lon 飞机经度 (度*10^7)
 * @param plane_lat 飞机纬度 (度*10^7)
 * @param plane_alt 飞机海拔高度 (米*10)
 * @param gimbal_yaw 云台偏航角 (度*10, 相对于飞机机头)
 * @param gimbal_pitch 云台俯仰角 (度*10, 负值向下)
 * @param target_distance 目标距离 (米)
 * @param obj_lon 输出目标经度 (度*10^7)
 * @param obj_lat 输出目标纬度 (度*10^7)
 * @param obj_alt 输出目标海拔高度 (米*10)
 */
void calculate_target_position(
    int plane_lon, int plane_lat, int plane_alt,
    int gimbal_yaw, int gimbal_pitch, int target_distance,
    int *obj_lon, int *obj_lat, int *obj_alt)
{
    // 参数检查
    if (obj_lon == NULL || obj_lat == NULL || obj_alt == NULL) {
        printf("错误: calculate_target_position - 输出参数为空\n");
        return;
    }

    if (target_distance <= 0) {
        printf("警告: 目标距离无效 (%d m), 使用默认值\n", target_distance);
        *obj_lon = plane_lon;
        *obj_lat = plane_lat;
        *obj_alt = plane_alt;
        return;
    }

    // 数据格式转换
    double plane_lon_deg = (double)plane_lon / 1e7;      // 度*10^7 -> 度
    double plane_lat_deg = (double)plane_lat / 1e7;      // 度*10^7 -> 度
    double plane_alt_m = (double)plane_alt / 10.0;       // 米*10 -> 米
    double gimbal_yaw_deg = (double)gimbal_yaw / 10.0;   // 度*10 -> 度
    double gimbal_pitch_deg = (double)gimbal_pitch / 10.0; // 度*10 -> 度
    double distance_m = (double)target_distance;          // 米

    // 角度转弧度
    double plane_lat_rad = DEG_TO_RAD(plane_lat_deg);
    double plane_lon_rad = DEG_TO_RAD(plane_lon_deg);
    double gimbal_yaw_rad = DEG_TO_RAD(gimbal_yaw_deg);
    double gimbal_pitch_rad = DEG_TO_RAD(gimbal_pitch_deg);

    // 计算水平距离和高度差
    double horizontal_distance = distance_m * cos(gimbal_pitch_rad);  // 水平投影距离
    double height_diff = distance_m * sin(-gimbal_pitch_rad);         // 高度差 (负俯仰角向下)

    // 计算目标海拔高度
    double target_alt_m = plane_alt_m + height_diff;

    // 计算地球在当前纬度的半径
    double earth_radius_at_lat = EARTH_RADIUS_M * cos(plane_lat_rad);

    // 计算经纬度偏移量 (使用小角度近似，适用于短距离)
    double delta_lat_rad = (horizontal_distance * cos(gimbal_yaw_rad)) / EARTH_RADIUS_M;
    double delta_lon_rad = (horizontal_distance * sin(gimbal_yaw_rad)) / earth_radius_at_lat;

    // 计算目标经纬度
    double target_lat_rad = plane_lat_rad + delta_lat_rad;
    double target_lon_rad = plane_lon_rad + delta_lon_rad;

    // 转换回度
    double target_lat_deg = RAD_TO_DEG(target_lat_rad);
    double target_lon_deg = RAD_TO_DEG(target_lon_rad);

    // 转换为输出格式
    *obj_lat = (int)(target_lat_deg * 1e7);    // 度 -> 度*10^7
    *obj_lon = (int)(target_lon_deg * 1e7);    // 度 -> 度*10^7
    *obj_alt = (int)(target_alt_m * 10.0);     // 米 -> 米*10

    // 调试输出
    printf("目标位置计算:\n");
    printf("  飞机位置: %.7f°, %.7f°, %.1fm\n", plane_lon_deg, plane_lat_deg, plane_alt_m);
    printf("  云台姿态: 偏航=%.1f°, 俯仰=%.1f°\n", gimbal_yaw_deg, gimbal_pitch_deg);
    printf("  目标距离: %.1fm (水平=%.1fm, 高差=%.1fm)\n", distance_m, horizontal_distance, height_diff);
    printf("  目标位置: %.7f°, %.7f°, %.1fm\n", target_lon_deg, target_lat_deg, target_alt_m);
}




// SD卡信息结构体
typedef struct {
    int is_mounted;         // 是否已挂载 (1:是, 0:否)
    unsigned long total_mb;  // SD卡总容量 (MB)
    unsigned long free_mb;   // 可用空间 (MB)
    unsigned long used_mb;   // 已用空间 (MB)
} SDCardInfo;

// 获取SD卡信息函数
int get_sdcard_info(SDCardInfo *info) {
    const char* mount_point = "/media/sdcard1";
    const char* flag_file = "/media/sdcard1/.sdcard_is_good";  // 修正为隐藏文件名
    
    // 初始化结构体
    memset(info, 0, sizeof(SDCardInfo));
    
    // 检查隐藏标志文件是否存在
    if (access(flag_file, F_OK) != 0) {
        // 文件不存在，SD卡未挂载
        info->is_mounted = 0;
        return 0;  // 正常返回，但未挂载
    }
    
    // 如果标志文件存在，继续获取容量信息
    info->is_mounted = 1;
    
    struct statvfs vfs;
    if (statvfs(mount_point, &vfs) != 0) {
        perror("statvfs failed");
        return -1;  // 获取文件系统信息失败
    }
    
    // 计算块大小 (字节)
    unsigned long block_size = vfs.f_frsize;
    
    // 计算容量 (转换为MB)
    unsigned long total_bytes = vfs.f_blocks * block_size;
    unsigned long free_bytes = vfs.f_bavail * block_size;
    
    info->total_mb = total_bytes / (1024 * 1024);
    info->free_mb  = free_bytes / (1024 * 1024);
    info->used_mb  = info->total_mb - info->free_mb;
    
    return 0;
}

// enum trackState
// {
//     TRACKER_TRACKING = 1,
//     TRACKER_FINDING = 2,
//     TRACKER_LOST = 3,
//     TRACKER_NOT_UPDATED = 4
// };
// Payload_Camera_System_Status_0x000003_t g_camera_sys_status = {0};
// Payload_IR_Camera_Status_0x000004_t g_ir_camera_status = {0};
// Payload_VL_Camera_Status_0x000005_t g_visible_camera_status = {0};
payload_gb_heartbeat_v1_t hb = {0};

void *send_track_detect_message_to_fcs_proc(void *arg) 
{

    char proc_name[32] = "trackdetectfcs";
    prctl(PR_SET_NAME, proc_name, 0, 0, 0);

    int ret = -1;
    int client_sock = -1;

    // extern char *g_strConfigIni;
    //float d_send_time = 33.3;
    float d_send_time = 40;
    // int FPS = GetIniKeyInt("serial", "sendfps", g_strConfigIni);
    // if (FPS != 0)
    //     d_send_time = 1000.0 / (float)FPS;
    // airvisen_trace("delta send time: %f\n", d_send_time);

    //g_track_result.short_lens_divisor = 0.3;
    //g_track_result.long_lens_divisor = 0.2;

    while (!g_exit_thread)
    {
        // printf("time(ns):%ld, command send count: %ld\n", get_sys_uptime_in_ns(), g_SendCount);
        // usleep(3000);
        //< send nn result out from serial/net

        static double total_t = 0;
        current_frame_clock_t = clock();
        total_t = (double)(current_frame_clock_t - pre_frame_clock_t) / CLOCKS_PER_SEC * 1000;

        if (total_t < d_send_time)
        {
            int sleep_us = (int)((d_send_time - total_t) * 1000);
            // printf("will sleep %d us\n", sleep_us);
            usleep(sleep_us);
        }
        pre_frame_clock_t = clock();

        // float divisor = g_track_result.short_lens_divisor;
        // if (g_track_result.algo_trakcer_source == 1)
        // {
        //     divisor = g_track_result.long_lens_divisor;
        // }
        //
        //cv::Rect tempRect = g_SendRect;
        // int x = (g_track_result.x - 960) * divisor + 960;
        // int y = (g_track_result.y - 540) * divisor + 540;
        // int width = g_track_result.width * divisor;
        // int height = g_track_result.height * divisor;

        // handle_state_ai_tracking(x, y, width, height, 100, 0);

        ai_result_t tmp_buffer = {0};
        pthread_mutex_lock(&ai_buffer_lock);
        memcpy(&tmp_buffer, &g_ai_buffer, sizeof(ai_result_t));
        pthread_mutex_unlock(&ai_buffer_lock);

        if (tmp_buffer.type == 0)
        {
           // 检测结果处理
            int total_detections = tmp_buffer.detect_result_num;
            int max_per_packet = 24; // 每包最多24个检测结果
            int num_packets = (total_detections + max_per_packet - 1) / max_per_packet; // 向上取整计算包数
            
            if(num_packets > 0){
                //printf("发送检测结果，共 %d 个目标，分 %d 包发送\n", total_detections, num_packets);
                LOG_D("发送检测结果，共 %d 个目标，分 %d 包发送\n", total_detections, num_packets);
            }
            
            for (int packet = 0; packet < num_packets; packet++)
            {
                int start_idx = packet * max_per_packet;
                int end_idx = (packet + 1) * max_per_packet;
                if (end_idx > total_detections) {
                    end_idx = total_detections;
                }
                
                int current_packet_size = end_idx - start_idx;
                LOG_D("发送第 %d 包，包含 %d 个目标 (索引 %d 到 %d)\n", 
                       packet + 1, current_packet_size, start_idx, end_idx - 1);
                
                // 创建状态帧结构体
                StatusFrame_AI_Recognition_0x000319_t status_frame = {0};
                
                // 设置包信息：高4位为总包数，低4位为当前包ID
                status_frame.packet_info = ((num_packets & 0x0F) << 4) | ((packet + 1) & 0x0F);
                
                // 设置检测状态：0x00表示成功
                status_frame.detect_status = 0x00;                
                
                // 复制当前包的目标数据
                for (int i = 0; i < current_packet_size; i++)
                {
                    memcpy(&status_frame.targets[i], &tmp_buffer.detect_result[start_idx + i], sizeof(AI_Target_Data_t));
                }
                
                // 调用handle_state_ai_detection发送当前包
                handle_state_ai_detection(&status_frame);
                
                // 如果有多个包，在包之间添加一些延迟，避免发送过快
                if (num_packets > 1 && packet < num_packets - 1) {
                    usleep(50); // 5ms延迟
                }
            }

        }
        else
        {
            handle_state_ai_tracking(
                g_ai_buffer.track_result.top_left_x,
                g_ai_buffer.track_result.top_left_y, 
                g_ai_buffer.track_result.width, 
                g_ai_buffer.track_result.height, 
                100, 
                1);
        }
    

    }

}

void *send_other_message_to_fcs_proc(void *arg) 
{

    pthread_setname_np(pthread_self(), "sendtofcs");
    char proc_name[32] = "sendtofcs";
    prctl(PR_SET_NAME, proc_name, 0, 0, 0);

    int ret = -1;
    int client_sock = -1;
    unsigned int loop_count = 0;

    // extern char *g_strConfigIni;
    float d_send_time = 10;
    // int FPS = GetIniKeyInt("serial", "sendfps", g_strConfigIni);
    // if (FPS != 0)
    //     d_send_time = 1000.0 / (float)FPS;
    // airvisen_trace("delta send time: %f\n", d_send_time);

    //g_track_result.short_lens_divisor = 0.3;
    //g_track_result.long_lens_divisor = 0.2;

    while (!g_exit_thread)
    {
        // printf("time(ns):%ld, command send count: %ld\n", get_sys_uptime_in_ns(), g_SendCount);
        // usleep(3000);
        //< send nn result out from serial/net
        loop_count++;
        static double total_t = 0;
        current_frame_clock_t = clock();
        total_t = (double)(current_frame_clock_t - pre_frame_clock_t) / CLOCKS_PER_SEC * 1000;

        if (total_t < d_send_time)
        {
            int sleep_us = (int)((d_send_time - total_t) * 1000);
            // printf("will sleep %d us\n", sleep_us);
            usleep(sleep_us);
        }
        pre_frame_clock_t = clock();

        //20ms
        if (loop_count % 2 == 0)
        {
            osd_message_s osd_message;
            dronepod_status_t * status = get_dronepod_status();

            // 计算目标位置 (如果有有效的飞机位置、云台姿态和目标距离)
            if (status->plane_lontitude != 0 && status->plane_latitude != 0 &&
                status->obj_distance > 0) {
                calculate_target_position(
                    status->plane_lontitude,    // 飞机经度
                    status->plane_latitude,     // 飞机纬度
                    status->plane_altitude,     // 飞机海拔
                    status->yaw_angle,          // 云台偏航角
                    status->pitch_angle,        // 云台俯仰角
                    status->obj_distance,       // 目标距离
                    &status->obj_lontitude,     // 输出目标经度
                    &status->obj_latitude,      // 输出目标纬度
                    &status->obj_altitude       // 输出目标海拔
                );
            }
            osd_message.plane_lontitude = status->plane_lontitude;
            osd_message.plane_latitude = status->plane_latitude;
            osd_message.plane_altitude = status->plane_altitude;
            osd_message.obj_distance = status->obj_distance;

            osd_message.obj_lontitude = status->obj_lontitude;
            osd_message.obj_latitude = status->obj_latitude;
            osd_message.obj_altitude = status->obj_altitude;

            osd_message.track_state = status->track_state;
            osd_message.zoom_factor = get_zoom_factor() * 10.0;
            osd_message.pitch_angle = status->pitch_angle;
            osd_message.yaw_angle = status->yaw_angle;
            airvisen_update_osd_request(&osd_message);
        }


        //100ms 10hz
        if (loop_count % 10 == 0)
        {
            ////payload_gb_heartbeat_v1_t *hb = get_gimbal_heartbeat();
            handle_gimbal_attitude_message_report(hb.joint_outter_angle, 
                                            hb.joint_inner_angle, 
                                            hb.joint_middle_angle, 
                                            hb.gb_yaw,
                                            hb.gb_roll,
                                            hb.gb_pitch,
                                            hb.imu_w_z,
                                            hb.imu_w_y,
                                            hb.imu_w_x
                                                );
            
        }
        

        //200ms 5hz
        if (loop_count % 20 == 0)
        {
            dronepod_status_t *status = get_dronepod_status();
            //status->visible_camera_status.zoom_status = 0;
            status->visible_camera_status.focal_length = get_focal_length();
            status->visible_camera_status.hybrid_zoom_ratio = get_real_zoom_factor() * 10;
            handle_visible_status_report(&status->visible_camera_status);

            //status->ir_camera_status.zoom_status = 0;
            handle_ir_status_report(&status->ir_camera_status);

            if (status->laser_switch == 1)
            {
                
            }
            
        }

        //1s 1hz
        if (loop_count % 100 == 0)
        {
            dronepod_status_t *status = get_dronepod_status();
            handle_cam_sys_status_report(&status->camera_system_status);
        }
        

        //5s 
        if(loop_count % 500 == 0)
        {
            dronepod_status_t *status = get_dronepod_status();
            SDCardInfo sdcard;
            if (get_sdcard_info(&sdcard) == 0) 
            {
                if (sdcard.is_mounted) 
                {
                    status->camera_system_status.sd_card_status = 0;
                    status->camera_system_status.sd_total_capacity = sdcard.total_mb / 10;
                    status->camera_system_status.sd_remaining_capacity = sdcard.free_mb /10;
                    status->camera_system_status.sd_used_capacity = sdcard.used_mb / 10;
                    // if (sdcard.free_mb < 10)
                    // {
                    //     status->camera_system_status.sd_card_status = 4;
                    // }
                    // printf("SD卡已挂载\n");
                    // printf("总容量: %lu MB\n", sdcard.total_mb);
                    // printf("可用空间: %lu MB\n", sdcard.free_mb);
                    // printf("已用空间: %lu MB\n", sdcard.used_mb);
                    
                } else {
                    status->camera_system_status.sd_card_status = 3;
                    //printf("SD卡未插入或未挂载\n");
                }
            } else {
                status->camera_system_status.sd_card_status = 1;
                //printf("获取SD卡信息失败\n");
            }


        }
    }

}



int start_report_message_proc()
{
    pthread_t tracker_comm_thread_id1;
    pthread_t send_track_detect_message_to_fcs_thread_id1;
    pthread_create(&send_track_detect_message_to_fcs_thread_id1, 0, send_track_detect_message_to_fcs_proc, NULL);
    return pthread_create(&tracker_comm_thread_id1, 0, send_other_message_to_fcs_proc, NULL);
}


void stop_report_message_proc()
{
    g_exit_thread = 1;
}


void set_ai_result(ai_result_t* ai_result)
{
    pthread_mutex_lock(&ai_buffer_lock);
    memcpy(&g_ai_buffer, ai_result, sizeof(ai_result_t));
    pthread_mutex_unlock(&ai_buffer_lock);
}



void set_gimbal_status(payload_gb_heartbeat_v1_t *payload)
{
    memcpy(&hb, payload, sizeof(payload_gb_heartbeat_v1_t));
    dronepod_status_t *status = get_dronepod_status();
    // status->pitch_angle =  hb.gb_pitch;
    // status->yaw_angle =  hb.gb_yaw; 
    status->pitch_angle = (float)hb.gb_pitch / 100.0f * 10.0;
    status->yaw_angle = (float)hb.gb_yaw / 100.0f * 10.0;
}