#include "report_message.h"
#include "dronepod_k40t_protocol.h"
#include "dronepod_status.h"
#include "zoom.h"
#include "osd_message.h"

#define LOG_LEVEL LOG_LEVEL_ERROR
#include "log_utils.h"
static int g_exit_thread = 0;
static clock_t pre_frame_clock_t = 0, current_frame_clock_t;
static ai_result_t g_ai_buffer = {0};
pthread_mutex_t ai_buffer_lock = PTHREAD_MUTEX_INITIALIZER;

// enum trackState
// {
//     TRACKER_TRACKING = 1,
//     TRACKER_FINDING = 2,
//     TRACKER_LOST = 3,
//     TRACKER_NOT_UPDATED = 4
// };
// Payload_Camera_System_Status_0x000003_t g_camera_sys_status = {0};
// Payload_IR_Camera_Status_0x000004_t g_ir_camera_status = {0};
// Payload_VL_Camera_Status_0x000005_t g_visible_camera_status = {0};
payload_gb_heartbeat_v1_t hb = {0};

void *send_track_detect_message_to_fcs_proc(void *arg) 
{

    char proc_name[32] = "trackdetectfcs";
    prctl(PR_SET_NAME, proc_name, 0, 0, 0);

    int ret = -1;
    int client_sock = -1;

    // extern char *g_strConfigIni;
    //float d_send_time = 33.3;
    float d_send_time = 40;
    // int FPS = GetIniKeyInt("serial", "sendfps", g_strConfigIni);
    // if (FPS != 0)
    //     d_send_time = 1000.0 / (float)FPS;
    // airvisen_trace("delta send time: %f\n", d_send_time);

    //g_track_result.short_lens_divisor = 0.3;
    //g_track_result.long_lens_divisor = 0.2;

    while (!g_exit_thread)
    {
        // printf("time(ns):%ld, command send count: %ld\n", get_sys_uptime_in_ns(), g_SendCount);
        // usleep(3000);
        //< send nn result out from serial/net

        static double total_t = 0;
        current_frame_clock_t = clock();
        total_t = (double)(current_frame_clock_t - pre_frame_clock_t) / CLOCKS_PER_SEC * 1000;

        if (total_t < d_send_time)
        {
            int sleep_us = (int)((d_send_time - total_t) * 1000);
            // printf("will sleep %d us\n", sleep_us);
            usleep(sleep_us);
        }
        pre_frame_clock_t = clock();

        // float divisor = g_track_result.short_lens_divisor;
        // if (g_track_result.algo_trakcer_source == 1)
        // {
        //     divisor = g_track_result.long_lens_divisor;
        // }
        //
        //cv::Rect tempRect = g_SendRect;
        // int x = (g_track_result.x - 960) * divisor + 960;
        // int y = (g_track_result.y - 540) * divisor + 540;
        // int width = g_track_result.width * divisor;
        // int height = g_track_result.height * divisor;

        // handle_state_ai_tracking(x, y, width, height, 100, 0);

        ai_result_t tmp_buffer = {0};
        pthread_mutex_lock(&ai_buffer_lock);
        memcpy(&tmp_buffer, &g_ai_buffer, sizeof(ai_result_t));
        pthread_mutex_unlock(&ai_buffer_lock);

        if (tmp_buffer.type == 0)
        {
           // 检测结果处理
            int total_detections = tmp_buffer.detect_result_num;
            int max_per_packet = 24; // 每包最多24个检测结果
            int num_packets = (total_detections + max_per_packet - 1) / max_per_packet; // 向上取整计算包数
            
            if(num_packets > 0){
                //printf("发送检测结果，共 %d 个目标，分 %d 包发送\n", total_detections, num_packets);
                LOG_D("发送检测结果，共 %d 个目标，分 %d 包发送\n", total_detections, num_packets);
            }
            
            for (int packet = 0; packet < num_packets; packet++)
            {
                int start_idx = packet * max_per_packet;
                int end_idx = (packet + 1) * max_per_packet;
                if (end_idx > total_detections) {
                    end_idx = total_detections;
                }
                
                int current_packet_size = end_idx - start_idx;
                LOG_D("发送第 %d 包，包含 %d 个目标 (索引 %d 到 %d)\n", 
                       packet + 1, current_packet_size, start_idx, end_idx - 1);
                
                // 创建状态帧结构体
                StatusFrame_AI_Recognition_0x000319_t status_frame = {0};
                
                // 设置包信息：高4位为总包数，低4位为当前包ID
                status_frame.packet_info = ((num_packets & 0x0F) << 4) | ((packet + 1) & 0x0F);
                
                // 设置检测状态：0x00表示成功
                status_frame.detect_status = 0x00;                
                
                // 复制当前包的目标数据
                for (int i = 0; i < current_packet_size; i++)
                {
                    memcpy(&status_frame.targets[i], &tmp_buffer.detect_result[start_idx + i], sizeof(AI_Target_Data_t));
                }
                
                // 调用handle_state_ai_detection发送当前包
                handle_state_ai_detection(&status_frame);
                
                // 如果有多个包，在包之间添加一些延迟，避免发送过快
                if (num_packets > 1 && packet < num_packets - 1) {
                    usleep(50); // 5ms延迟
                }
            }

        }
        else
        {
            handle_state_ai_tracking(
                g_ai_buffer.track_result.top_left_x,
                g_ai_buffer.track_result.top_left_y, 
                g_ai_buffer.track_result.width, 
                g_ai_buffer.track_result.height, 
                100, 
                1);
        }
    

    }

}

void *send_other_message_to_fcs_proc(void *arg) 
{

    pthread_setname_np(pthread_self(), "sendtofcs");
    char proc_name[32] = "sendtofcs";
    prctl(PR_SET_NAME, proc_name, 0, 0, 0);

    int ret = -1;
    int client_sock = -1;
    unsigned int loop_count = 0;

    // extern char *g_strConfigIni;
    float d_send_time = 10;
    // int FPS = GetIniKeyInt("serial", "sendfps", g_strConfigIni);
    // if (FPS != 0)
    //     d_send_time = 1000.0 / (float)FPS;
    // airvisen_trace("delta send time: %f\n", d_send_time);

    //g_track_result.short_lens_divisor = 0.3;
    //g_track_result.long_lens_divisor = 0.2;

    while (!g_exit_thread)
    {
        // printf("time(ns):%ld, command send count: %ld\n", get_sys_uptime_in_ns(), g_SendCount);
        // usleep(3000);
        //< send nn result out from serial/net
        loop_count++;
        static double total_t = 0;
        current_frame_clock_t = clock();
        total_t = (double)(current_frame_clock_t - pre_frame_clock_t) / CLOCKS_PER_SEC * 1000;

        if (total_t < d_send_time)
        {
            int sleep_us = (int)((d_send_time - total_t) * 1000);
            // printf("will sleep %d us\n", sleep_us);
            usleep(sleep_us);
        }
        pre_frame_clock_t = clock();

        //20ms
        if (loop_count % 2 == 0)
        {
            osd_message_s osd_message;
            osd_message.obj_lontitude = 225430990;
            osd_message.obj_latitude = 1140578680;
            osd_message.obj_altitude = 200;
            osd_message.obj_distance = 688;
            osd_message.plane_lontitude = 225430980;
            osd_message.plane_latitude = 1140578670;
            osd_message.plane_altitude = 9000;
            osd_message.track_state = 0;
            osd_message.zoom_factor = 100;
            osd_message.pitch_angle = -110;
            osd_message.yaw_angle = -132;
            airvisen_update_osd_request(&osd_message);
        }


        //100ms 10hz
        if (loop_count % 10 == 0)
        {
            ////payload_gb_heartbeat_v1_t *hb = get_gimbal_heartbeat();
            handle_gimbal_attitude_message_report(hb.joint_outter_angle, 
                                            hb.joint_inner_angle, 
                                            hb.joint_middle_angle, 
                                            hb.gb_yaw,
                                            hb.gb_roll,
                                            hb.gb_pitch,
                                            hb.imu_w_z,
                                            hb.imu_w_y,
                                            hb.imu_w_x
                                                );
            
        }
        

        //200ms 5hz
        if (loop_count % 20 == 0)
        {
            dronepod_status_t *status = get_dronepod_status();
            status->visible_camera_status.zoom_status = 0;
            status->visible_camera_status.focal_length = get_focal_length();
            status->visible_camera_status.hybrid_zoom_ratio = get_real_zoom_factor();
            handle_visible_status_report(&status->visible_camera_status);

            //status->ir_camera_status.zoom_status = 0;
            handle_ir_status_report(&status->ir_camera_status);
        }

        //1s 1hz
        if (loop_count % 100 == 0)
        {
            dronepod_status_t *status = get_dronepod_status();
            handle_cam_sys_status_report(&status->camera_system_status);
        }
        
        //1s 1hz
        if (loop_count % 100 == 0)
        {
            ZHE
        }
    }

}



int start_report_message_proc()
{
    pthread_t tracker_comm_thread_id1;
    pthread_t send_track_detect_message_to_fcs_thread_id1;
    pthread_create(&send_track_detect_message_to_fcs_thread_id1, 0, send_track_detect_message_to_fcs_proc, NULL);
    return pthread_create(&tracker_comm_thread_id1, 0, send_other_message_to_fcs_proc, NULL);
}


void stop_report_message_proc()
{
    g_exit_thread = 1;
}


void set_ai_result(ai_result_t* ai_result)
{
    pthread_mutex_lock(&ai_buffer_lock);
    memcpy(&g_ai_buffer, ai_result, sizeof(ai_result_t));
    pthread_mutex_unlock(&ai_buffer_lock);
}



void set_gimbal_status(payload_gb_heartbeat_v1_t *payload)
{
    memcpy(&hb, payload, sizeof(payload_gb_heartbeat_v1_t));
}