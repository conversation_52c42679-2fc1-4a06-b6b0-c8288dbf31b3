#include "config_tool.h"
#include "configparser.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h> 

extern char *g_strConfigIni;

int g_long_lens_dx;
int g_long_lens_dy;

int execute_command(const char *command) {
    int ret = system(command);
    if (ret == -1) {
        perror("system");
        return -1;
    }
    int exit_status = WEXITSTATUS(ret);
    
    if (exit_status == 0) {
        printf("Command executed successfully.\n");
        return 0;
    } else {
        printf("Command failed with exit status: %d\n", exit_status);
        return exit_status;
    }
}


int sync_config()
{
    char cmd[256] = {0};
    sprintf(cmd, "ql_save_config.sh\n");
    return execute_command(cmd);
}


/// @brief 
/// @param in_path 
/// @param out_path note:!! out_path LEN >= MAX_FILE_NAME_LEN
/// @return 
int get_real_config_path(const char *in_path, char *out_path)
{
    struct stat statbuf;

    // read file info
    if (lstat(in_path, &statbuf) == -1) {
        perror("lstat");
        exit(EXIT_FAILURE);
    }else{

	}

    // detect file type
    if (S_ISLNK(statbuf.st_mode)) {
        printf("%s is a symbolic link.\n", in_path);
		
		char buf[256] = {0};
		ssize_t len;

		len = readlink(in_path, buf, sizeof(buf) - 1);
		if (len != -1) {
			buf[len] = '\0'; 
			memcpy(out_path, buf, len);
			printf("Symbolic link '%s' points to '%s'\n", in_path, buf);
		} else {
			perror("readlink");
			return 1;
		}

    } else if (S_ISREG(statbuf.st_mode)) {
        printf("%s is a regular file.\n", in_path);
		memcpy(out_path, in_path, strlen(in_path));
    } else {
        printf("%s is of some other type.\n", in_path);
    }

}


int save_long_focal_lens_x_offset(int x)
{
    char buf[FILENAME_MAX] = {0};
    get_real_config_path(g_strConfigIni, buf);
    int ret = PutIniKeyInt("tracking", "x_long_lens_offset", x, buf);
    if(ret == -1){
        printf("save long focal lens offset x failed\n");
    }
    else{
        sync_config();
    }
}



int save_long_focal_lens_y_offset(int y)
{
    char buf[FILENAME_MAX] = {0};
    get_real_config_path(g_strConfigIni, buf);
    int ret = PutIniKeyInt("tracking", "y_long_lens_offset", y, buf);
    if(ret == -1){
        printf("save long focal lens offset y failed\n");
    }else{
        sync_config();
    }
    
}




int add_long_focal_lens_x_offset()
{
    g_long_lens_dx += 2;
    save_long_focal_lens_x_offset(g_long_lens_dx);
}


int sub_long_focal_lens_x_offset()
{
    g_long_lens_dx -= 2;
    // if(g_long_lens_dx > 16){
    //     g_long_lens_dx -= 16;
    // }
    // else{
    //     g_long_lens_dx = 0;
    // }
    save_long_focal_lens_x_offset(g_long_lens_dx);
}


int add_long_focal_lens_y_offset()
{
    g_long_lens_dy += 1;
    save_long_focal_lens_y_offset(g_long_lens_dy);
}


int sub_long_focal_lens_y_offset()
{
    g_long_lens_dy -= 1;
    // if(g_long_lens_dy > 1){
    //     g_long_lens_dy -= 1;
    // }
    // else{
    //     g_long_lens_dy = 0;
    // }
    save_long_focal_lens_y_offset(g_long_lens_dy);
}


int get_long_focal_lens_x_offset()
{
    return g_long_lens_dx;
}

int get_long_focal_lens_y_offset()
{
    return g_long_lens_dy;;
}



void set_long_focal_lens_x_offset(int dx)
{
    g_long_lens_dx = dx;
}

void set_long_focal_lens_y_offset(int dy)
{
    g_long_lens_dy = dy;
}