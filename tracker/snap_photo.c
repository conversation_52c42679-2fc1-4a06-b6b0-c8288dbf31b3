

#include <sys/select.h>
#include <string.h>
#include <stdlib.h>
#include "snap_photo.h"
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdio.h>
#include <time.h>
#include <sys/time.h>
#include <string.h>
#include <errno.h>
#include <limits.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>
#include <errno.h>
#include <limits.h>
#include <fcntl.h>
#include <unistd.h>



static  int need_snap_photo = 0;
FILE* create_image_file_fp( const char *path, const char *prefix, int chnid) {
    /* 参数有效性检查 */
    if (!path || !prefix) {
        errno = EINVAL;
        return NULL;
    }

    /* 时间戳生成 */
    struct timeval tv;
    struct tm tm_time;
    char time_buf[17] = {0};
    
    if (gettimeofday(&tv, NULL) != 0) {
        perror("Get precise time failed");
        return NULL;
    }
    
    if (!localtime_r(&tv.tv_sec, &tm_time)) {
        perror("Convert local time failed");
        return NULL;
    }

    /* 生成16位时间字符串 (YYYYMMDDHHMMSSMM) */
    strftime(time_buf, 15, "%Y%m%d%H%M%S", &tm_time);
    snprintf(time_buf + 14, 3, "%02d", (int)(tv.tv_usec / 10000));

    /* 构造完整路径 */
    char fullpath[PATH_MAX * 2] = {0};
    int sep_needed = (path[strlen(path)-1] != '/') ? 1 : 0;
    
    snprintf(fullpath, sizeof(fullpath), 
            "%s%s%s%s.jpg", 
            path,
            sep_needed ? "/" : "",
            prefix,
            time_buf);

    /* 原子化创建文件 */
    printf("snap_photo_take: fullpath=%s\n", fullpath);
    FILE* fp = fopen(fullpath, "wbx");  // C11独占模式
    if (!fp) {
        /* 处理旧标准兼容 */
        if (access(fullpath, F_OK) == 0) {
            errno = EEXIST;
        }
        perror("Create file failed");
        return NULL;
    }

    /* 显式设置文件权限 */
    if (fchmod(fileno(fp), 0644) != 0) {
        perror("Set permissions failed");
        fclose(fp);
        return NULL;
    }

    return fp;
}



static vs_int32_t venc_stream_data_write(snap_thread_param_s *p_task_param, vs_venc_stream_s *p_stream, vs_bool_t *p_end, vs_int32_t chn_index)
{
	vs_uint32_t i = 0, j = 0, wr_len = 0;

	for (i = 0; i < p_stream->pack_num ; i++) {
		if (0 == p_stream->p_pack[i].length) {
			continue;
		}

		if (VS_TRUE == p_stream->p_pack[i].is_frame_end) {
			*p_end = VS_TRUE;
		}

		switch (p_task_param->chn_attr[chn_index].enc_attr.stream_mode) {
		case E_VENC_STREAM_MODE_STREAM:
            printf("come to case:E_VENC_STREAM_MODE_STREAM!\n");
            fwrite(p_stream->p_pack[i].virt_addr, 1, p_stream->p_pack[i].length, p_task_param->p_out_file[chn_index]);
            fflush(p_task_param->p_out_file[chn_index]);
			break;
		case E_VENC_STREAM_MODE_FRAME:
            printf("come to case:E_VENC_STREAM_MODE_FRAME!\n");
            wr_len = 0;
            for (j = 0; j < p_stream->p_pack[i].data_num; j++) {
        		fwrite(p_stream->p_pack[i].virt_addr + p_stream->p_pack[i].pack_info[j].pack_offset, 1,
                   p_stream->p_pack[i].pack_info[j].pack_len, p_task_param->p_out_file[chn_index]);
        		wr_len += p_stream->p_pack[i].pack_info[j].pack_len;
        	}
            fwrite(p_stream->p_pack[i].virt_addr + p_stream->p_pack[i].offset, 1,
                   p_stream->p_pack[i].length - wr_len, p_task_param->p_out_file[chn_index]);
            fflush(p_task_param->p_out_file[chn_index]);
			break;
		case E_VENC_STREAM_MODE_NALU:
            printf("come to case:E_VENC_STREAM_MODE_NALU!\n");
            fwrite(p_stream->p_pack[i].virt_addr, 1, p_stream->p_pack[i].length, p_task_param->p_out_file[chn_index]);
            fflush(p_task_param->p_out_file[chn_index]);
			break;
		default:
            vs_sample_trace(" chn[%d] stream_mode=%d not support!\n",
                            p_task_param->venc_chnid[chn_index], p_task_param->chn_attr[chn_index].enc_attr.stream_mode);
			break;
		}
	}

	return VS_SUCCESS;
}


static vs_void_t *snap_photo_proc(vs_void_t *arg)
{
    pthread_setname_np(pthread_self(), "snapphoto");
    printf("snap_photo_proc Thread ID: %ld\n", pthread_self());
    snap_thread_param_s *p_task_param = (snap_thread_param_s *)arg;
    
    vs_int32_t ret = VS_SUCCESS;
	vs_int32_t chn_index = 0;
	vs_int32_t chn_num_started = 0;
    //	vs_int32_t stoped_chn = 0;
	vs_int32_t venc_fd[VENC_MAX_CHN_NUM], max_venc_fd = 0;
    vs_uint32_t frame_count[VENC_MAX_CHN_NUM] = {0};
	vs_bool_t frame_end = VS_FALSE;
	struct timeval timeout_val;
	fd_set read_fds;
    //vs_int32_t received_frame_num[VENC_MAX_CHN_NUM] = {0};
	struct vs_venc_chn_status chn_status;
	vs_venc_stream_s out_stream;

    unsigned int venc_chn = 2;
    
    sleep(1);

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    for(chn_index = 0; chn_index < p_task_param->chn_num; chn_index++) 
    {
		venc_fd[chn_index] = vs_mal_venc_chn_fd_get(p_task_param->venc_chnid[chn_index]);
		if (venc_fd[chn_index] <= 0) 
        {
			vs_sample_trace(" chn[%d] vs_mal_venc_chn_fd_get get error\n", p_task_param->venc_chnid[chn_index]);
			return VS_NULL;
		}

        //printf("chn_index:%d, venc_fd:%d\n", chn_index, venc_fd[chn_index]);
		max_venc_fd = (max_venc_fd > venc_fd[chn_index]) ? max_venc_fd : venc_fd[chn_index];
		chn_num_started++;

        ret = vs_mal_venc_chn_attr_get(p_task_param->venc_chnid[chn_index], &p_task_param->chn_attr[chn_index]);
        if (ret != VS_SUCCESS) 
        {
            vs_sample_trace("sample_common_vpp_bind_venc failed, ret[0x%x]\n", ret);
            return VS_NULL;
        }
	}

    chn_index = 0;

    while (VS_TRUE != p_task_param->stop_stream_task) 
    {
        
        // if (p_task_param->capture_jpeg != VS_TRUE)
        // {
        //     usleep(300000);
        //     continue;
        // }
        // else{   
        //     //p_task_param->capture_jpeg = VS_FALSE;
        // }
        
        FD_ZERO(&read_fds);
        FD_SET(venc_fd[chn_index], &read_fds);

        if (FD_ISSET(venc_fd[chn_index], &read_fds)) 
        {
            //printf("snap_photo_proc: p_task_param->capture_jpeg=%d\n", p_task_param->capture_jpeg);
            ret = vs_mal_venc_chn_status_get(venc_chn, &chn_status);
            if (VS_SUCCESS != ret || (chn_status.left_stream_frame_num < 1)) 
            {
                //printf("snap_photo_proc: vs_mal_venc_chn_status_get failed, ret[0x%x]");
                usleep(20*1000);
                continue;
            }

            memset(&out_stream, 0, sizeof(vs_venc_stream_s));
            out_stream.p_pack = (vs_venc_pack_s *)calloc(1, sizeof(vs_venc_pack_s) * chn_status.cur_pack_num);
            if (VS_NULL == out_stream.p_pack) 
            {
                vs_sample_trace(" chn[%d] no memory for out_stream.p_pack len[%lu] error!!! \n",
                    venc_chn, sizeof(vs_venc_pack_s) * chn_status.cur_pack_num);
                p_task_param->stop_stream_task = VS_TRUE;
                continue;
            }

            printf("snap_photo_proc: vs_mal_venc_stream_acquire  ----1---- venc_chn : %d\n", venc_chn);

            out_stream.pack_num = chn_status.cur_pack_num;
            ret = vs_mal_venc_stream_acquire(venc_chn, &out_stream, -1);
            if (VS_SUCCESS != ret || 0 == out_stream.pack_num) 
            {
                if(VS_NULL != out_stream.p_pack) 
                {
                    free(out_stream.p_pack);
                    out_stream.p_pack = VS_NULL;
                }
                continue;
            } 

            printf("snap_photo_proc: vs_mal_venc_stream_acquire  ----2----\n");

            char *store_file_name = NULL;
            /**frame counter + 1 */
            frame_count[chn_index] += 1;
            frame_end = VS_FALSE;

            /* store jpeg per p_task_param->jpeg_store_per_frame frame*/
            //if (p_task_param->capture_jpeg == VS_TRUE)
            //{
                //just capture once

            printf("snap_photo_proc: vs_mal_venc_stream_acquire  ----3----\n");

            p_task_param->capture_jpeg = VS_FALSE;
            p_task_param->p_out_file[chn_index] = create_image_file_fp(p_task_param->store_path, p_task_param->file_prefix, chn_index);
            if(p_task_param->p_out_file[chn_index] != NULL) {
                //p_task_param->p_out_file[chn_index] = create_image_file_fp(p_task_param->store_path, p_task_param->file_prefix, chn_index);
                venc_stream_data_write(p_task_param, &out_stream, &frame_end, chn_index);
                fclose(p_task_param->p_out_file[chn_index]);
                p_task_param->p_out_file[chn_index] = VS_NULL;
            }
            else
            {
                printf("create image file failed\n");
            }
            printf("snap_photo_proc: vs_mal_venc_stream_acquire  ----4----\n");
            //}
            // else
            // {
            //     vs_sample_trace("unexpert switch\n");
            // }

            vs_mal_venc_stream_release(venc_chn, &out_stream);
            if(VS_NULL != out_stream.p_pack) 
            {
                free(out_stream.p_pack);
                out_stream.p_pack = VS_NULL;
            }

            if (VS_TRUE == frame_end) 
            {
                printf("frame end!\n");
            }

            //vs_sample_trace("chn[%d] rcv frame[%d] \n", venc_chnid, received_frame_num);
        }
        else
        {
            usleep(300000);
        }

    }

    vs_mal_venc_chn_fd_close(venc_chn);
    if (VS_NULL != p_task_param->p_out_file[chn_index]) 
    {
        fclose(p_task_param->p_out_file[chn_index]);
        p_task_param->p_out_file[chn_index] = VS_NULL;
    }

    return VS_NULL;
}




void photo_snap_process(snap_thread_param_s *params)
{
    pthread_t snap_photo_id;
    if (0 != pthread_create(&snap_photo_id,0, (void *(*)(void *))snap_photo_proc, params))
    {
        printf("create digital_zoom thread failed!\n");
        return;
    }
}


static snap_thread_param_s params = {0};
int snap_photo_init()
{
    //snap_thread_param_s params = {0};
    params.capture_jpeg = true;
    params.chn_num = 1;
    sprintf(params.file_prefix, "%s", "dronepod");
    sprintf(params.store_path, "%s", "/tmp/");
    params.venc_chnid[0] = 2;
    params.venc_chnid[1] = 1;
    params.venc_chnid[2] = 3;
    photo_snap_process(&params);

}


int snap_photo_take_once()
{
    //params.capture_jpeg = true;
    printf("handle_photo\n");
    need_snap_photo = 1;
}



bool get_snap_photo_status()
{
    if (need_snap_photo)
    {
        need_snap_photo = 0;
        return true;
    }
    return false;

}