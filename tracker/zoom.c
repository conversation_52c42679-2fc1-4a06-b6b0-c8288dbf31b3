#include "zoom.h"
#include "algorithm_message.h"
#include "gimbal_protocol.h"
#include "report_message.h"
#include "dronepod_status.h"

//= 1.0
static int visible_zoom_factor = MIN_ZOOM_FACTOR;

static int max_zoom_factor = MAX_ZOOM_FACTOR;
static int min_zoom_factor = MIN_ZOOM_FACTOR;


#define IR_MIN_ZOOM_FACTOR 100
#define IR_MAX_ZOOM_FACTOR 800
static int ir_zoom_factor = IR_MIN_ZOOM_FACTOR;

//0 short lens, 1 long lens , 2 ir lens
static int sensor_type = 0;

//上位机设置的图像输出模式
static int video_type = 0;
static int need_refresh_factor = 0;
IMAGE_MODE_E pip_mode = E_4K_FULL_SCREEN;


void set_min_zoom_factor(int factor)
{
    min_zoom_factor = factor;
}


void set_max_zoom_factor(int factor)
{
    max_zoom_factor = factor;
}


float get_zoom_factor()
{
    return visible_zoom_factor/100.0;
}


float get_zoom_factor_ex()
{
    if (sensor_type == 2)
    {
        return ir_zoom_factor/100.0;
    }
    else
    {
        return visible_zoom_factor/100.0;
    }
}



//0.01 step 
void add_zoom_factor_1x()
{
    if (sensor_type == 2)
    {
        ir_zoom_factor = ir_zoom_factor + 1 < IR_MAX_ZOOM_FACTOR ? ir_zoom_factor + 1 : IR_MAX_ZOOM_FACTOR;
    }
    else
    {
        visible_zoom_factor = visible_zoom_factor + 1 < max_zoom_factor ? visible_zoom_factor + 1 : max_zoom_factor;
        set_gimbal_speed_factor(visible_zoom_factor);
    } 
}

//real 0.01 step
void sub_zoom_factor_1x()
{
    if (sensor_type == 2)
    {
        ir_zoom_factor = ir_zoom_factor - 1 > IR_MIN_ZOOM_FACTOR ? ir_zoom_factor - 1 : IR_MIN_ZOOM_FACTOR;
    }
    else
    {
        visible_zoom_factor = visible_zoom_factor - 1 > min_zoom_factor ? visible_zoom_factor - 1 : min_zoom_factor;
        set_gimbal_speed_factor(visible_zoom_factor);
    }
}


//0.1 step 
void add_zoom_factor_10x()
{
    if (sensor_type == 2)
    {
        ir_zoom_factor = ir_zoom_factor + 10 < IR_MAX_ZOOM_FACTOR ? ir_zoom_factor + 10 : IR_MAX_ZOOM_FACTOR;
    }
    else{
        visible_zoom_factor = visible_zoom_factor + 10 < max_zoom_factor ? visible_zoom_factor + 10 : max_zoom_factor;
        set_gimbal_speed_factor(visible_zoom_factor);
    }
        
}

//real 0.1 step
void sub_zoom_factor_10x()
{
    if (sensor_type == 2)
    {
        ir_zoom_factor = ir_zoom_factor - 10 > IR_MIN_ZOOM_FACTOR ? ir_zoom_factor - 10 : IR_MIN_ZOOM_FACTOR;
    }
    else{
        visible_zoom_factor = visible_zoom_factor - 10 > min_zoom_factor ? visible_zoom_factor - 10 : min_zoom_factor;
        set_gimbal_speed_factor(visible_zoom_factor);
    }
}


//1 step 
void add_zoom_factor_100x()
{

    if (sensor_type == 2)
    {
        ir_zoom_factor  = ir_zoom_factor + 100 < IR_MAX_ZOOM_FACTOR ? ir_zoom_factor  + 100: IR_MAX_ZOOM_FACTOR;
    }
    else
    {
        visible_zoom_factor = visible_zoom_factor + 100 < max_zoom_factor ? visible_zoom_factor + 100 : max_zoom_factor;
        set_gimbal_speed_factor(visible_zoom_factor);
    }  
}

//real 1 step
void sub_zoom_factor_100x()
{
    if (sensor_type == 2)
    {
        ir_zoom_factor = ir_zoom_factor - 100 > IR_MIN_ZOOM_FACTOR ? ir_zoom_factor - 100 : IR_MIN_ZOOM_FACTOR;
    }
    else
    {
        visible_zoom_factor = visible_zoom_factor - 100 > min_zoom_factor ? visible_zoom_factor - 100 : min_zoom_factor;
        set_gimbal_speed_factor(visible_zoom_factor);
    }
}


int set_zoom_factor_to_default()
{
    if (sensor_type == 2)
    {
        ir_zoom_factor = IR_MIN_ZOOM_FACTOR;
    }
    else{
        visible_zoom_factor = min_zoom_factor;
        set_gimbal_speed_factor(visible_zoom_factor);
    }
    return 0;
}

void set_zoom_factor(int factor)
{
    if (sensor_type == 2)
    {
        if (factor >= IR_MIN_ZOOM_FACTOR && factor <= IR_MAX_ZOOM_FACTOR)
        {
            ir_zoom_factor = factor;
        }
    }
    else
    {
        if (factor >= min_zoom_factor && factor <= max_zoom_factor)
        {
            visible_zoom_factor = factor;
            set_gimbal_speed_factor(visible_zoom_factor);
        }
    }

    update_vo_mode();
    need_refresh_factor = 1;
}




float get_real_zoom_factor()
{
    if (sensor_type == 2)
    {
        return ir_zoom_factor/100.0;
    }
    else
    {
        if(visible_zoom_factor < DUAL_LIGHT_SWITCH_FACTOR_INFLAT)
        {
            return visible_zoom_factor/100.0;
        }else{
            int _factor = visible_zoom_factor - LONG_FOCAL_BASE_ZOOM_FACTOR_INFLAT + 100;
            return _factor/100.0;
        }
    }
}


int get_ir_zoom_facotr()
{
    return ir_zoom_factor;
}

int get_focal_length()
{
    if (sensor_type == 2)
    {
        return 1300;
    }
    else
    {
        if(visible_zoom_factor < DUAL_LIGHT_SWITCH_FACTOR_INFLAT)
        {
            return 800;
        }else{
            return 2500;
        }
    }
    
}



int get_input_visible_sensor_type()
{
    if(visible_zoom_factor < DUAL_LIGHT_SWITCH_FACTOR_INFLAT)
    {
        return USE_SHORT_FOCAL_SENSOR;
    }else{
        return USE_LONG_FOCAL_SENSOR;
    }
}


void set_sensor_type(int type)
{
    if(sensor_type != type)
    {
        sensor_type = type;
        if (sensor_type == SENSOR_MODE_SHORT_VI)
        {
            set_vii_pipe_framerate(1, 30, 25);
            set_vii_pipe_framerate(0, 30, 30);
        }
        else if (sensor_type == SENSOR_MODE_LONG_VI)
        {
            set_vii_pipe_framerate(0, 30, 25);
            set_vii_pipe_framerate(1, 30, 30);
        }
    }
    //sensor_type = type;
}


int get_sensor_type()
{
    return sensor_type;
}


bool is_zoom_factor_on_4k()
{
    if (sensor_type == 2)
    {
        return false;
    }
    else
    {
        if(visible_zoom_factor >= DUAL_LIGHT_SWITCH_FACTOR_INFLAT)
        {
            if(visible_zoom_factor - LONG_FOCAL_BASE_ZOOM_FACTOR_INFLAT + 100 > 200){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }
    
}



void long_switch_short()
{
    if (sensor_type != 2)
    {    
        visible_zoom_factor = DUAL_LIGHT_SWITCH_FACTOR_INFLAT - 10;
    }
    
}



void short_switch_long()
{
    if (sensor_type != 2)
    {    
        visible_zoom_factor = DUAL_LIGHT_SWITCH_FACTOR_INFLAT;
    }
    
}


void limit_short_lens_zoom_factor()
{
    if (sensor_type != 2)
    {
        set_min_zoom_factor(MIN_ZOOM_FACTOR);
        set_max_zoom_factor(DUAL_LIGHT_SWITCH_FACTOR_INFLAT - 10);
    }
    

}


void limit_long_lens_zoom_factor()
{
    if (sensor_type != 2)
    {
        set_min_zoom_factor(DUAL_LIGHT_SWITCH_FACTOR_INFLAT);
        set_max_zoom_factor(MAX_ZOOM_FACTOR);
    }
    

}


void clear_zoom_factor_limit()
{
    if (sensor_type != 2)
    {
        set_min_zoom_factor(MIN_ZOOM_FACTOR);
        set_max_zoom_factor(MAX_ZOOM_FACTOR);
    }
    

}




//持续放大
static int g_begin_continuous_zoom_switch = 0;
static int g_zoom_type = 1;  //1 zoom in,  2 zoom out
int continuous_zoom()
{
    if (g_begin_continuous_zoom_switch == 0){
        return -1;
    }

    if(g_zoom_type == 1)
    {
        //add_zoom_factor_10x();
        add_zoom_factor_1x();
        add_zoom_factor_1x();
        add_zoom_factor_1x();
        add_zoom_factor_1x();
        add_zoom_factor_1x();
    }
    else
    {
        //sub_zoom_factor_10x();
        sub_zoom_factor_1x();
        sub_zoom_factor_1x();
        sub_zoom_factor_1x();
        sub_zoom_factor_1x();
        sub_zoom_factor_1x();
    }
    need_refresh_factor = 1;
    return 0;
}



int vl_start_continuous_zoom(unsigned int zoom_type)
{
    g_zoom_type = zoom_type;
    printf("come to vl_continuous_zoom!!!\n");
    g_begin_continuous_zoom_switch = 1;
    return 0;
}


int vl_stop_continuous_zoom()
{
    printf("come to vl_stop_continuous_zoom!!!\n");
    g_begin_continuous_zoom_switch = 0;
    return 0;
}


int vl_continuous_zoom_single_step(unsigned int zoom_type)
{
    if(zoom_type == 1)
    {
        add_zoom_factor_1x();
    }
    else
    {
        sub_zoom_factor_1x();
    }
}



/**
 * when change the zoom_factor、video_type、
 * 
 * 
 */
void update_vo_mode()
{
    //printf("come to update_vo_mode!!!\n");
    int sensor_type = SENSOR_MODE_SHORT_VI;
    if (video_type == 0) 
    {
        if(get_input_visible_sensor_type() == USE_SHORT_FOCAL_SENSOR)
        {
            pip_mode = E_4K_FULL_SCREEN;
            sensor_type = SENSOR_MODE_SHORT_VI; 
        }
        else
        {
            pip_mode = E_1080P_FULL_SCREEN;
            sensor_type = SENSOR_MODE_LONG_VI; 
        }
    } 
    else if (video_type == 1) 
    {

        pip_mode = E_INFRA_FULL_SCREEN;
        sensor_type = SENSOR_MODE_IR; 

    } 
    else if (video_type == 2) 
    {

        if(get_input_visible_sensor_type() == USE_SHORT_FOCAL_SENSOR)
        {
            pip_mode = E_4K_PIP_INFRA;
            sensor_type = SENSOR_MODE_SHORT_VI;
        }
        else
        {
            pip_mode = E_1080P_PIP_INFRA;
            sensor_type = SENSOR_MODE_LONG_VI; 
        }

        //mode = E_4K_PIP_INFRA;
        //mode = E_1080P_PIP_INFRA
    }
    else if (video_type== 3)
    {

        if(get_input_visible_sensor_type() == USE_SHORT_FOCAL_SENSOR)
        {
            pip_mode = E_INFRA_PIP_4K;
        }
        else
        {
            pip_mode = E_INFRA_PIP_1080P; 
        }   
        sensor_type = SENSOR_MODE_IR;
    }

    set_sensor_type(sensor_type);

    //printf("\n\n pip_mode: %d airvisen_send_camera_mode\n", pip_mode);
    airvisen_send_camera_mode(sensor_type);

}




void handle_set_vo_mode(int type)
{
    if (type == 0)
    {
        video_type = 1;
        update_vo_mode();
        printf("set vo mode %d\n", video_type);  
    }
    else if (type == 5 || type == 6)
    {
        video_type = 0;
        update_vo_mode();
        printf("set vo mode %d\n", video_type);  
    }
    else if (type == 7)
    {
        video_type = 2;
        update_vo_mode();
        printf("set vo mode %d\n", video_type); 
    }
    else
    {
        printf("not support video type %d\n", type);
    }
}


IMAGE_MODE_E get_pip_mode()
{
    return pip_mode;
}


int get_refresh_status()
{
    if (need_refresh_factor == 1)
    {
        need_refresh_factor = 0;
        return 1;
    }
    else{
        return 0;
    }
}
