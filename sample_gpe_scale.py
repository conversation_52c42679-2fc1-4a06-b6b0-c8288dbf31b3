#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python版本的sample_gpe_scale函数实现
基于原始C++代码的设计和逻辑
"""

import ctypes
import numpy as np
from typing import Optional, Tuple
from dataclasses import dataclass
from enum import IntEnum


# 常量定义
class Constants:
    VS_SUCCESS = 0
    VS_FAILED = -1
    VS_NULL = 0
    VS_TRUE = 1
    VS_FALSE = 0


# 枚举定义
class PixelFormat(IntEnum):
    E_PIXEL_FORMAT_YUV_420SP = 149


class VideoFormat(IntEnum):
    E_VIDEO_FORMAT_LINEAR = 0


class IveDmaMode(IntEnum):
    E_IVE_DMA_MODE_DIRECT_COPY = 0


class GpeFilterType(IntEnum):
    E_GPE_FILTER_TYPE_3_TAP = 0


# 结构体定义
@dataclass
class VideoFrame:
    """视频帧结构体"""
    width: int = 0
    height: int = 0
    stride: list = None  # [stride0, stride1]
    pixel_format: int = PixelFormat.E_PIXEL_FORMAT_YUV_420SP
    video_format: int = VideoFormat.E_VIDEO_FORMAT_LINEAR
    phys_addr: list = None  # [phys_addr0, phys_addr1]
    virt_addr: list = None  # [virt_addr0, virt_addr1]
    
    def __post_init__(self):
        if self.stride is None:
            self.stride = [0, 0]
        if self.phys_addr is None:
            self.phys_addr = [0, 0]
        if self.virt_addr is None:
            self.virt_addr = [0, 0]


@dataclass
class VideoFrameInfo:
    """视频帧信息结构体"""
    frame: VideoFrame = None
    poolid: int = 0
    modid: int = 0
    
    def __post_init__(self):
        if self.frame is None:
            self.frame = VideoFrame()


@dataclass
class TaskAttr:
    """任务属性结构体"""
    src_frame: VideoFrameInfo = None
    dst_frame: VideoFrameInfo = None
    check_only: bool = False
    reserved: list = None
    debug: int = 0
    
    def __post_init__(self):
        if self.src_frame is None:
            self.src_frame = VideoFrameInfo()
        if self.dst_frame is None:
            self.dst_frame = VideoFrameInfo()
        if self.reserved is None:
            self.reserved = [0, 0, 0, 0]


@dataclass
class IveData:
    """IVE数据结构体"""
    stride: int = 0
    width: int = 0
    height: int = 0
    phys_addr: int = 0
    virt_addr: Optional[np.ndarray] = None


@dataclass
class IveDmaCfg:
    """IVE DMA配置结构体"""
    mode: int = IveDmaMode.E_IVE_DMA_MODE_DIRECT_COPY


class MemoryManager:
    """内存管理器 - 模拟MMZ内存分配"""
    
    def __init__(self):
        self.allocated_memory = {}
        self.next_phys_addr = 0x10000000  # 模拟物理地址起始
    
    def malloc(self, zone_name: str, mmb_name: str, size: int) -> Tuple[int, int, np.ndarray]:
        """
        模拟内存分配
        返回: (ret_code, phys_addr, virt_addr_array)
        """
        try:
            # 分配虚拟内存（使用numpy数组模拟）
            virt_addr = np.zeros(size, dtype=np.uint8)
            phys_addr = self.next_phys_addr
            self.next_phys_addr += size
            
            # 记录分配的内存
            self.allocated_memory[phys_addr] = {
                'size': size,
                'virt_addr': virt_addr,
                'zone_name': zone_name,
                'mmb_name': mmb_name
            }
            
            return Constants.VS_SUCCESS, phys_addr, virt_addr
        except Exception as e:
            print(f"内存分配失败: {e}")
            return Constants.VS_FAILED, 0, None
    
    def free(self, phys_addr: int) -> int:
        """释放内存"""
        if phys_addr in self.allocated_memory:
            del self.allocated_memory[phys_addr]
            return Constants.VS_SUCCESS
        return Constants.VS_FAILED


class MockHardwareAPI:
    """模拟硬件API调用"""
    
    @staticmethod
    def vs_mal_gpe_job_start() -> Tuple[int, int]:
        """开始GPE任务"""
        job_handle = 12345  # 模拟句柄
        return Constants.VS_SUCCESS, job_handle
    
    @staticmethod
    def vs_mal_gpe_job_finish(job_handle: int) -> int:
        """完成GPE任务"""
        print(f"GPE任务完成, 句柄: {job_handle}")
        return Constants.VS_SUCCESS
    
    @staticmethod
    def vs_mal_gpe_job_cancel(job_handle: int) -> int:
        """取消GPE任务"""
        print(f"GPE任务取消, 句柄: {job_handle}")
        return Constants.VS_SUCCESS
    
    @staticmethod
    def vs_mal_gpe_scale_task_add(job_handle: int, task_attr: TaskAttr, filter_type: int) -> int:
        """添加缩放任务"""
        print(f"添加缩放任务 - 句柄: {job_handle}, 滤波器类型: {filter_type}")
        print(f"源尺寸: {task_attr.src_frame.frame.width}x{task_attr.src_frame.frame.height}")
        print(f"目标尺寸: {task_attr.dst_frame.frame.width}x{task_attr.dst_frame.frame.height}")
        return Constants.VS_SUCCESS
    
    @staticmethod
    def vs_mal_ive_dma(src_data: IveData, dst_data: IveData, cfg: IveDmaCfg) -> Tuple[int, int]:
        """IVE DMA操作"""
        handle = 54321  # 模拟句柄
        print(f"IVE DMA操作 - 尺寸: {src_data.width}x{src_data.height}, 模式: {cfg.mode}")
        
        # 模拟数据拷贝
        if src_data.virt_addr is not None and dst_data.virt_addr is not None:
            copy_size = min(len(src_data.virt_addr), len(dst_data.virt_addr))
            dst_data.virt_addr[:copy_size] = src_data.virt_addr[:copy_size]
        
        return Constants.VS_SUCCESS, handle
    
    @staticmethod
    def vs_mal_ive_query(handle: int) -> Tuple[int, bool]:
        """查询IVE操作状态"""
        return Constants.VS_SUCCESS, True  # 完成状态


# 全局内存管理器实例
memory_manager = MemoryManager()


def sample_ive_mmz_malloc(zone_name: str, mmb_name: str, size: int) -> Tuple[int, int, np.ndarray]:
    """模拟MMZ内存分配"""
    return memory_manager.malloc(zone_name, mmb_name, size)


def sample_ive_mmz_free(phys_addr: int, virt_addr: np.ndarray, size: int) -> None:
    """模拟MMZ内存释放"""
    if phys_addr != 0 and virt_addr is not None:
        memory_manager.free(phys_addr)


def sample_gpe_scale(zoom_factor: float, frame_info: VideoFrameInfo, 
                    src_virt_addr: np.ndarray, x_offset: int, y_offset: int) -> int:
    """
    GPE缩放函数的Python实现
    
    Args:
        zoom_factor: 缩放因子
        frame_info: 视频帧信息
        src_virt_addr: 源虚拟地址（numpy数组）
        x_offset: X偏移
        y_offset: Y偏移
    
    Returns:
        int: 操作结果 (VS_SUCCESS 或 VS_FAILED)
    """
    print(f"开始GPE缩放操作 - 缩放因子: {zoom_factor}")
    
    # 获取原始图像参数
    image_origin_stride = frame_info.frame.stride[0]
    image_origin_width = frame_info.frame.width
    image_origin_height = frame_info.frame.height
    
    # 计算缩放后的源图像尺寸
    image_scale_src_width = int(frame_info.frame.width / zoom_factor)
    
    # 宽度对齐处理
    if image_scale_src_width % 2 != 0:
        image_scale_src_width += 1
    if (image_scale_src_width // 2) % 2 != 0:
        image_scale_src_width += 2
    
    # 计算高度（保持16:9比例）
    image_scale_src_height = int(image_scale_src_width * 0.5625)
    if image_scale_src_height % 2 != 0:
        image_scale_src_height += 1
    
    print(f"缩放源尺寸: {image_scale_src_width}x{image_scale_src_height}")
    
    # 开始GPE任务
    ret, job_handle = MockHardwareAPI.vs_mal_gpe_job_start()
    if ret != Constants.VS_SUCCESS:
        print(f"GPE任务启动失败: {ret}")
        return Constants.VS_FALSE
    
    # 创建任务属性
    task_attr = TaskAttr()
    
    # 设置源帧属性
    task_attr.src_frame.frame.width = image_scale_src_width
    task_attr.src_frame.frame.height = image_scale_src_height
    task_attr.src_frame.frame.pixel_format = PixelFormat.E_PIXEL_FORMAT_YUV_420SP
    task_attr.src_frame.frame.stride = [image_origin_stride, image_origin_stride]
    task_attr.src_frame.frame.video_format = VideoFormat.E_VIDEO_FORMAT_LINEAR
    
    # 设置目标帧属性（固定为1920x1080）
    task_attr.dst_frame.frame.width = 1920
    task_attr.dst_frame.frame.height = 1080
    task_attr.dst_frame.frame.pixel_format = PixelFormat.E_PIXEL_FORMAT_YUV_420SP
    task_attr.dst_frame.frame.stride = [image_origin_stride, image_origin_stride]
    task_attr.dst_frame.frame.video_format = VideoFormat.E_VIDEO_FORMAT_LINEAR
    
    # 分配临时缓冲区
    resize_size = image_origin_stride * image_origin_height * 2
    ret, resize_phys_addr, p_resize_virt_addr = sample_ive_mmz_malloc(
        None, "resize_dst", resize_size)
    
    if ret != Constants.VS_SUCCESS:
        print("临时缓冲区分配失败")
        MockHardwareAPI.vs_mal_gpe_job_cancel(job_handle)
        return Constants.VS_FAILED
    
    # 计算起始位置
    start_x = image_origin_width // 2 - image_scale_src_width // 2 - x_offset
    start_y = image_origin_height // 2 - image_scale_src_height // 2 - y_offset
    
    # X坐标对齐
    if start_x % 2 != 0:
        start_x += 1
    
    print(f"起始位置: ({start_x}, {start_y})")
    
    # 执行DMA拷贝操作（Y分量）
    dma_cfg = IveDmaCfg()
    
    # 源数据配置
    ive_copy_src_data = IveData(
        stride=image_origin_stride,
        width=image_scale_src_width,
        height=image_scale_src_height,
        phys_addr=frame_info.frame.phys_addr[0] + start_y * image_origin_stride + start_x,
        virt_addr=src_virt_addr[start_y * image_origin_stride + start_x:]
    )
    
    # 目标数据配置
    ive_copy_dst_data = IveData(
        stride=image_origin_stride,
        width=image_scale_src_width,
        height=image_scale_src_height,
        phys_addr=resize_phys_addr,
        virt_addr=p_resize_virt_addr
    )
    
    # 执行Y分量DMA拷贝
    ret, handle = MockHardwareAPI.vs_mal_ive_dma(ive_copy_src_data, ive_copy_dst_data, dma_cfg)
    if ret != Constants.VS_SUCCESS:
        print(f"Y分量DMA拷贝失败: {ret}")
    
    ret, finish = MockHardwareAPI.vs_mal_ive_query(handle)
    if ret != Constants.VS_SUCCESS:
        print(f"Y分量DMA查询失败: {ret}")
    
    # 执行UV分量DMA拷贝
    uv_offset = image_origin_stride * image_origin_height
    ive_copy_src_data.height = image_scale_src_height // 2
    ive_copy_src_data.phys_addr = (frame_info.frame.phys_addr[1] + 
                                  start_y // 2 * image_origin_stride + start_x)
    ive_copy_src_data.virt_addr = src_virt_addr[uv_offset + start_y // 2 * image_origin_stride + start_x:]
    
    ive_copy_dst_data.height = image_scale_src_height // 2
    ive_copy_dst_data.phys_addr = resize_phys_addr + image_scale_src_height * image_origin_stride
    ive_copy_dst_data.virt_addr = p_resize_virt_addr[image_scale_src_height * image_origin_stride:]
    
    ret, handle = MockHardwareAPI.vs_mal_ive_dma(ive_copy_src_data, ive_copy_dst_data, dma_cfg)
    if ret != Constants.VS_SUCCESS:
        print(f"UV分量DMA拷贝失败: {ret}")
    
    ret, finish = MockHardwareAPI.vs_mal_ive_query(handle)
    if ret != Constants.VS_SUCCESS:
        print(f"UV分量DMA查询失败: {ret}")
    
    # 设置任务属性的地址信息
    task_attr.src_frame.frame.phys_addr = [resize_phys_addr, 
                                          resize_phys_addr + image_scale_src_height * image_origin_stride]
    task_attr.src_frame.frame.virt_addr = [int(p_resize_virt_addr.ctypes.data), 
                                          int(p_resize_virt_addr.ctypes.data) + image_scale_src_height * image_origin_stride]
    
    task_attr.dst_frame.frame.phys_addr = frame_info.frame.phys_addr.copy()
    task_attr.dst_frame.frame.virt_addr = [int(src_virt_addr.ctypes.data),
                                          int(src_virt_addr.ctypes.data) + image_origin_stride * image_origin_height]
    
    # 添加缩放任务
    filter_type = GpeFilterType.E_GPE_FILTER_TYPE_3_TAP
    ret = MockHardwareAPI.vs_mal_gpe_scale_task_add(job_handle, task_attr, filter_type)
    if ret != Constants.VS_SUCCESS:
        print(f"添加缩放任务失败: {ret}")
        MockHardwareAPI.vs_mal_gpe_job_cancel(job_handle)
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size)
        return Constants.VS_FAILED
    
    # 完成GPE任务
    ret = MockHardwareAPI.vs_mal_gpe_job_finish(job_handle)
    if ret != Constants.VS_SUCCESS:
        print(f"GPE任务完成失败: {ret}")
        MockHardwareAPI.vs_mal_gpe_job_cancel(job_handle)
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size)
        return Constants.VS_FAILED
    
    # 更新帧信息
    frame_info.frame.width = 1920
    frame_info.frame.height = 1080
    
    # 释放临时缓冲区
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size)
    
    print("GPE缩放操作完成")
    return Constants.VS_SUCCESS


def main():
    """测试函数"""
    print("=== sample_gpe_scale Python版本测试 ===")
    
    # 创建测试数据
    frame_info = VideoFrameInfo()
    frame_info.frame.width = 3840
    frame_info.frame.height = 2160
    frame_info.frame.stride = [3840, 3840]
    frame_info.frame.phys_addr = [0x20000000, 0x20800000]
    
    # 模拟源图像数据
    image_size = frame_info.frame.stride[0] * frame_info.frame.height * 3 // 2  # YUV420SP
    src_virt_addr = np.random.randint(0, 255, image_size, dtype=np.uint8)
    
    # 执行缩放操作
    zoom_factor = 2.0
    x_offset = 0
    y_offset = 0
    
    result = sample_gpe_scale(zoom_factor, frame_info, src_virt_addr, x_offset, y_offset)
    
    if result == Constants.VS_SUCCESS:
        print("✓ 缩放操作成功完成")
        print(f"输出尺寸: {frame_info.frame.width}x{frame_info.frame.height}")
    else:
        print("✗ 缩放操作失败")
    
    print("=== 测试完成 ===")


if __name__ == "__main__":
    main()
