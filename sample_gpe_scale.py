#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python版本的sample_gpe_scale函数实现
基于原始C++代码的设计和逻辑
"""

import ctypes
import array
from typing import Optional, Tuple, Union
from dataclasses import dataclass
from enum import IntEnum


# 基础类型定义
vs_int32_t = ctypes.c_int32
vs_uint32_t = ctypes.c_uint32
vs_uint64_t = ctypes.c_uint64
vs_void_t = ctypes.c_void_p
vs_bool_t = ctypes.c_bool
vs_char_t = ctypes.c_char

# 常量定义
class Constants:
    VS_SUCCESS = 0
    VS_FAILED = -1
    VS_NULL = 0
    VS_TRUE = 1
    VS_FALSE = 0


# 枚举定义
class PixelFormat(IntEnum):
    E_PIXEL_FORMAT_YUV_420SP = 149


class VideoFormat(IntEnum):
    E_VIDEO_FORMAT_LINEAR = 0


class IveDmaMode(IntEnum):
    E_IVE_DMA_MODE_DIRECT_COPY = 0


class GpeFilterType(IntEnum):
    E_GPE_FILTER_TYPE_3_TAP = 0


# ============================================================================
# ctypes结构体定义 - 对应C结构体
# ============================================================================

class VsVideoFrame(ctypes.Structure):
    """对应vs_video_frame_s结构体"""
    _fields_ = [
        ("width", vs_uint32_t),
        ("height", vs_uint32_t),
        ("stride", vs_uint32_t * 2),  # stride[2]
        ("pixel_format", vs_uint32_t),
        ("video_format", vs_uint32_t),
        ("phys_addr", vs_uint64_t * 2),  # phys_addr[2]
        ("virt_addr", vs_void_t * 2),   # virt_addr[2]
    ]

class VsVideoFrameInfo(ctypes.Structure):
    """对应vs_video_frame_info_s结构体"""
    _fields_ = [
        ("frame", VsVideoFrame),
        ("poolid", vs_uint32_t),
        ("modid", vs_uint32_t),
    ]

class VsTaskAttr(ctypes.Structure):
    """对应vs_task_attr_s结构体"""
    _fields_ = [
        ("src_frame", VsVideoFrameInfo),
        ("dst_frame", VsVideoFrameInfo),
        ("check_only", vs_bool_t),
        ("reserved", vs_uint32_t * 4),  # reserved[4]
        ("debug", vs_uint32_t),
    ]

class VsIveData(ctypes.Structure):
    """对应vs_ive_data_s结构体"""
    _fields_ = [
        ("stride", vs_uint32_t),
        ("width", vs_uint32_t),
        ("height", vs_uint32_t),
        ("phys_addr", vs_uint64_t),
        ("virt_addr", vs_void_t),
    ]

class VsIveDmaCfg(ctypes.Structure):
    """对应vs_ive_dma_cfg_s结构体"""
    _fields_ = [
        ("mode", vs_uint32_t),
    ]


# 结构体定义
@dataclass
class VideoFrame:
    """视频帧结构体"""
    width: int = 0
    height: int = 0
    stride: list = None  # [stride0, stride1]
    pixel_format: int = PixelFormat.E_PIXEL_FORMAT_YUV_420SP
    video_format: int = VideoFormat.E_VIDEO_FORMAT_LINEAR
    phys_addr: list = None  # [phys_addr0, phys_addr1]
    virt_addr: list = None  # [virt_addr0, virt_addr1]
    
    def __post_init__(self):
        if self.stride is None:
            self.stride = [0, 0]
        if self.phys_addr is None:
            self.phys_addr = [0, 0]
        if self.virt_addr is None:
            self.virt_addr = [0, 0]


@dataclass
class VideoFrameInfo:
    """视频帧信息结构体"""
    frame: VideoFrame = None
    poolid: int = 0
    modid: int = 0
    
    def __post_init__(self):
        if self.frame is None:
            self.frame = VideoFrame()


@dataclass
class TaskAttr:
    """任务属性结构体"""
    src_frame: VideoFrameInfo = None
    dst_frame: VideoFrameInfo = None
    check_only: bool = False
    reserved: list = None
    debug: int = 0
    
    def __post_init__(self):
        if self.src_frame is None:
            self.src_frame = VideoFrameInfo()
        if self.dst_frame is None:
            self.dst_frame = VideoFrameInfo()
        if self.reserved is None:
            self.reserved = [0, 0, 0, 0]


@dataclass
class IveData:
    """IVE数据结构体"""
    stride: int = 0
    width: int = 0
    height: int = 0
    phys_addr: int = 0
    virt_addr: Optional[Union[array.array, bytes]] = None


@dataclass
class IveDmaCfg:
    """IVE DMA配置结构体"""
    mode: int = IveDmaMode.E_IVE_DMA_MODE_DIRECT_COPY


class MemoryManager:
    """内存管理器 - 支持真实MMZ和模拟内存分配"""

    def __init__(self, use_real_mmz: bool = False):
        self.use_real_mmz = use_real_mmz
        self.allocated_memory = {}
        self.next_phys_addr = 0x10000000  # 模拟物理地址起始
        self.mmz_lib = None

        if use_real_mmz:
            try:
                # 尝试加载MMZ库
                self.mmz_lib = ctypes.CDLL("libmmz.so")
                self._setup_mmz_prototypes()
                print("已加载真实MMZ库")
            except OSError as e:
                print(f"警告: 无法加载MMZ库 ({e})，将使用模拟模式")
                self.use_real_mmz = False

    def _setup_mmz_prototypes(self):
        """设置MMZ函数原型"""
        if not self.mmz_lib:
            return

        # sample_ive_mmz_malloc
        self.mmz_lib.sample_ive_mmz_malloc.argtypes = [
            ctypes.c_char_p,    # zone_name
            ctypes.c_char_p,    # mmb_name
            vs_uint32_t,        # size
            ctypes.POINTER(vs_uint64_t),  # phys_addr
            ctypes.POINTER(vs_void_t)     # virt_addr
        ]
        self.mmz_lib.sample_ive_mmz_malloc.restype = vs_int32_t

        # sample_ive_mmz_free
        self.mmz_lib.sample_ive_mmz_free.argtypes = [
            vs_uint64_t,        # phys_addr
            vs_void_t,          # virt_addr
            vs_uint32_t         # size
        ]
        self.mmz_lib.sample_ive_mmz_free.restype = None

    def malloc(self, zone_name: str, mmb_name: str, size: int) -> Tuple[int, int, array.array]:
        """
        内存分配 - 支持真实MMZ和模拟模式
        返回: (ret_code, phys_addr, virt_addr_array)
        """
        if self.use_real_mmz and self.mmz_lib:
            # 真实MMZ分配
            phys_addr = vs_uint64_t()
            virt_addr = vs_void_t()

            zone_name_c = zone_name.encode('utf-8') if zone_name else None
            mmb_name_c = mmb_name.encode('utf-8') if mmb_name else None

            ret = self.mmz_lib.sample_ive_mmz_malloc(
                zone_name_c,
                mmb_name_c,
                vs_uint32_t(size),
                ctypes.byref(phys_addr),
                ctypes.byref(virt_addr)
            )

            if ret == Constants.VS_SUCCESS:
                # 创建array来包装真实内存
                # 注意：这里需要小心处理真实内存指针
                virt_array = array.array('B', [0] * size)
                self.allocated_memory[phys_addr.value] = {
                    'size': size,
                    'virt_addr': virt_array,
                    'real_virt_addr': virt_addr.value,
                    'zone_name': zone_name,
                    'mmb_name': mmb_name
                }
                return ret, phys_addr.value, virt_array
            else:
                return ret, 0, None
        else:
            # 模拟模式
            try:
                virt_addr = array.array('B', [0] * size)
                phys_addr = self.next_phys_addr
                self.next_phys_addr += size

                self.allocated_memory[phys_addr] = {
                    'size': size,
                    'virt_addr': virt_addr,
                    'zone_name': zone_name,
                    'mmb_name': mmb_name
                }

                return Constants.VS_SUCCESS, phys_addr, virt_addr
            except Exception as e:
                print(f"内存分配失败: {e}")
                return Constants.VS_FAILED, 0, None

    def free(self, phys_addr: int, virt_addr=None, size: int = 0) -> int:
        """释放内存"""
        if phys_addr in self.allocated_memory:
            mem_info = self.allocated_memory[phys_addr]

            if self.use_real_mmz and self.mmz_lib:
                # 真实MMZ释放
                real_virt_addr = mem_info.get('real_virt_addr', 0)
                self.mmz_lib.sample_ive_mmz_free(
                    vs_uint64_t(phys_addr),
                    vs_void_t(real_virt_addr),
                    vs_uint32_t(mem_info['size'])
                )

            del self.allocated_memory[phys_addr]
            return Constants.VS_SUCCESS
        return Constants.VS_FAILED


class HardwareAPI:
    """硬件API调用 - 可以选择模拟模式或真实硬件调用"""

    def __init__(self, use_real_hardware: bool = False):
        self.use_real_hardware = use_real_hardware
        self.lib = None

        if use_real_hardware:
            try:
                # 尝试加载真实的硬件库
                self.lib = ctypes.CDLL("libvs_mal.so")  # 根据实际库名调整
                self._setup_function_prototypes()
                print("已加载真实硬件库")
            except OSError as e:
                print(f"警告: 无法加载硬件库 ({e})，将使用模拟模式")
                self.use_real_hardware = False

    def _setup_function_prototypes(self):
        """设置函数原型"""
        if not self.lib:
            return

        # vs_mal_gpe_job_start
        self.lib.vs_mal_gpe_job_start.argtypes = [ctypes.POINTER(vs_uint32_t)]
        self.lib.vs_mal_gpe_job_start.restype = vs_int32_t

        # vs_mal_gpe_job_finish
        self.lib.vs_mal_gpe_job_finish.argtypes = [vs_uint32_t]
        self.lib.vs_mal_gpe_job_finish.restype = vs_int32_t

        # vs_mal_gpe_job_cancel
        self.lib.vs_mal_gpe_job_cancel.argtypes = [vs_uint32_t]
        self.lib.vs_mal_gpe_job_cancel.restype = vs_int32_t

        # vs_mal_gpe_scale_task_add
        self.lib.vs_mal_gpe_scale_task_add.argtypes = [
            vs_uint32_t,                    # job_handle
            ctypes.POINTER(VsTaskAttr),     # task_attr
            vs_uint32_t                     # filter_type
        ]
        self.lib.vs_mal_gpe_scale_task_add.restype = vs_int32_t

        # vs_mal_ive_dma
        self.lib.vs_mal_ive_dma.argtypes = [
            ctypes.POINTER(VsIveData),      # src_data
            ctypes.POINTER(VsIveData),      # dst_data
            ctypes.POINTER(VsIveDmaCfg),    # cfg
            ctypes.POINTER(vs_uint32_t)     # handle
        ]
        self.lib.vs_mal_ive_dma.restype = vs_int32_t

        # vs_mal_ive_query
        self.lib.vs_mal_ive_query.argtypes = [
            vs_uint32_t,                    # handle
            ctypes.POINTER(vs_bool_t),      # finish
            vs_bool_t                       # block
        ]
        self.lib.vs_mal_ive_query.restype = vs_int32_t

    def vs_mal_gpe_job_start(self) -> Tuple[int, int]:
        """开始GPE任务"""
        if self.use_real_hardware and self.lib:
            # 调用真实硬件API
            job_handle = vs_uint32_t()
            ret = self.lib.vs_mal_gpe_job_start(ctypes.byref(job_handle))
            return ret, job_handle.value
        else:
            # 模拟模式
            job_handle = 12345
            print(f"[模拟] GPE任务开始, 句柄: {job_handle}")
            return Constants.VS_SUCCESS, job_handle

    def vs_mal_gpe_job_finish(self, job_handle: int) -> int:
        """完成GPE任务"""
        if self.use_real_hardware and self.lib:
            return self.lib.vs_mal_gpe_job_finish(vs_uint32_t(job_handle))
        else:
            print(f"[模拟] GPE任务完成, 句柄: {job_handle}")
            return Constants.VS_SUCCESS

    def vs_mal_gpe_job_cancel(self, job_handle: int) -> int:
        """取消GPE任务"""
        if self.use_real_hardware and self.lib:
            return self.lib.vs_mal_gpe_job_cancel(vs_uint32_t(job_handle))
        else:
            print(f"[模拟] GPE任务取消, 句柄: {job_handle}")
            return Constants.VS_SUCCESS

    def vs_mal_gpe_scale_task_add(self, job_handle: int, task_attr: TaskAttr, filter_type: int) -> int:
        """添加缩放任务"""
        if self.use_real_hardware and self.lib:
            # 将Python结构体转换为ctypes结构体
            c_task_attr = self._convert_task_attr_to_c(task_attr)
            ret = self.lib.vs_mal_gpe_scale_task_add(
                vs_uint32_t(job_handle),
                ctypes.byref(c_task_attr),
                vs_uint32_t(filter_type)
            )
            return ret
        else:
            print(f"[模拟] 添加缩放任务 - 句柄: {job_handle}, 滤波器类型: {filter_type}")
            print(f"[模拟] 源尺寸: {task_attr.src_frame.frame.width}x{task_attr.src_frame.frame.height}")
            print(f"[模拟] 目标尺寸: {task_attr.dst_frame.frame.width}x{task_attr.dst_frame.frame.height}")
            return Constants.VS_SUCCESS

    def vs_mal_ive_dma(self, src_data: IveData, dst_data: IveData, cfg: IveDmaCfg) -> Tuple[int, int]:
        """IVE DMA操作"""
        if self.use_real_hardware and self.lib:
            # 转换为ctypes结构体
            c_src_data = self._convert_ive_data_to_c(src_data)
            c_dst_data = self._convert_ive_data_to_c(dst_data)
            c_cfg = self._convert_ive_dma_cfg_to_c(cfg)
            handle = vs_uint32_t()

            ret = self.lib.vs_mal_ive_dma(
                ctypes.byref(c_src_data),
                ctypes.byref(c_dst_data),
                ctypes.byref(c_cfg),
                ctypes.byref(handle)
            )
            return ret, handle.value
        else:
            handle = 54321
            print(f"[模拟] IVE DMA操作 - 尺寸: {src_data.width}x{src_data.height}, 模式: {cfg.mode}")

            # 模拟数据拷贝
            if src_data.virt_addr is not None and dst_data.virt_addr is not None:
                try:
                    copy_size = min(len(src_data.virt_addr), len(dst_data.virt_addr))
                    dst_data.virt_addr[:copy_size] = src_data.virt_addr[:copy_size]
                    print(f"[模拟] 数据拷贝完成: {copy_size} 字节")
                except Exception as e:
                    print(f"[模拟] 数据拷贝失败: {e}")

            return Constants.VS_SUCCESS, handle

    def vs_mal_ive_query(self, handle: int) -> Tuple[int, bool]:
        """查询IVE操作状态"""
        if self.use_real_hardware and self.lib:
            finish = vs_bool_t()
            ret = self.lib.vs_mal_ive_query(
                vs_uint32_t(handle),
                ctypes.byref(finish),
                vs_bool_t(True)
            )
            return ret, finish.value
        else:
            print(f"[模拟] IVE操作查询 - 句柄: {handle}, 状态: 完成")
            return Constants.VS_SUCCESS, True

    def _convert_task_attr_to_c(self, task_attr: TaskAttr) -> VsTaskAttr:
        """将Python TaskAttr转换为ctypes VsTaskAttr"""
        c_task_attr = VsTaskAttr()

        # 转换源帧
        c_task_attr.src_frame.frame.width = task_attr.src_frame.frame.width
        c_task_attr.src_frame.frame.height = task_attr.src_frame.frame.height
        c_task_attr.src_frame.frame.stride[0] = task_attr.src_frame.frame.stride[0]
        c_task_attr.src_frame.frame.stride[1] = task_attr.src_frame.frame.stride[1]
        c_task_attr.src_frame.frame.pixel_format = task_attr.src_frame.frame.pixel_format
        c_task_attr.src_frame.frame.video_format = task_attr.src_frame.frame.video_format
        c_task_attr.src_frame.frame.phys_addr[0] = task_attr.src_frame.frame.phys_addr[0]
        c_task_attr.src_frame.frame.phys_addr[1] = task_attr.src_frame.frame.phys_addr[1]
        c_task_attr.src_frame.frame.virt_addr[0] = task_attr.src_frame.frame.virt_addr[0]
        c_task_attr.src_frame.frame.virt_addr[1] = task_attr.src_frame.frame.virt_addr[1]
        c_task_attr.src_frame.poolid = task_attr.src_frame.poolid
        c_task_attr.src_frame.modid = task_attr.src_frame.modid

        # 转换目标帧
        c_task_attr.dst_frame.frame.width = task_attr.dst_frame.frame.width
        c_task_attr.dst_frame.frame.height = task_attr.dst_frame.frame.height
        c_task_attr.dst_frame.frame.stride[0] = task_attr.dst_frame.frame.stride[0]
        c_task_attr.dst_frame.frame.stride[1] = task_attr.dst_frame.frame.stride[1]
        c_task_attr.dst_frame.frame.pixel_format = task_attr.dst_frame.frame.pixel_format
        c_task_attr.dst_frame.frame.video_format = task_attr.dst_frame.frame.video_format
        c_task_attr.dst_frame.frame.phys_addr[0] = task_attr.dst_frame.frame.phys_addr[0]
        c_task_attr.dst_frame.frame.phys_addr[1] = task_attr.dst_frame.frame.phys_addr[1]
        c_task_attr.dst_frame.frame.virt_addr[0] = task_attr.dst_frame.frame.virt_addr[0]
        c_task_attr.dst_frame.frame.virt_addr[1] = task_attr.dst_frame.frame.virt_addr[1]
        c_task_attr.dst_frame.poolid = task_attr.dst_frame.poolid
        c_task_attr.dst_frame.modid = task_attr.dst_frame.modid

        # 其他属性
        c_task_attr.check_only = task_attr.check_only
        for i in range(4):
            c_task_attr.reserved[i] = task_attr.reserved[i]
        c_task_attr.debug = task_attr.debug

        return c_task_attr

    def _convert_ive_data_to_c(self, ive_data: IveData) -> VsIveData:
        """将Python IveData转换为ctypes VsIveData"""
        c_ive_data = VsIveData()
        c_ive_data.stride = ive_data.stride
        c_ive_data.width = ive_data.width
        c_ive_data.height = ive_data.height
        c_ive_data.phys_addr = ive_data.phys_addr

        # 处理虚拟地址
        if ive_data.virt_addr is not None:
            if hasattr(ive_data.virt_addr, 'buffer_info'):
                c_ive_data.virt_addr = ive_data.virt_addr.buffer_info()[0]
            else:
                c_ive_data.virt_addr = id(ive_data.virt_addr)
        else:
            c_ive_data.virt_addr = 0

        return c_ive_data

    def _convert_ive_dma_cfg_to_c(self, cfg: IveDmaCfg) -> VsIveDmaCfg:
        """将Python IveDmaCfg转换为ctypes VsIveDmaCfg"""
        c_cfg = VsIveDmaCfg()
        c_cfg.mode = cfg.mode
        return c_cfg


# 全局实例
memory_manager = MemoryManager(use_real_mmz=False)  # 默认使用模拟模式
hardware_api = HardwareAPI(use_real_hardware=False)  # 默认使用模拟模式


def sample_ive_mmz_malloc(zone_name: str, mmb_name: str, size: int) -> Tuple[int, int, array.array]:
    """模拟MMZ内存分配"""
    return memory_manager.malloc(zone_name, mmb_name, size)


def sample_ive_mmz_free(phys_addr: int, virt_addr: Union[array.array, bytes], size: int) -> None:
    """MMZ内存释放"""
    if phys_addr != 0:
        memory_manager.free(phys_addr, virt_addr, size)


def sample_gpe_scale(zoom_factor: float, frame_info: VideoFrameInfo,
                    src_virt_addr: Union[array.array, bytes], x_offset: int, y_offset: int) -> int:
    """
    GPE缩放函数的Python实现
    
    Args:
        zoom_factor: 缩放因子
        frame_info: 视频帧信息
        src_virt_addr: 源虚拟地址（array数组或bytes）
        x_offset: X偏移
        y_offset: Y偏移
    
    Returns:
        int: 操作结果 (VS_SUCCESS 或 VS_FAILED)
    """
    print(f"开始GPE缩放操作 - 缩放因子: {zoom_factor}")
    
    # 获取原始图像参数
    image_origin_stride = frame_info.frame.stride[0]
    image_origin_width = frame_info.frame.width
    image_origin_height = frame_info.frame.height
    
    # 计算缩放后的源图像尺寸
    image_scale_src_width = int(frame_info.frame.width / zoom_factor)
    
    # 宽度对齐处理
    if image_scale_src_width % 2 != 0:
        image_scale_src_width += 1
    if (image_scale_src_width // 2) % 2 != 0:
        image_scale_src_width += 2
    
    # 计算高度（保持16:9比例）
    image_scale_src_height = int(image_scale_src_width * 0.5625)
    if image_scale_src_height % 2 != 0:
        image_scale_src_height += 1
    
    print(f"缩放源尺寸: {image_scale_src_width}x{image_scale_src_height}")
    
    # 开始GPE任务
    ret, job_handle = hardware_api.vs_mal_gpe_job_start()
    if ret != Constants.VS_SUCCESS:
        print(f"GPE任务启动失败: {ret}")
        return Constants.VS_FALSE
    
    # 创建任务属性
    task_attr = TaskAttr()
    
    # 设置源帧属性
    task_attr.src_frame.frame.width = image_scale_src_width
    task_attr.src_frame.frame.height = image_scale_src_height
    task_attr.src_frame.frame.pixel_format = PixelFormat.E_PIXEL_FORMAT_YUV_420SP
    task_attr.src_frame.frame.stride = [image_origin_stride, image_origin_stride]
    task_attr.src_frame.frame.video_format = VideoFormat.E_VIDEO_FORMAT_LINEAR
    
    # 设置目标帧属性（固定为1920x1080）
    task_attr.dst_frame.frame.width = 1920
    task_attr.dst_frame.frame.height = 1080
    task_attr.dst_frame.frame.pixel_format = PixelFormat.E_PIXEL_FORMAT_YUV_420SP
    task_attr.dst_frame.frame.stride = [image_origin_stride, image_origin_stride]
    task_attr.dst_frame.frame.video_format = VideoFormat.E_VIDEO_FORMAT_LINEAR
    
    # 分配临时缓冲区
    resize_size = image_origin_stride * image_origin_height * 2
    ret, resize_phys_addr, p_resize_virt_addr = sample_ive_mmz_malloc(
        None, "resize_dst", resize_size)
    
    if ret != Constants.VS_SUCCESS:
        print("临时缓冲区分配失败")
        hardware_api.vs_mal_gpe_job_cancel(job_handle)
        return Constants.VS_FAILED
    
    # 计算起始位置
    start_x = image_origin_width // 2 - image_scale_src_width // 2 - x_offset
    start_y = image_origin_height // 2 - image_scale_src_height // 2 - y_offset
    
    # X坐标对齐
    if start_x % 2 != 0:
        start_x += 1
    
    print(f"起始位置: ({start_x}, {start_y})")
    
    # 执行DMA拷贝操作（Y分量）
    dma_cfg = IveDmaCfg()
    
    # 源数据配置
    ive_copy_src_data = IveData(
        stride=image_origin_stride,
        width=image_scale_src_width,
        height=image_scale_src_height,
        phys_addr=frame_info.frame.phys_addr[0] + start_y * image_origin_stride + start_x,
        virt_addr=src_virt_addr[start_y * image_origin_stride + start_x:]
    )
    
    # 目标数据配置
    ive_copy_dst_data = IveData(
        stride=image_origin_stride,
        width=image_scale_src_width,
        height=image_scale_src_height,
        phys_addr=resize_phys_addr,
        virt_addr=p_resize_virt_addr
    )
    
    # 执行Y分量DMA拷贝
    ret, handle = hardware_api.vs_mal_ive_dma(ive_copy_src_data, ive_copy_dst_data, dma_cfg)
    if ret != Constants.VS_SUCCESS:
        print(f"Y分量DMA拷贝失败: {ret}")

    ret, finish = hardware_api.vs_mal_ive_query(handle)
    if ret != Constants.VS_SUCCESS:
        print(f"Y分量DMA查询失败: {ret}")

    # 执行UV分量DMA拷贝
    uv_offset = image_origin_stride * image_origin_height
    ive_copy_src_data.height = image_scale_src_height // 2
    ive_copy_src_data.phys_addr = (frame_info.frame.phys_addr[1] +
                                  start_y // 2 * image_origin_stride + start_x)
    ive_copy_src_data.virt_addr = src_virt_addr[uv_offset + start_y // 2 * image_origin_stride + start_x:]

    ive_copy_dst_data.height = image_scale_src_height // 2
    ive_copy_dst_data.phys_addr = resize_phys_addr + image_scale_src_height * image_origin_stride
    ive_copy_dst_data.virt_addr = p_resize_virt_addr[image_scale_src_height * image_origin_stride:]

    ret, handle = hardware_api.vs_mal_ive_dma(ive_copy_src_data, ive_copy_dst_data, dma_cfg)
    if ret != Constants.VS_SUCCESS:
        print(f"UV分量DMA拷贝失败: {ret}")

    ret, finish = hardware_api.vs_mal_ive_query(handle)
    if ret != Constants.VS_SUCCESS:
        print(f"UV分量DMA查询失败: {ret}")
    
    # 设置任务属性的地址信息
    task_attr.src_frame.frame.phys_addr = [resize_phys_addr, 
                                          resize_phys_addr + image_scale_src_height * image_origin_stride]
    # 处理源帧虚拟地址
    if hasattr(p_resize_virt_addr, 'buffer_info'):
        resize_base_addr = p_resize_virt_addr.buffer_info()[0]
    else:
        resize_base_addr = id(p_resize_virt_addr)

    task_attr.src_frame.frame.virt_addr = [resize_base_addr,
                                          resize_base_addr + image_scale_src_height * image_origin_stride]
    
    task_attr.dst_frame.frame.phys_addr = frame_info.frame.phys_addr.copy()
    # 处理虚拟地址（模拟）
    if hasattr(src_virt_addr, 'buffer_info'):
        # array.array类型
        base_addr = src_virt_addr.buffer_info()[0]
    else:
        # bytes或其他类型，使用id作为模拟地址
        base_addr = id(src_virt_addr)

    task_attr.dst_frame.frame.virt_addr = [base_addr,
                                          base_addr + image_origin_stride * image_origin_height]
    
    # 添加缩放任务
    filter_type = GpeFilterType.E_GPE_FILTER_TYPE_3_TAP
    ret = hardware_api.vs_mal_gpe_scale_task_add(job_handle, task_attr, filter_type)
    if ret != Constants.VS_SUCCESS:
        print(f"添加缩放任务失败: {ret}")
        hardware_api.vs_mal_gpe_job_cancel(job_handle)
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size)
        return Constants.VS_FAILED

    # 完成GPE任务
    ret = hardware_api.vs_mal_gpe_job_finish(job_handle)
    if ret != Constants.VS_SUCCESS:
        print(f"GPE任务完成失败: {ret}")
        hardware_api.vs_mal_gpe_job_cancel(job_handle)
        sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size)
        return Constants.VS_FAILED
    
    # 更新帧信息
    frame_info.frame.width = 1920
    frame_info.frame.height = 1080
    
    # 释放临时缓冲区
    sample_ive_mmz_free(resize_phys_addr, p_resize_virt_addr, resize_size)
    
    print("GPE缩放操作完成")
    return Constants.VS_SUCCESS


def main():
    """测试函数"""
    print("=== sample_gpe_scale Python版本测试 ===")
    print("注意: 当前运行在模拟模式下")
    print("要使用真实硬件，请修改 hardware_api = HardwareAPI(use_real_hardware=True)")
    print()

    # 创建测试数据
    frame_info = VideoFrameInfo()
    frame_info.frame.width = 3840
    frame_info.frame.height = 2160
    frame_info.frame.stride = [3840, 3840]
    frame_info.frame.phys_addr = [0x20000000, 0x20800000]

    # 模拟源图像数据
    image_size = frame_info.frame.stride[0] * frame_info.frame.height * 3 // 2  # YUV420SP
    import random
    src_virt_addr = array.array('B', [random.randint(0, 255) for _ in range(image_size)])

    # 执行缩放操作
    zoom_factor = 2.0
    x_offset = 0
    y_offset = 0

    print(f"输入参数:")
    print(f"  - 缩放因子: {zoom_factor}")
    print(f"  - 输入尺寸: {frame_info.frame.width}x{frame_info.frame.height}")
    print(f"  - 偏移: ({x_offset}, {y_offset})")
    print()

    result = sample_gpe_scale(zoom_factor, frame_info, src_virt_addr, x_offset, y_offset)

    print()
    if result == Constants.VS_SUCCESS:
        print("✓ 缩放操作成功完成")
        print(f"✓ 输出尺寸: {frame_info.frame.width}x{frame_info.frame.height}")
    else:
        print("✗ 缩放操作失败")

    print("=== 测试完成 ===")


def configure_for_real_hardware():
    """配置真实硬件模式"""
    global hardware_api, memory_manager

    print("配置真实硬件模式...")
    hardware_api = HardwareAPI(use_real_hardware=True)
    memory_manager = MemoryManager(use_real_mmz=True)

    print("注意: 真实硬件模式需要:")
    print("1. 正确的硬件库文件 (libvs_mal.so, libmmz.so)")
    print("2. 适当的权限和驱动支持")
    print("3. ARM设备环境")

    return hardware_api.use_real_hardware and memory_manager.use_real_mmz


if __name__ == "__main__":
    import sys

    print("=" * 60)
    print("sample_gpe_scale Python版本")
    print("基于原始C++代码的完整实现")
    print("=" * 60)

    # 检查命令行参数
    use_real_hardware = False
    if len(sys.argv) > 1 and sys.argv[1] == "--real-hardware":
        use_real_hardware = configure_for_real_hardware()
        if use_real_hardware:
            print("✓ 真实硬件模式已启用")
        else:
            print("✗ 真实硬件模式启用失败，使用模拟模式")

    main()

    print()
    print("=" * 60)
    print("使用说明:")
    print("1. 模拟模式 (默认):")
    print("   python3 sample_gpe_scale.py")
    print("   - 用于理解算法逻辑和测试")
    print("   - 不需要硬件库")
    print()
    print("2. 真实硬件模式:")
    print("   python3 sample_gpe_scale.py --real-hardware")
    print("   - 需要以下库文件:")
    print("     * libvs_mal.so (GPE/IVE硬件API)")
    print("     * libmmz.so (内存管理)")
    print("   - 需要ARM设备和相应驱动")
    print("   - 需要适当的系统权限")
    print()
    print("3. 主要功能:")
    print("   - 图像缩放和裁剪")
    print("   - YUV420SP格式处理")
    print("   - 硬件加速内存管理")
    print("   - GPE硬件缩放")
    print("   - IVE硬件DMA操作")
    print()
    print("4. 结构体映射:")
    print("   - Python类 ↔ ctypes结构体 ↔ C结构体")
    print("   - 完整的类型转换和内存管理")
    print("=" * 60)
