#ifndef AIRVISEN_MESSAGE_H_
#define AIRVISEN_MESSAGE_H_

//#include "sample_common.h"
typedef enum airvisen_ipc_cmd {

    COMMAND_ACK = 0,

    START_JPEG_STORE = 1,

    STOP_JPEG_STORE,

    START_VIDEO_RECORD,

    STOP_VIDEO_RECORD,

    VIDEO_ZOOM,

    //Auto stop after 30 min or unexpected halt event. notify tracker main process.
    VIDEO_RECORD_STOP_EVENT,

    DETECTION_RESULT,

    TRACKING_RESULT,

    TRACKER_INIT_BOX,

    SET_DXDY_FROM_WEB,

    TRACKER_STOP,

    TRACKER_CHANGE_CAMERA,

    CAMERA_MODE,

    //ZOOM JUST FOR IR
    IR_VIDEO_ZOOM,

    //ENABLE SHUTTER
    IR_SHUTTER,

    //PSEDUO
    IR_CTRL_PSEDUO_COLOR,

    //MEASURE TEMPERATURE
    IR_MEASURE_TEMPERATURE_REQUEST,
    
    //RESPONSE
    IR_MEASURE_TEMPERATURE_RESPONSE,

    //*********detection**********//
    DETECTOR_START,

    DETECTOR_STOP,
} airvisen_ipc_cmd_e;


/**
 * message head
 */
typedef struct airvisen_ipc_message_head
{
    int cmd;
    int len;

}airvisen_ipc_message_head_s;


/**
 * store jpeg command
 */
typedef struct airvisen_track_message_store_jpeg
{
    airvisen_ipc_message_head_s head;

    /**reserved */
    int reserved;

}airvisen_track_message_store_jpeg_s;


/**
 * video record command
 */
typedef struct airvisen_track_message_store_record
{
    airvisen_ipc_message_head_s head;

    /*single record event, save frame_count frame*/
    int frame_count;
    
    /*record type
    0 default, indicate record as mp4
    */
    int record_type;

    int record_venc_chn;

}airvisen_track_message_store_record_s;


/**
 * video record auto stop or unexpected halt
 */
typedef struct airvisen_track_message_record_stop_event
{
    airvisen_ipc_message_head_s head;
    
    /*stop_reason
    0 auto stop after 30 mintues.
    1 other.
    */
    int stop_reason;

}airvisen_track_message_record_stop_event_s;


/**
 * video zoom command
 */
typedef struct airvisen_track_message_video_zoom
{
    airvisen_ipc_message_head_s head;

    /*zoom factor ,range from 1.0-8.0*/
    int factor;

}airvisen_track_message_video_zoom_s;


#endif /*AIRVISEN_MESSAGE_H_*/



