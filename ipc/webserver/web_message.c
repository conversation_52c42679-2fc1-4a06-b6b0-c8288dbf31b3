#include "web_message.h"
#include "../ipc.h"
#include "stdio.h"


static int sock;
#define WEBSERVER_SOCKET_ADDRESS "ipc:///tmp/webserver.ipc"

void start_webserver_ipc_ser()
{
    sock = ipc_server_start(WEBSERVER_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }    
}


void start_webserver_ipc_client()
{
    sock = ipc_client_start(WEBSERVER_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
}

void send_dxdy_config_command()
{
    airvisen_track_message_store_jpeg_s store_jpeg_msg = {0};
    store_jpeg_msg.head.cmd = SET_DXDY_FROM_WEB;
    store_jpeg_msg.head.len = sizeof(airvisen_track_message_store_jpeg_s);
    ipc_send_message(sock, sizeof(airvisen_track_message_store_jpeg_s), (char *)&store_jpeg_msg);
}


void stop_webserver_ipc()
{
    ipc_close(sock);
}


static int dispatch_webser_message(int data_len, char *data)
{
    if (data_len < sizeof(airvisen_ipc_message_head_s)){
        return -1;
    }
    
    airvisen_ipc_message_head_s *msg = (airvisen_ipc_message_head_s*)data;
    switch (msg->cmd)
    {
        case SET_DXDY_FROM_WEB:{
            printf("Recv SET DXDY command\n");
            return SET_DXDY_FROM_WEB;
        }
        break;  

    default:
        printf("unsupport command: [%d]\n",msg->cmd);
        return -1;
        break;
    }
}

int recv_webserver_message(char *data, int data_len)
{
    int rc = ipc_recv_message_wait(sock, data, data_len);
    if (rc > 0)
    {
        return dispatch_webser_message(rc, data);
    }else{
        return rc;
    }
    
}
