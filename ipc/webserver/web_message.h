#ifndef WEB_MESSAGE_H_
#define WEB_MESSAGE_H_

#ifdef __cplusplus
extern "C" {
#endif
#include "stdio.h"
#include "../airvisen_message.h"



/**
 * set dxdy command
 */
typedef struct {
    airvisen_ipc_message_head_s head;
    //0 :add dx 
    //1 :sub dy 
    //2 :add dy
    //3 :sub dx
    int action;

}duallight_dxdy_message_s;


void start_webserver_ipc_ser();
void stop_webserver_ipc_ser();
void start_webserver_ipc_client();
void send_dxdy_config_command();
int recv_webserver_message(char *data, int data_len);

#ifdef __cplusplus
}
#endif


#endif
