#include "ipc.h"
#include "algorithm_message.h"
#include "sample_common.h"
#include "internal_defs.h"

int detection_sock;
int tracker_sock;
#define DETECTOR_SOCKET_ADDRESS "ipc:///tmp/airvisen.detector.ipc"
#define TRACKER_SOCKET_ADDRESS "ipc:///tmp/airvisen.tracker.ipc"
//< flight control
#ifdef FC_DISPATCHER_ENABLED
static int fc_tracker_sock;
#define FC_TRACKER_SOCKET_ADDRESS "ipc:///tmp/airvisen.fctracker.ipc"
int fc_tracker_ipc_thread_id;
int g_bFCIpcExit = 0;
int g_fc_tracker_state; //enum trackState { TRACKER_TRACKING = 1, TRACKER_FINDING = 2, TRACKER_LOST = 3 };
track_result_ipc_s g_fc_track_results;
#endif

int g_bDetectionIpcExit;
int g_bTrackingIpcExit;
int g_tracker_state; //enum trackState { TRACKER_TRACKING = 1, TRACKER_FINDING = 2, TRACKER_LOST = 3, TRACKER_NOT_UPDATED = 4 };
track_result_ipc_s g_track_results;
detection_result_ipc_s g_detect_results;
pthread_mutex_t track_mutex;
pthread_mutex_t detect_mutex;
int tracker_ipc_thread_id;
int detector_ipc_thread_id;

#ifdef DUAL_CAM
int g_im_source_from_algo;
int g_im_source_from_app;
#endif

unsigned long get_reltime (void)
{
    struct timespec tp;
    clock_gettime(CLOCK_MONOTONIC, &tp);
    // airvisen_trace("cur_time: %ld\n", (tp.tv_sec * 1000000ULL + tp.tv_nsec / 1000ULL));
    return (tp.tv_sec * 1000000ULL + tp.tv_nsec / 1000ULL);
}

//< flight control
#ifdef FC_DISPATCHER_ENABLED
//server
void airvisen_start_fc_tracker_ipc()
{
   //printf("line:%d\n", __LINE__);
    fc_tracker_sock = ipc_server_start(FC_TRACKER_SOCKET_ADDRESS); 
   //printf("line:%d\n", __LINE__);
    if (fc_tracker_sock < 0)
    {
        printf("start ipc server faild\n");
    }  
    else {
        airvisen_trace("================fc tracker_sock started!================\n");
    }
   //printf("line:%d\n", __LINE__);
    g_bFCIpcExit = 0;
    pthread_create(&fc_tracker_ipc_thread_id, 0, airvisen_run_fc_tracker_recv, NULL);
   //printf("line:%d\n", __LINE__);
}

void airvisen_stop_fc_tracker_ipc()
{
    g_bFCIpcExit = 1;
    pthread_join(tracker_ipc_thread_id, NULL);
    ipc_close(fc_tracker_sock);
}

void airvisen_start_fc_tracker_client_ipc()
{
    fc_tracker_sock = ipc_client_start(FC_TRACKER_SOCKET_ADDRESS); 
    if (fc_tracker_sock < 0)
    {
        printf("start tracker ipc server faild\n");
    }  
    else 
    {
        printf("================fc tracker_sock client started!================\n");
    }
}

#endif


//server
void airvisen_start_tracker_ipc()
{
    tracker_sock = ipc_server_start(TRACKER_SOCKET_ADDRESS); 
    if (tracker_sock < 0)
    {
        printf("start ipc server faild\n");
    }  
    else {
        airvisen_trace("================tracker_sock started!================\n");
    }
    // g_bTrackingIpcExit = 0;
    pthread_create(&tracker_ipc_thread_id, 0, airvisen_run_tracker_recv, NULL);
}

void airvisen_stop_detector_ipc()
{
    g_bDetectionIpcExit = 1;
    pthread_join(detector_ipc_thread_id, NULL);
    ipc_close(detection_sock);
}

void airvisen_stop_tracker_ipc()
{
    g_bTrackingIpcExit = 1;
    pthread_join(tracker_ipc_thread_id, NULL);
    ipc_close(tracker_sock);
}

void airvisen_start_detector_ipc()
{
    detection_sock = ipc_server_start(DETECTOR_SOCKET_ADDRESS); 
    if (detection_sock < 0)
    {
        printf("start ipc server faild\n");
    }  
    pthread_create(&detector_ipc_thread_id, 0, airvisen_run_detection_recv, NULL);
}

// void airvisen_stop_detector_ipc()
// {
//     ipc_close(detection_sock);
// }

/**
 * @param char *data: recv buf.
 * @param int data_len: recv buf length.
 * @param int flags: 0 indicate wait, 1 NNDONOTWAIT.
 */
// int airvisen_recv_detection_result(char *data, int data_len, int flags, sample_nn_post_result_s* results)
// {
//     int rc = ipc_recv_message(detection_sock, data, data_len, flags);
//     if (rc > 0)
//     {
//         if (data_len < sizeof(airvisen_ipc_message_head_s)){
//             return -1;
//         }
        
//         airvisen_track_message_detection_result_s *msg = (airvisen_track_message_detection_result_s*)data;
//         if (msg->head.cmd != DETECTION_RESULT) return -1;
//         if (data_len < msg->head.len) return -1;
//         if(results->model_type != E_NN_MODE_DETECTION) 
//         {
//             airvisen_trace("wrong model type!");
//             return -1;
//         }
//         // results = (airvisen_track_message_detection_result_s *)malloc(sizeof(airvisen_track_message_detection_result_s));
//         memcpy(&results->detection, &msg->detection_results, sizeof(sample_nn_detection_result_s));
//         results->is_updated = VS_TRUE;
        
//         unsigned long cur_time = get_reltime();
//         airvisen_trace("detection ipc cost time:%ld - %ld = %ld\n", cur_time, msg->pts, cur_time - msg->pts);
//         return 0;
//     }else{
//         airvisen_trace("detection not updated!");
//         return rc;
//     }
// }

void airvisen_run_detection_recv()
{
    // PRINTLINE
    char ipc_detect_data[4096];
    int data_len = 4096;
    int delay_thresh = 25 * 1000;
    g_bDetectionIpcExit = 0;
    airvisen_trace("g_bDetectionIpcExit: %d \n", g_bDetectionIpcExit);
    while(!g_bDetectionIpcExit)
    {
        // airvisen_recv_tracker_update_result(ipc_tracking_data, 256, 0, &g_track_results, &g_tracker_state, 25 * 1000);
        int rc = ipc_recv_message(detection_sock, ipc_detect_data, data_len, 0);
        if (rc > 0)
        {
            if (data_len < sizeof(airvisen_ipc_message_head_s)){
                // return -1;
                continue;
            }
            
            airvisen_track_message_detection_result_s *msg = (airvisen_track_message_detection_result_s*)ipc_detect_data;
            if (msg->head.cmd != DETECTION_RESULT) continue;
            if (data_len < msg->head.len) continue;

            //test time delay
            unsigned long cur_time = get_reltime();
            // airvisen_trace("ipc cost time:%ld - %ld = %ld\n", cur_time, msg->pts, cur_time - msg->pts);
            if (cur_time - msg->pts > delay_thresh) 
            {
                continue;
            }
            // pthread_mutex_lock(&detect_mutex);
            memcpy(&g_detect_results, &msg->detection_results, sizeof(detection_result_ipc_s));
            // g_detect_results.model_type = E_NN_MODE_DETECTION;
            // g_detect_results.is_updated = VS_TRUE;
            // pthread_mutex_unlock(&detect_mutex);
            
            continue;
        }else{
            airvisen_trace("tracking not updated!");
            continue;
        }
    }
}

int airvisen_detector_update_result(detection_result_ipc_s* p_detect_results)
{
    // pthread_mutex_lock(&detect_mutex);
    // PRINTLINE
    memcpy(p_detect_results, &g_detect_results, sizeof(detection_result_ipc_s));
    // pthread_mutex_unlock(&detect_mutex);
    // airvisen_trace("g_tracker_state : %d\n", g_tracker_state);
    return 0;
}

int airvisen_send_detector_start()
{
    airvisen_ipc_message_head_s detection_result_msg = {0};
    detection_result_msg.cmd = DETECTOR_START;
    detection_result_msg.len = sizeof(airvisen_ipc_message_head_s);
    int ret = ipc_send_message(detection_sock, sizeof(airvisen_ipc_message_head_s), (char *)&detection_result_msg);

    return 0;
}

int airvisen_send_detector_stop()
{
    airvisen_ipc_message_head_s detection_result_msg = {0};
    detection_result_msg.cmd = DETECTOR_STOP;
    detection_result_msg.len = sizeof(airvisen_ipc_message_head_s);
    int ret = ipc_send_message(detection_sock, sizeof(airvisen_ipc_message_head_s), (char *)&detection_result_msg);

    return 0;
}


// int airvisen_send_tracker_initbox(int l, int t, int w, int h)
#ifdef DUAL_CAM
int airvisen_send_tracker_initbox(float l, float t, float w, float h, int tracker_source)
#else
int airvisen_send_tracker_initbox(float l, float t, float w, float h)
#endif
{
    airvisen_track_message_tracking_initbox_s tracking_result_msg = {0};
    tracking_result_msg.head.cmd = TRACKER_INIT_BOX;
    tracking_result_msg.head.len = sizeof(airvisen_track_message_tracking_initbox_s);
    // memcpy(&tracking_result_msg.detection_results, p_detection_results, sizeof(sample_nn_detection_result_s));
    tracking_result_msg.l = l * IPC_INT_RANGE;
    tracking_result_msg.t = t * IPC_INT_RANGE;
    tracking_result_msg.w = w * IPC_INT_RANGE;
    tracking_result_msg.h = h * IPC_INT_RANGE;
#ifdef DUAL_CAM
    tracking_result_msg.tracker_source = tracker_source;
#endif
    // airvisen_trace("l, t, w, h: %f, %f, %f, %f\n", l, t, w, h)
    int ret = ipc_send_message(tracker_sock, sizeof(airvisen_track_message_tracking_initbox_s), (char *)&tracking_result_msg);

    
    g_tracker_state = 4;
    return 0;
}

int airvisen_send_tracker_stop()
{
    airvisen_ipc_message_head_s tracking_result_msg = {0};
    tracking_result_msg.cmd = TRACKER_STOP;
    tracking_result_msg.len = sizeof(airvisen_ipc_message_head_s);
    int ret = ipc_send_message(tracker_sock, sizeof(airvisen_ipc_message_head_s), (char *)&tracking_result_msg);

    // g_tracker_state = 3;
    return 0;
}

void airvisen_start_tracker_client_ipc()
{
    tracker_sock = ipc_client_start(TRACKER_SOCKET_ADDRESS); 
    if (tracker_sock < 0)
    {
        printf("start tracker ipc server faild\n");
    }  
    else 
    {
        printf("================tracker_sock client started!================\n");
    }
}

void airvisen_start_detector_client_ipc()
{
    detection_sock = ipc_client_start(DETECTOR_SOCKET_ADDRESS); 
    if (detection_sock < 0)
    {
        printf("start detector ipc server faild\n");
    }
}

int airvisen_send_detection_ret(detection_result_ipc_s* p_detection_results)
{
    airvisen_track_message_detection_result_s detection_result_msg = {0};
    detection_result_msg.head.cmd = DETECTION_RESULT;
    detection_result_msg.head.len = sizeof(airvisen_track_message_detection_result_s);
    memcpy(&detection_result_msg.detection_results, p_detection_results, sizeof(detection_result_ipc_s));
    detection_result_msg.pts = get_reltime();
    ipc_send_message(detection_sock, sizeof(airvisen_track_message_detection_result_s), (char *)&detection_result_msg);
}

#ifdef DUAL_CAM
int airvisen_send_tracker_ret(track_result_ipc_s* tracking_results, int cur_state, int tracker_source)
#else
int airvisen_send_tracker_ret(track_result_ipc_s* tracking_results, int cur_state)
#endif
{
#ifdef FC_DISPATCHER_ENABLED
#ifdef FC_SEND_WHOLE_TRACKING_MSG_2_DISPATCHER
    airvisen_track_message_tracking_result_s tracking_result_msg_fc = {0};
    tracking_result_msg_fc.head.cmd = TRACKING_RESULT;
    tracking_result_msg_fc.head.len = sizeof(airvisen_track_message_tracking_result_s);
    memcpy(&tracking_result_msg_fc.tracking_results, tracking_results, sizeof(track_result_ipc_s));
    tracking_result_msg_fc.cur_state = cur_state;
    tracking_result_msg_fc.pts =  get_reltime();
   //printf("come to airvisen_send_tracker_ret to fc_tracker_sock\n"); 
    int ret = ipc_send_message(fc_tracker_sock, sizeof(airvisen_track_message_tracking_result_s), (char *)&tracking_result_msg_fc);
   //printf("ret:%d\n",ret);
#else
     track_result_ipc_s ipc2fc;
     memcpy(&ipc2fc, tracking_results, sizeof(track_result_ipc_s));
     if (1)
     {
         vs_uint16_t x1 =   ipc2fc.target_pos_x_norm;
         vs_uint16_t y1 =   ipc2fc.target_pos_y_norm;
         vs_uint16_t w1 =   ipc2fc.target_sz_x_norm;
         vs_uint16_t h1 =   ipc2fc.target_sz_y_norm;
         airvisen_trace("will send INT16: %d, %d, %d, %d\n", x1, y1, w1, h1);
         airvisen_trace("will send NORM: %1.3f, %1.3f, %1.3f, %1.3f\n", (float)x1/65535, (float)y1/65535, (float)w1/65535, (float)h1/65535);
     }
    int ret = ipc_send_message(fc_tracker_sock, sizeof(track_result_ipc_s), (char *)&ipc2fc);
    printf("ret:%d\n",ret);
#endif
#endif
    airvisen_track_message_tracking_result_s tracking_result_msg = {0};
    tracking_result_msg.head.cmd = TRACKING_RESULT;
    tracking_result_msg.head.len = sizeof(airvisen_track_message_tracking_result_s);
    memcpy(&tracking_result_msg.tracking_results, tracking_results, sizeof(track_result_ipc_s));
    tracking_result_msg.cur_state = cur_state;
    //airvisen_trace("sizeof(airvisen_track_message_tracking_result_s) : %d\n", sizeof(airvisen_track_message_tracking_result_s));
    //airvisen_trace("rect: [%d, %d, %f, %f]\n", tracking_results->target_pos_x, tracking_results->target_pos_y, tracking_results->target_sz_x, tracking_results->target_sz_y);
     
    tracking_result_msg.pts =  get_reltime();
#ifdef DUAL_CAM
    tracking_result_msg.tracker_source = tracker_source;
#endif
    int nret = ipc_send_message(tracker_sock, sizeof(airvisen_track_message_tracking_result_s), (char *)&tracking_result_msg);
   //printf("nret:%d\n", nret);
    return 0;
}

#ifdef DUAL_CAM
int airvisen_recv_tracker_initbox(char *data, int data_len, int flags, float* initbox, int *tracker_source)
#else
int airvisen_recv_tracker_initbox(char *data, int data_len, int flags, float* initbox)
#endif
{
    int rc = ipc_recv_message(tracker_sock, data, data_len, flags);
    // PRINTLINE
    if (rc > 0)
    {
        if (data_len < sizeof(airvisen_ipc_message_head_s)){
            return -1;
        }
        
        airvisen_track_message_tracking_initbox_s *msg = (airvisen_track_message_tracking_initbox_s*)data;
        // PRINTLINE
        if (msg->head.cmd != TRACKER_INIT_BOX) return -1;
        if (data_len < msg->head.len) return -1;
        // PRINTLINE
        initbox[0] = (float)msg->l / IPC_INT_RANGE;
        initbox[1] = (float)msg->t / IPC_INT_RANGE;
        initbox[2] = (float)msg->w / IPC_INT_RANGE;
        initbox[3] = (float)msg->h / IPC_INT_RANGE;
        // airvisen_trace("l, t, w, h: %f, %f, %f, %f\n", msg->l, msg->t, msg->w, msg->h)
        // PRINTLINE
#ifdef DUAL_CAM
        *tracker_source = msg->tracker_source;
#endif
        return 0;
    // }else{
    //     return rc;
    }
    return -1;
}

void airvisen_run_tracker_recv()
{
    // PRINTLINE
    char ipc_tracking_data[256];
    int data_len = 256;
    int delay_thresh = 25 * 1000;
    g_bTrackingIpcExit = 0;
    airvisen_trace("g_bTrackingIpcExit: %d \n", g_bTrackingIpcExit);
    while(!g_bTrackingIpcExit)
    {
        // airvisen_recv_tracker_update_result(ipc_tracking_data, 256, 0, &g_track_results, &g_tracker_state, 25 * 1000);
        // PRINTLINE
        int rc = ipc_recv_message(tracker_sock, ipc_tracking_data, data_len, 0);
        if (rc > 0)
        {
            if (data_len < sizeof(airvisen_ipc_message_head_s)){
                // return -1;
                continue;
            }
            
            airvisen_track_message_tracking_result_s *msg = (airvisen_track_message_tracking_result_s*)ipc_tracking_data;
            if (msg->head.cmd != TRACKING_RESULT) continue;
            if (data_len < msg->head.len) continue;

            //test time delay
            unsigned long cur_time = get_reltime();
            // airvisen_trace("ipc cost time:%ld - %ld = %ld\n", cur_time, msg->pts, cur_time - msg->pts);
            if (cur_time - msg->pts > delay_thresh) 
            {
                continue;
            }
            // track_results = (airvisen_track_message_tracking_result_s *)malloc(sizeof(airvisen_track_message_tracking_result_s));
            pthread_mutex_lock(&track_mutex);
            memcpy(&g_track_results, &msg->tracking_results, sizeof(track_result_ipc_s));
            // g_track_results.model_type = E_NN_MODE_TRACK;
            // g_track_results.is_updated = VS_TRUE;
            g_tracker_state = msg->cur_state;
#ifdef DUAL_CAM
            g_im_source_from_algo = msg->tracker_source;
#endif
            pthread_mutex_unlock(&track_mutex);
            
            continue;
        }else{
            airvisen_trace("tracking not updated!");
            continue;
        }
    }
}

#ifdef DUAL_CAM
int airvisen_tracker_update_result(track_result_ipc_s* p_track_results, int* p_cur_state, int* p_im_source)
#else
int airvisen_tracker_update_result(track_result_ipc_s* p_track_results, int* p_cur_state)
#endif
{
    pthread_mutex_lock(&track_mutex);
    memcpy(p_track_results, &g_track_results, sizeof(track_result_ipc_s));
    *p_cur_state = g_tracker_state;
#ifdef DUAL_CAM
    *p_im_source = g_im_source_from_algo;
#endif
    pthread_mutex_unlock(&track_mutex);
    // airvisen_trace("g_tracker_state : %d\n", g_tracker_state);
    return 0;
}

int airvisen_send_camera_mode(int camera_mode)
{
    airvisen_track_message_camera_mode_s camera_mode_msg = {0};
    camera_mode_msg.head.cmd = CAMERA_MODE;
    camera_mode_msg.head.len = sizeof(airvisen_track_message_camera_mode_s);
    camera_mode_msg.camera_mode = camera_mode;
    int ret = ipc_send_message(detection_sock, sizeof(airvisen_track_message_camera_mode_s), (char *)&camera_mode_msg);
    return 0;
}

#ifdef FC_DISPATCHER_ENABLED
int airvisen_fc_tracker_update_result(track_result_ipc_s* p_track_results, int* p_cur_state)
{
    pthread_mutex_lock(&track_mutex);
    memcpy(p_track_results, &g_fc_track_results, sizeof(track_result_ipc_s));
    *p_cur_state = g_fc_tracker_state;
    pthread_mutex_unlock(&track_mutex);
    // airvisen_trace("g_fc_tracker_state : %d\n", g_fc_tracker_state);
    return 0;
}
void airvisen_run_fc_tracker_recv()
{
    // PRINTLINE
    char ipc_tracking_data[256];
    int data_len = 256;
    int delay_thresh = 25 * 1000;
    g_bFCIpcExit = 0;
    airvisen_trace("g_bFCIpcExit: %d \n", g_bFCIpcExit);
    while(!g_bFCIpcExit)
    {
       //printf("..\n");
        int rc = ipc_recv_message(fc_tracker_sock, ipc_tracking_data, data_len, 0);
       //printf("fc rc:%d\n", rc);
        if (rc > 0)
        {
           //printf("line:%d, file:%s\n", __LINE__, __FILE__);
            if (data_len < sizeof(airvisen_ipc_message_head_s)){
                // return -1;
               //printf("line:%d, file:%s\n", __LINE__, __FILE__);
                continue;
            }
            
           //printf("line:%d, file:%s\n", __LINE__, __FILE__);
            airvisen_track_message_tracking_result_s *msg = (airvisen_track_message_tracking_result_s*)ipc_tracking_data;
            if (msg->head.cmd != TRACKING_RESULT) 
            {

               //printf("line:%d, file:%s\n", __LINE__, __FILE__);
                continue;
            }
            if (data_len < msg->head.len) 
            {
               //printf("line:%d, file:%s\n", __LINE__, __FILE__);
                continue;
            }

            //test time delay
            unsigned long cur_time = get_reltime();
            // airvisen_trace("ipc cost time:%ld - %ld = %ld\n", cur_time, msg->pts, cur_time - msg->pts);
            if (cur_time - msg->pts > delay_thresh) 
            {
               //printf("line:%d, file:%s\n", __LINE__, __FILE__);
                continue;
            }
           //printf("line:%d, file:%s\n", __LINE__, __FILE__);
            // track_results = (airvisen_track_message_tracking_result_s *)malloc(sizeof(airvisen_track_message_tracking_result_s));
            //pthread_mutex_lock(&track_mutex);
            memcpy(&g_fc_track_results, &msg->tracking_results, sizeof(track_result_ipc_s));
            // g_fc_track_results.model_type = E_NN_MODE_TRACK;
            // g_fc_track_results.is_updated = VS_TRUE;
            g_fc_tracker_state = msg->cur_state;
            //pthread_mutex_unlock(&track_mutex);

             vs_uint16_t x1 =   g_fc_track_results.target_pos_x_norm;
             vs_uint16_t y1 =   g_fc_track_results.target_pos_y_norm;
             vs_uint16_t w1 =   g_fc_track_results.target_sz_x_norm;
             vs_uint16_t h1 =   g_fc_track_results.target_sz_y_norm;

             //< @Aaron, the following 2 lines are for debugging, you should call airvisen_fc_tracker_update_result() to retrieve the tracking results to your own memory and then processing it.
             airvisen_trace("INT16: %d, %d, %d, %d\n", x1, y1, w1, h1);
             airvisen_trace("NORM: %1.3f, %1.3f, %1.3f, %1.3f\n", (float)x1/65535, (float)y1/65535, (float)w1/65535, (float)h1/65535);

            
            continue;
        }else{
            airvisen_trace("tracking not updated!");
            continue;
        }
    }
}
#endif

