#ifndef ALGO_MESSAGE_H_
#define ALGO_MESSAGE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stdio.h"
#include "sample_common.h"
#include "airvisen_message.h"
/**
 * detection command
 */

#define IPC_INT_RANGE 65535

typedef enum _airvisen_camera_mode
{
    CAMMODE_SHORT_VI = 0,
    CAMMODE_LONG_VI = 1,
    CAMMODE_IR,
}airvisen_camera_mode;

typedef struct det_obj_ipc {
    int             x_norm;
    int             y_norm;
    int             w_norm;
    int             h_norm;
    vs_uint32_t             cid;
    vs_float_t              cprob;
    vs_uint32_t             color;
    long            obj_id;
} det_obj_ipc_s;

typedef struct detection_result_ipc{
    vs_uint32_t             obj_num;
    det_obj_ipc_s     objs[NN_DETECT_OBJ_MAX_NUM];
    unsigned long frame_pts;
    unsigned long frame_id;
    int cam_source; // 0: short focal lens, 1: long focal lens, 2:ir
} detection_result_ipc_s;

typedef struct airvisen_track_message_detection_result
{
    airvisen_ipc_message_head_s head;

    /*detection result*/
    // sample_nn_detection_result_s detection_results;
    detection_result_ipc_s detection_results;
    unsigned long pts;

}airvisen_track_message_detection_result_s;

typedef struct track_result_ipc{
    int              target_pos_x_norm;
    int              target_pos_y_norm;
    int              target_sz_x_norm;
    int              target_sz_y_norm;
    // vs_float_t              cls_score_max;
    // vs_bool_t               is_tracking;
}track_result_ipc_s;

typedef struct airvisen_track_message_tracking_result
{

    airvisen_ipc_message_head_s head;
    /*tracking result*/
    track_result_ipc_s tracking_results;
    int cur_state;
    unsigned long pts;
#ifdef DUAL_CAM
    int tracker_source;
#endif
}airvisen_track_message_tracking_result_s;

typedef struct airvisen_track_message_tracking_initbox
{

    airvisen_ipc_message_head_s head;
    /*detection result*/
    int t, l, w, h;
    //0 short focal lens, 1 long focal lens
    int tracker_source;
}airvisen_track_message_tracking_initbox_s;

typedef struct airvisen_track_message_camera_mode
{
    airvisen_ipc_message_head_s head;
    airvisen_camera_mode camera_mode;
}airvisen_track_message_camera_mode_s;

//server
void airvisen_start_tracker_ipc();
void airvisen_stop_tracker_ipc();
#ifdef FC_DISPATCHER_ENABLED
void airvisen_start_fc_tracker_ipc();
void airvisen_stop_fc_tracker_ipc();
#endif
void airvisen_start_detector_ipc();
void airvisen_stop_detector_ipc();

// int airvisen_recv_detection_result(char *data, int data_len, int flags, sample_nn_post_result_s* results);
// int airvisen_recv_tracker_update_result(char *data, int data_len, int flags, sample_nn_post_result_s* track_results);
int airvisen_detector_update_result(detection_result_ipc_s* p_detect_results);

#ifdef DUAL_CAM
    int airvisen_send_tracker_initbox(float l, float t, float w, float h, int tracker_source);
    int airvisen_recv_tracker_initbox(char *data, int data_len, int flags, float* initbox, int* tracker_source);
    int airvisen_send_tracker_ret(track_result_ipc_s* tracking_results, int cur_state, int tracker_source);
    int airvisen_tracker_update_result(track_result_ipc_s* p_track_results, int* p_cur_state, int* p_im_source);
    int airvisen_send_camera_mode(int camera_mode);
    // int airvisen_recv_camera_mode(char *data, int data_len, int flags, airvisen_camera_mode* camera_mode);
#else
    int airvisen_send_tracker_initbox(float l, float t, float w, float h);
    int airvisen_recv_tracker_initbox(char *data, int data_len, int flags, float* initbox);
    int airvisen_send_tracker_ret(track_result_ipc_s* tracking_results, int cur_state);
    int airvisen_tracker_update_result(track_result_ipc_s* p_track_results, int* p_cur_state);
#endif

void airvisen_run_detection_recv();
int airvisen_send_tracker_stop();

int airvisen_send_detector_start();
int airvisen_send_detector_stop();

//client
//< flight control
#ifdef FC_DISPATCHER_ENABLED
void airvisen_start_fc_tracker_client_ipc();
#endif
void airvisen_start_tracker_client_ipc();
void airvisen_start_detector_client_ipc();
int airvisen_send_detection_ret(detection_result_ipc_s* p_detection_results);

void airvisen_run_tracker_recv();
void airvisen_run_fc_tracker_recv();

#ifdef __cplusplus
}
#endif
#endif /*ALGO_MESSAGE_H_*/
