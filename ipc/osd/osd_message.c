#include "ipc.h"
#include "osd_message.h"


static int sock;
#define IR_CTRL_SOCKET_ADDRESS "ipc:///tmp/airvisen.osd.ipc"

void airvisen_start_osd_ctrl_ipc()
{
    sock = ipc_server_start(IR_CTRL_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
}


void airvisen_start_osd_ctrl_client_ipc()
{
    sock = ipc_client_start(IR_CTRL_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
    
}



void airvisen_update_osd_request(osd_message_s *result)
{
    result->head.cmd = OSD_INFO_CMD;
    result->head.len = sizeof(osd_message_s);
    ipc_send_message(sock, sizeof(osd_message_s), (char *)result);
}


void airvisen_show_osd_request()
{
    airvisen_ipc_message_head_s result;
    result.cmd = OSD_SWITCH_ON_CMD;
    result.len = sizeof(airvisen_ipc_message_head_s);
    ipc_send_message(sock, sizeof(airvisen_ipc_message_head_s), (char *)&result);
}

void airvisen_hide_osd_request()
{
    airvisen_ipc_message_head_s result;
    result.cmd = OSD_SWITCH_OFF_CMD;
    result.len = sizeof(airvisen_ipc_message_head_s);
    ipc_send_message(sock, sizeof(airvisen_ipc_message_head_s), (char *)&result);
}

void airvisen_change_osd_language_request()
{
    airvisen_ipc_message_head_s result;
    result.cmd = OSD_LANGUAGE_CMD;
    result.len = sizeof(airvisen_ipc_message_head_s);
    ipc_send_message(sock, sizeof(airvisen_ipc_message_head_s), (char *)&result);
}


void airvisen_stop_osd_ctrl_ipc()
{
    ipc_close(sock);
}


int dispatch_osd_ctrl_message(int data_len, char *data)
{
    if (data_len < sizeof(airvisen_ipc_message_head_s)){
        return -1;
    }
    
    airvisen_ipc_message_head_s *msg = (airvisen_ipc_message_head_s*)data;
    switch (msg->cmd)
    {
        case OSD_SWITCH_OFF_CMD:
        {
            printf("Recv OSD_SWITCH_OFF_CMD command\n");
            break;
        }
        case OSD_SWITCH_ON_CMD:
        {
            printf("Recv OSD_SWITCH_ON_CMD command\n");
            break;
        }
        case OSD_INFO_CMD:{
            printf("Recv OSD_INFO_CMD command\n");
            break;
        }
        case OSD_LANGUAGE_CMD:{
            printf("Recv OSD_LANGUAGE_CMD command\n");
            break;
        }
    default:
        printf("unsupport command: [%d]\n",msg->cmd);
        return -1;
    }
    return msg->cmd;
}
 

int airvisen_recv_osd_ctrl_message(char *data, int data_len)
{
    return ipc_recv_message(sock, data, data_len, 1);
}



/**
 * @param char *data: recv buf.
 * @param int data_len: recv buf length.
 * @param int flags: 0 indicate wait, 1 NNDONOTWAIT.
 */
int airvisen_recv_osd_message(char *data, int data_len, int flags)
{
    int rc = ipc_recv_message(sock, data, data_len,flags);
    if (rc > 0)
    {
        return dispatch_osd_ctrl_message(rc, data);
    }else{
        return rc;
    }
    
}