#include "ipc.h"
#include "stdio.h"
#include "store_jpeg_message.h"

static int sock;
#define STORE_JPEG_SOCKET_ADDRESS "ipc:///tmp/airvisen.storejpeg.ipc"

void airvisen_start_store_jpeg_ipc()
{
    sock = ipc_server_start(STORE_JPEG_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }    
}


void airvisen_start_store_jpeg_client_ipc()
{
    sock = ipc_client_start(STORE_JPEG_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
}

void airvisen_send_start_capture_command()
{
    airvisen_track_message_store_jpeg_s store_jpeg_msg = {0};
    store_jpeg_msg.head.cmd = START_JPEG_STORE;
    store_jpeg_msg.head.len = sizeof(airvisen_track_message_store_jpeg_s);
    ipc_send_message(sock, sizeof(airvisen_track_message_store_jpeg_s), (char *)&store_jpeg_msg);
}

void airvisen_send_stop_capture_command()
{
    airvisen_track_message_store_jpeg_s store_jpeg_msg = {0};
    store_jpeg_msg.head.cmd = STOP_JPEG_STORE;
    store_jpeg_msg.head.len = sizeof(airvisen_track_message_store_jpeg_s);
    ipc_send_message(sock, sizeof(airvisen_track_message_store_jpeg_s), (char *)&store_jpeg_msg);
}

void airvisen_stop_store_jpeg_ipc()
{
    ipc_close(sock);
}


static int dispatch_track_message(int data_len, char *data)
{
    if (data_len < sizeof(airvisen_ipc_message_head_s)){
        return -1;
    }
    
    airvisen_ipc_message_head_s *msg = (airvisen_ipc_message_head_s*)data;
    switch (msg->cmd)
    {
        case START_JPEG_STORE:{
            printf("Recv START_JPEG_STORE command\n");
            return START_JPEG_STORE;
        }
        break;  

        case STOP_JPEG_STORE:{
            printf("Recv STOP_JPEG_STORE command\n");
            return STOP_JPEG_STORE;
        }
        break;

    default:
        printf("unsupport command: [%d]\n",msg->cmd);
        return -1;
        break;
    }
}

int airvisen_recv_store_jpeg_message(char *data, int data_len)
{
    int rc = ipc_recv_message_wait(sock, data, data_len);
    if (rc > 0)
    {
        return dispatch_track_message(rc, data);
    }else{
        return rc;
    }
    
}
