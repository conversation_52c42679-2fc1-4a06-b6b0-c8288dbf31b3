#ifndef IR_CTRL_MESSAGE_H_
#define IR_CTRL_MESSAGE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stdio.h"
#include "airvisen_message.h"


typedef struct 
{
    airvisen_ipc_message_head_s head;
    //0 plus
    //1 minus
    //specified parameter;
    int mode; 
    //when mode == 2,use this zoom factor, eg. 800 => 8x
    int zoom_spec_factor;

}ir_video_zoom_message_s;



typedef struct 
{
    airvisen_ipc_message_head_s head;
    //0 disable
    //1 enable
    int state;
    
}ir_shutter_message_s;




typedef struct 
{
    airvisen_ipc_message_head_s head;

    // 0x00：白热（默认） 
    // 0x01：黑热 
    // 0x02：彩虹 
    // 0x03：铁红 
    // 0x04：冰火 
    // 0x05：熔岩
    int mode; 
}ir_pseudo_color_message_s;


/**
 * @brief Point struct, start from 0
 */
typedef struct {
    /// x position
    unsigned short x;
    /// y position
    unsigned short y;
}ir_point_t;

/**
 * @brief Line struct, start from 0
 */
typedef struct {
    /// start point's position
    ir_point_t start_point;
    /// end point's position
    ir_point_t end_point;
}ir_line_t;

/**
 * @brief Rectangle struct, start from 0
 */
typedef struct {
    /// start point's position
    ir_point_t start_point;
    /// end point's position
    ir_point_t end_point;
}ir_rect_t;

/**
 * @brief Maximum and minimum temperature information struct of the frame
 */
typedef struct
{
    /// maximum temperature value
    unsigned short max_temp;
    /// minimum temperature value
    unsigned short min_temp;
    /// maximum temperature point's position
    ir_point_t max_temp_point;
    /// minimum temperature point's position
    ir_point_t min_temp_point;
}ir_frame_temp_info_t;


// typedef struct 
// {
//     //measure type, 0. point 1 line 2 rect 3.max min in on frame
//     int mode;
//     //point
//     ir_point_t point;
// }ir_request_measure_point_s;


// typedef struct 
// {
//     //measure type, 0. point 1 line 2 rect 3.max min in on frame
//     int mode;
//     //point
//     ir_line_t line;
// }ir_request_measure_line_s;


// typedef struct 
// {
//     //measure type, 0. point 1 line 2 rect 3.max min in on frame
//     int mode;
//     //point
//     ir_rect_t point;
// }ir_request_measure_rect_s;


// typedef struct 
// {
//     //measure type, 0. point 1 line 2 rect 3.max min in on frame
//     int mode;
// }ir_request_measure_frame_s;


typedef struct 
{
    airvisen_ipc_message_head_s head;
    //measure type, 0. point 1 line 2 rect 3.max min in on frame
    int mode;
    ir_point_t point; //use for point
    ir_line_t line;
    ir_rect_t rect;
}ir_request_measure_temperature_s;





/**
 * @brief Temperature information struct, including average, maximum,
 * and minimum temperature value.
 */
typedef struct
{
    /// average temperature value
    unsigned short ave_temp;
    /// maximum temperature value
    unsigned short max_temp;
    /// minimum temperature value
    unsigned short min_temp;
}ir_temp_info_value_t;



/**
 * @brief Maximum and minimum temperature information struct of the frame
 */
typedef struct
{
    /// maximum temperature value
    unsigned short max_temp;
    /// minimum temperature value
    unsigned short min_temp;
    /// maximum temperature point's position
    ir_point_t max_temp_point;
    /// minimum temperature point's position
    ir_point_t min_temp_point;
}if_frame_temp_info_t;



typedef struct 
{
    airvisen_ipc_message_head_s head;
    //measure type, 0. point 1 line 2 rect 3.max min in on frame
    int mode;
    unsigned short point; //only use when mode is point
    ir_temp_info_value_t line; //only use when mode is line
    ir_temp_info_value_t rect; //only use when mode is rect
    if_frame_temp_info_t frame;//only use when mode is max min in on frame
}ir_response_measure_temperature_s;



void airvisen_start_ir_ctrl_ipc();
void airvisen_stop_ir_ctrl_ipc();
void airvisen_start_ir_ctrl_client_ipc();
void airvisen_start_ir_ctrl_command(int zoom_factor);
int dispatch_ir_ctrl_message(int data_len, char *data);
int airvisen_recv_ir_ctrl_message(char *data, int data_len);
int airvisen_recv_ir_message(char *data, int data_len, int flags);


void airvisen_send_video_zoom_command(int mode, int zoom_factor);
void airvisen_send_control_shutter_command(int state);
void airvisen_control_pseudo_color_command(int mode);
void airvisen_measure_point_temperature_request(ir_point_t point) ;
void airvisen_measure_line_temperature_request(ir_line_t line);
void airvisen_measure_rect_temperature_request(ir_rect_t rect);
void airvisen_measure_frame_temperature_request(int mode);
void airvisen_send_temperature_result(ir_response_measure_temperature_s *result);

#ifdef __cplusplus
}
#endif

#endif /*VIDEO_ZOOM_MESSAGE_H_*/