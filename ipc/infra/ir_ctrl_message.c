#include "ipc.h"
#include "ir_ctrl_message.h"


static int sock;
#define IR_CTRL_SOCKET_ADDRESS "ipc:///tmp/airvisen.infra.ipc"

void airvisen_start_ir_ctrl_ipc()
{
    sock = ipc_server_start(IR_CTRL_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
}


void airvisen_start_ir_ctrl_client_ipc()
{
    sock = ipc_client_start(IR_CTRL_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
    
}

void airvisen_send_video_zoom_command(int mode, int zoom_factor)
{
    ir_video_zoom_message_s video_zoom_msg = {0};
    video_zoom_msg.head.cmd = IR_VIDEO_ZOOM;
    video_zoom_msg.head.len = sizeof(ir_video_zoom_message_s);
    video_zoom_msg.mode = mode;
    video_zoom_msg.zoom_spec_factor = zoom_factor;
    ipc_send_message(sock, sizeof(ir_video_zoom_message_s), (char *)&video_zoom_msg);
}



void airvisen_send_control_shutter_command(int state)
{
    ir_shutter_message_s shutter = {0};
    shutter.head.cmd = IR_SHUTTER;
    shutter.head.len = sizeof(ir_shutter_message_s);
    shutter.state = state;
    ipc_send_message(sock, sizeof(ir_shutter_message_s), (char *)&shutter);
}


void airvisen_control_pseudo_color_command(int mode)
{
    ir_pseudo_color_message_s pseudo = {0};
    pseudo.head.cmd = IR_CTRL_PSEDUO_COLOR;
    pseudo.head.len = sizeof(ir_pseudo_color_message_s);
    pseudo.mode = mode;
    ipc_send_message(sock, sizeof(ir_pseudo_color_message_s), (char *)&pseudo);
}

void airvisen_measure_point_temperature_request(ir_point_t point) 
{
    ir_request_measure_temperature_s request = {0};
    request.head.cmd = IR_MEASURE_TEMPERATURE_REQUEST;
    request.head.len = sizeof(ir_request_measure_temperature_s);
    request.mode = 0;
    request.point = point;
    ipc_send_message(sock, sizeof(ir_request_measure_temperature_s), (char *)&request);
}


void airvisen_measure_line_temperature_request(ir_line_t line) 
{
    ir_request_measure_temperature_s request = {0};
    request.head.cmd = IR_MEASURE_TEMPERATURE_REQUEST;
    request.head.len = sizeof(ir_request_measure_temperature_s);
    request.mode = 1;
    request.line = line;
    ipc_send_message(sock, sizeof(ir_request_measure_temperature_s), (char *)&request);
}

void airvisen_measure_rect_temperature_request(ir_rect_t rect) 
{
    ir_request_measure_temperature_s request = {0};
    request.head.cmd = IR_MEASURE_TEMPERATURE_REQUEST;
    request.head.len = sizeof(IR_MEASURE_TEMPERATURE_REQUEST);
    request.mode = 2;
    request.rect = rect;
    ipc_send_message(sock, sizeof(ir_request_measure_temperature_s), (char *)&request);
}


void airvisen_measure_frame_temperature_request(int mode) 
{
    ir_request_measure_temperature_s request = {0};
    request.head.cmd = IR_MEASURE_TEMPERATURE_REQUEST;
    request.head.len = sizeof(ir_request_measure_temperature_s);
    request.mode = 3;
    ipc_send_message(sock, sizeof(ir_request_measure_temperature_s), (char *)&request);
}


void airvisen_send_temperature_result(ir_response_measure_temperature_s *result)
{
    result->head.cmd = IR_MEASURE_TEMPERATURE_RESPONSE;
    result->head.len = sizeof(ir_response_measure_temperature_s);
    ipc_send_message(sock, sizeof(ir_response_measure_temperature_s), (char *)result);
}


void airvisen_stop_ir_ctrl_ipc()
{
    ipc_close(sock);
}


int dispatch_ir_ctrl_message(int data_len, char *data)
{
    if (data_len < sizeof(airvisen_ipc_message_head_s)){
        return -1;
    }
    
    airvisen_ipc_message_head_s *msg = (airvisen_ipc_message_head_s*)data;
    switch (msg->cmd)
    {
        case IR_SHUTTER:
        {
            printf("Recv IR_ENABLE_SHUTTER command\n");
            break;
        }
        case IR_CTRL_PSEDUO_COLOR:
        {
            printf("Recv IR_CTRL_PSEDUO_COLOR command\n");
            break;
        }
        case IR_MEASURE_TEMPERATURE_RESPONSE:{
            printf("Recv IR_MEASURE_TEMPERATURE_RESPONSE command\n");
            break;
        }
        case IR_VIDEO_ZOOM:
        {
            printf("Recv IR_VIDEO_ZOOM command\n");
            break;

        }
    default:
        printf("unsupport command: [%d]\n",msg->cmd);
        return -1;
    }
    return msg->cmd;
}
 

int airvisen_recv_ir_ctrl_message(char *data, int data_len)
{
    return ipc_recv_message(sock, data, data_len, 1);
}



/**
 * @param char *data: recv buf.
 * @param int data_len: recv buf length.
 * @param int flags: 0 indicate wait, 1 NNDONOTWAIT.
 */
int airvisen_recv_ir_message(char *data, int data_len, int flags)
{
    int rc = ipc_recv_message(sock, data, data_len,flags);
    if (rc > 0)
    {
        return dispatch_ir_ctrl_message(rc, data);
    }else{
        return rc;
    }
    
}