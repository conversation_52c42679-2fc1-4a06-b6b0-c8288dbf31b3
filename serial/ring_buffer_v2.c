#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "ring_buffer_v2.h"

#define MIN(a, b) ((a) < (b) ? (a) : (b))

ring_buffer_v2_t* ring_buffer_v2_init(size_t capacity) {
    if (capacity == 0) return NULL;

    ring_buffer_v2_t *rb = (ring_buffer_v2_t*)malloc(sizeof(ring_buffer_v2_t));
    if (!rb) return NULL;

    rb->buffer = (uint8_t*)malloc(capacity);
    if (!rb->buffer) {
        free(rb);
        return NULL;
    }

    rb->capacity = capacity;
    rb->head = 0;
    rb->tail = 0;
    rb->count = 0;

    pthread_mutex_init(&rb->mutex, NULL);
    pthread_cond_init(&rb->cond_not_empty, NULL);

    return rb;
}

void ring_buffer_v2_destroy(ring_buffer_v2_t *rb) {
    if (!rb) return;

    pthread_mutex_destroy(&rb->mutex);
    pthread_cond_destroy(&rb->cond_not_empty);

    if (rb->buffer) free(rb->buffer);
    free(rb);
}

size_t ring_buffer_v2_write(ring_buffer_v2_t *rb, const uint8_t *data, size_t len) {
    if (!rb || !data || len == 0) return 0;
    
    // 如果要写入的数据本身就比缓冲区还大，我们只取其最后的部分
    if (len > rb->capacity) {
        data += (len - rb->capacity);
        len = rb->capacity;
    }

    pthread_mutex_lock(&rb->mutex);

    // 1. 计算覆盖
    // 如果写入后会超出容量，计算需要覆盖多少旧数据
    size_t free_space = rb->capacity - rb->count;
    if (len > free_space) {
        size_t overwrite_count = len - free_space;
        rb->tail = (rb->tail + overwrite_count) % rb->capacity;
        rb->count -= overwrite_count;
    }

    // 2. 写入数据（可能分两次 memcpy）
    size_t part1_len = MIN(len, rb->capacity - rb->head);
    memcpy(rb->buffer + rb->head, data, part1_len);

    size_t part2_len = len - part1_len;
    if (part2_len > 0) {
        memcpy(rb->buffer, data + part1_len, part2_len);
    }

    // 3. 更新 head 和 count
    rb->head = (rb->head + len) % rb->capacity;
    rb->count += len;

    // 4. 唤醒可能在等待数据的读取者
    pthread_cond_signal(&rb->cond_not_empty);

    pthread_mutex_unlock(&rb->mutex);
    return len;
}

size_t ring_buffer_v2_read(ring_buffer_v2_t *rb, uint8_t *buffer, size_t len) {
    if (!rb || !buffer || len == 0) return 0;

    pthread_mutex_lock(&rb->mutex);

    size_t read_bytes = 0;
    while (read_bytes < len) {
        // 等待直到有数据可读
        while (rb->count == 0) {
            pthread_cond_wait(&rb->cond_not_empty, &rb->mutex);
        }

        size_t bytes_to_read = MIN(len - read_bytes, rb->count);
        
        size_t part1_len = MIN(bytes_to_read, rb->capacity - rb->tail);
        memcpy(buffer + read_bytes, rb->buffer + rb->tail, part1_len);
        
        size_t part2_len = bytes_to_read - part1_len;
        if (part2_len > 0) {
            memcpy(buffer + read_bytes + part1_len, rb->buffer, part2_len);
        }
        
        rb->tail = (rb->tail + bytes_to_read) % rb->capacity;
        rb->count -= bytes_to_read;
        read_bytes += bytes_to_read;

        // 注意：这里不再需要 signal cond_not_full
    }
    
    pthread_mutex_unlock(&rb->mutex);
    return read_bytes;
}

// Peek 和 get_count 函数与 V1 基本相同，只是类型变为 v2
size_t ring_buffer_v2_peek(ring_buffer_v2_t *rb, uint8_t *buffer, size_t len) {
    if (!rb || !buffer || len == 0) return 0;
    pthread_mutex_lock(&rb->mutex);
    size_t bytes_to_peek = MIN(len, rb->count);
    if (bytes_to_peek > 0) {
        size_t current_tail = rb->tail;
        size_t part1_len = MIN(bytes_to_peek, rb->capacity - current_tail);
        memcpy(buffer, rb->buffer + current_tail, part1_len);
        size_t part2_len = bytes_to_peek - part1_len;
        if (part2_len > 0) {
            memcpy(buffer + part1_len, rb->buffer, part2_len);
        }
    }
    pthread_mutex_unlock(&rb->mutex);
    return bytes_to_peek;
}

size_t ring_buffer_v2_get_count(const ring_buffer_v2_t *rb) {
    if (!rb) return 0;
    pthread_mutex_lock((pthread_mutex_t*)&rb->mutex);
    size_t count = rb->count;
    pthread_mutex_unlock((pthread_mutex_t*)&rb->mutex);
    return count;
}




/**
 * @brief 实现指定索引的 peek 功能
 */
int ring_buffer_v2_peek_at(ring_buffer_v2_t *rb,  uint8_t *data, size_t index) {
    if (!rb || !data) {
        return 0; // 失败：无效的指针
    }

    int result = 0;
    pthread_mutex_lock(&rb->mutex);

    // 检查索引是否在有效范围内
    if (index < rb->count) {
        // 计算物理索引
        size_t physical_index = (rb->tail + index) % rb->capacity;
        *data = rb->buffer[physical_index];
        result = 1; // 成功
    }

    pthread_mutex_unlock(&rb->mutex);
    return result;
}


/**
 * @brief 实现等待数据的功能
 */
void ring_buffer_v2_wait_for_data(ring_buffer_v2_t *rb) {
    if (!rb) {
        return;
    }

    pthread_mutex_lock(&rb->mutex);

    // 使用 while 循环来防止“虚假唤醒” (spurious wakeup)
    // 这是使用条件变量的标准、健壮的写法
    while (rb->count == 0) {
        // 当 count 为 0 时，在 cond_not_empty 上等待。
        // pthread_cond_wait 会原子地：
        // 1. 解锁 mutex
        // 2. 将线程置于休眠状态
        // 3. 当被唤醒时，重新锁住 mutex
        pthread_cond_wait(&rb->cond_not_empty, &rb->mutex);
    }

    pthread_mutex_unlock(&rb->mutex);
}