#ifndef SERIAL_H
#define SERIAL_H

#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/signal.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <limits.h>
#include <string.h>
//#include "ringbuffer.h"
#include <pthread.h>
#include "ring_buffer_v2.h"


typedef enum
{
    E_BaudRate_2400,
    E_BaudRate_4800,
    E_BaudRate_9600,
    E_BaudRate_19200,
    E_BaudRate_38400,
    E_BaudRate_57600,
    E_BaudRate_115200,
    E_BaudRate_460800,
    E_BaudRate_921600,
}E_BaudRate;  //波特率

typedef enum
{
    E_DataSize_5,
    E_DataSize_6,
    E_DataSize_7,
    E_DataSize_8,
}E_DataSize;  //数据位

typedef enum
{
    E_Parity_None,
    E_Parity_Odd,
    <PERSON>_<PERSON>rity_Even,
}E_Parity;  //校验位

typedef enum
{
    E_StopBit_1,
    E_StopBit_2,
}E_StopBit;  //停止位

typedef struct {

    int fd;

    //1 stop
    int stop;
    // Mutex for the buffer
    //pthread_mutex_t mutex;
    // ring buffer
    //ring_buffer_t m_ring_buffer;
    ring_buffer_v2_t *k40rb;

    ring_buffer_v2_t *mavlinkrb;

} serial_context_t;

// 串口初始化
int serial_init(const char *device, E_BaudRate Bps, E_DataSize DataSize, E_Parity Parity, E_StopBit StopBit);
// 读取串口数据
ssize_t serial_read(int fd, void *buffer, size_t size);
// 写入数据到串口
ssize_t serial_write(int fd, const void *buffer, size_t size);

/// @brief get serial port status
/// @param fd 
/// @return 
int is_serial_open(int fd);

/// @brief close serial port
void serial_close(int fd);

#endif // SERIAL_H
