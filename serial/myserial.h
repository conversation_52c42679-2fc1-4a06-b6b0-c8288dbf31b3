/* ----------- myserial.h ----------- */
#ifndef MYSERIAL_H
#define MYSERIAL_H

#include <asm/ioctls.h>
#include <asm/termbits.h> 
#include <sys/ioctl.h>
#include <fcntl.h>
#include <unistd.h>
#include <pthread.h>
#include "ring_buffer_v2.h"

typedef enum {
    SERIAL_BAUD_921600 = 921600,
    SERIAL_BAUD_115200 = 115200
} SerialBaudRate;

typedef enum {
    SERIAL_DATA_8 = 8,
    SERIAL_DATA_7 = 7
} SerialDataBits;

typedef enum {
    SERIAL_PARITY_NONE = 0,
    SERIAL_PARITY_ODD = 1,
    SERIAL_PARITY_EVEN = 2
} SerialParity;

typedef enum {
    SERIAL_STOP_1 = 1,
    SERIAL_STOP_2 = 2
} SerialStopBits;

typedef struct {
    int fd;
    struct termios2 tio;
} SerialPort;


typedef struct {

    int fd;

    //1 stop
    int stop;
    // Mutex for the buffer
    pthread_mutex_t mutex;
    // ring buffer
    ring_buffer_v2_t *m_ring_buffer;

} gimbal_serial_context_t;


// 初始化串口
int gimbal_serial_init(const char* device, SerialBaudRate baudrate,
                      SerialDataBits data_bits, SerialParity parity,
                      SerialStopBits stop_bits);

// 发送数据
ssize_t gimbal_serial_write(int fd, const unsigned char* data, size_t len);

// 接收数据
ssize_t gimbal_serial_read(int fd, unsigned char* buffer, size_t len);

// 关闭串口
void gimbal_serial_close(int fd);

#endif // MYSERIAL_H