#ifndef RING_BUFFER_V2_H
#define RING_BUFFER_V2_H

#include <stddef.h>
#include <stdint.h>
#include <pthread.h>

// V2 环形缓冲区结构体 (移除了 cond_not_full)
typedef struct {
    uint8_t *buffer;
    size_t capacity;
    size_t head;
    size_t tail;
    size_t count;
    pthread_mutex_t mutex;
    pthread_cond_t cond_not_empty; // 读取者等待的条件
} ring_buffer_v2_t;

/**
 * @brief 初始化环形缓冲区 (V2)
 * @param capacity 缓冲区的总大小（字节）
 * @return ring_buffer_v2_t* 成功则返回指针，失败返回 NULL
 */
ring_buffer_v2_t* ring_buffer_v2_init(size_t capacity);

/**
 * @brief 销毁环形缓冲区 (V2)
 * @param rb 指向要销毁的环形缓冲区对象的指针
 */
void ring_buffer_v2_destroy(ring_buffer_v2_t *rb);

/**
 * @brief 向缓冲区写入数据（非阻塞，满时覆盖）
 * 如果缓冲区空间不足，此函数会覆盖最老的数据以存入新数据。
 * @param rb 环形缓冲区指针
 * @param data 要写入的数据的指针
 * @param len 要写入的数据长度（字节）
 * @return size_t 实际写入的字节数（总是等于 len）
 */
size_t ring_buffer_v2_write(ring_buffer_v2_t *rb, const uint8_t *data, size_t len);

/**
 * @brief 从缓冲区读取数据（阻塞操作）
 * 如果缓冲区没有数据，此函数会阻塞，直到有数据为止。
 * @param rb 环形缓冲区指针
 * @param buffer 用于存放读取数据的缓冲区
 * @param len 想要读取的数据长度（字节）
 * @return size_t 实际读取的字节数（总是等于 len）
 */
size_t ring_buffer_v2_read(ring_buffer_v2_t *rb, uint8_t *buffer, size_t len);

/**
 * @brief "窥探"缓冲区中的数据，但不移动读指针（非阻塞）
 * @param rb 环形缓冲区指针
 * @param buffer 用于存放窥探数据的缓冲区
 * @param len 想要窥探的数据长度（字节）
 * @return size_t 实际窥探到的字节数
 */
size_t ring_buffer_v2_peek(ring_buffer_v2_t *rb, uint8_t *buffer, size_t len);

/**
 * @brief 获取当前缓冲区中已用字节数
 * @param rb 环形缓冲区指针
 * @return size_t 已用字节数
 */
size_t ring_buffer_v2_get_count(const ring_buffer_v2_t *rb);




/**
 * @brief "窥探"（Peeks）环形缓冲区中指定索引位置的单个字节。
 * 这是一个非阻塞操作，并且不会移动读指针。
 * @param rb 环形缓冲区指针
 * @param index 要窥探的逻辑索引（0表示最老的数据）
 * @param data 用于存放窥探到的字节的指针
 * @return int 成功返回1，如果索引越界或缓冲区为空则返回0。
 */
int ring_buffer_v2_peek_at(ring_buffer_v2_t *rb, uint8_t *data, size_t index);



/**
 * @brief 等待直到缓冲区中有数据可读
 * 如果缓冲区为空，此函数将阻塞调用线程，直到有数据被写入。
 * 如果缓冲区中已有数据，此函数将立即返回。
 * @param rb 环形缓冲区指针
 */
void ring_buffer_v2_wait_for_data(ring_buffer_v2_t *rb);
#endif // RING_BUFFER_V2_H