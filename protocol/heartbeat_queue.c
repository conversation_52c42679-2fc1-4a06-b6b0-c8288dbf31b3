#include "heartbeat_queue.h"

heartbeat_queue_t g_heartbeat_queue;

// 初始化队列
void heartbeat_queue_init(heartbeat_queue_t *q, int capacity) {
    q->buf = (fcs_heartbeat_data_fields_t*)malloc(capacity * sizeof(fcs_heartbeat_data_fields_t));
    q->capacity = capacity;
    q->head = q->tail = q->size = 0;
    pthread_mutex_init(&q->mutex, NULL);
    pthread_cond_init(&q->not_empty, NULL);
    pthread_cond_init(&q->not_full, NULL);  // 新增条件变量
}

// 清理队列
void heartbeat_queue_destroy(heartbeat_queue_t *q) {
    if (q->buf) {
        free(q->buf);
        q->buf = NULL;
    }
    pthread_mutex_destroy(&q->mutex);
    pthread_cond_destroy(&q->not_empty);
    pthread_cond_destroy(&q->not_full);
}

// 高性能推入操作（非阻塞）
int heartbeat_queue_push(heartbeat_queue_t *q, const fcs_heartbeat_data_fields_t *data) {
    pthread_mutex_lock(&q->mutex);
    
    // 队列已满时阻塞等待
    while (q->size == q->capacity) {
        pthread_cond_wait(&q->not_full, &q->mutex);
    }
    
    // 直接内存拷贝（最高效的方式）
    memcpy(&q->buf[q->tail], data, sizeof(fcs_heartbeat_data_fields_t));
    
    // 更新队列指针
    q->tail = (q->tail + 1) % q->capacity;
    q->size++;
    
    // 通知消费者
    pthread_cond_signal(&q->not_empty);
    pthread_mutex_unlock(&q->mutex);
    return 0;
}

// 阻塞式取出
int heartbeat_queue_pop(heartbeat_queue_t *q, fcs_heartbeat_data_fields_t *result) {
    pthread_mutex_lock(&q->mutex);
    
    // 队列为空时阻塞等待
    while (q->size == 0) {
        pthread_cond_wait(&q->not_empty, &q->mutex);
    }
    
    // 直接内存拷贝
    memcpy(result, &q->buf[q->head], sizeof(fcs_heartbeat_data_fields_t));
    
    // 更新队列指针
    q->head = (q->head + 1) % q->capacity;
    q->size--;
    
    // 通知生产者
    pthread_cond_signal(&q->not_full);
    pthread_mutex_unlock(&q->mutex);
    return 0;
}

// 非阻塞式取出（立即返回）
int heartbeat_queue_try_pop(heartbeat_queue_t *q, fcs_heartbeat_data_fields_t *result) {
    pthread_mutex_lock(&q->mutex);
    
    if (q->size == 0) {
        pthread_mutex_unlock(&q->mutex);
        return -1;  // 队列为空
    }
    
    memcpy(result, &q->buf[q->head], sizeof(fcs_heartbeat_data_fields_t));
    q->head = (q->head + 1) % q->capacity;
    q->size--;
    
    pthread_cond_signal(&q->not_full);
    pthread_mutex_unlock(&q->mutex);
    return 0;
}

// 全局队列接口
int global_heartbeat_queue_push(const fcs_heartbeat_data_fields_t *data) {
    return heartbeat_queue_push(&g_heartbeat_queue, data);
}

int global_heartbeat_queue_pop(fcs_heartbeat_data_fields_t *result) {
    return heartbeat_queue_pop(&g_heartbeat_queue, result);
}

int global_heartbeat_queue_try_pop(fcs_heartbeat_data_fields_t *result) {
    return heartbeat_queue_try_pop(&g_heartbeat_queue, result);
}

int start_heartbeat_queue(void) {
    heartbeat_queue_init(&g_heartbeat_queue, QUEUE_CAPACITY);
    return 0;
}

int stop_heartbeat_queue(void) {
    heartbeat_queue_destroy(&g_heartbeat_queue);
    return 0;
}