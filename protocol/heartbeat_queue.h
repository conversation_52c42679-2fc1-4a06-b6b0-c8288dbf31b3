#ifndef HEARTBEAT_QUEUE_H
#define HEARTBEAT_QUEUE_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>
#include <stdint.h>
#include "gimbal_protocol.h"
// 最大队列长度
#define QUEUE_CAPACITY 5  // 根据实际需求调整


// 线程安全队列结构
typedef struct {
    fcs_heartbeat_data_fields_t *buf;  // 动态分配的缓冲区
    int          capacity;    // 队列容量
    int          head;        // 下一个要 pop 的索引
    int          tail;        // 下一个要 push 的索引
    int          size;        // 当前队列中数据数量
    pthread_mutex_t  mutex;
    pthread_cond_t   not_empty;
    pthread_cond_t   not_full;  // 新增条件变量
} heartbeat_queue_t;

// 全局队列
extern heartbeat_queue_t g_heartbeat_queue;

int start_heartbeat_queue(void);
int stop_heartbeat_queue(void);

// 高性能队列操作
int heartbeat_queue_push(heartbeat_queue_t *q, const fcs_heartbeat_data_fields_t *data);
int heartbeat_queue_pop(heartbeat_queue_t *q, fcs_heartbeat_data_fields_t *result);
int heartbeat_queue_try_pop(heartbeat_queue_t *q, fcs_heartbeat_data_fields_t *result);

// 全局队列接口
int global_heartbeat_queue_push(const fcs_heartbeat_data_fields_t *data);
int global_heartbeat_queue_pop(fcs_heartbeat_data_fields_t *result);
int global_heartbeat_queue_try_pop(fcs_heartbeat_data_fields_t *result);

#endif