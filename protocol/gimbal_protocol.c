#include "gimbal_protocol.h"
#include "gimbal_message.h"
#include <stdint.h>
#include <stddef.h>
#include "mavlink.h"
#include "dronepod_k40t_message.h"
#include "report_message.h"
#define LOG_LEVEL LOG_LEVEL_ERROR
#include "log_utils.h"

static uint16_t g_v1_send_seq = 0;
static unsigned int pitch_dial_speed = 155;
static unsigned int yaw_dial_speed = 155;
static float zoom_factor = 1;

// (可以添加到你的消息处理 .c 文件中, 例如 dronepod_new_protocol_message.c)
// #include "new_protocol_v1.h" // 包含上面的结构体定义
// #include "crc_utils.h"    // 包含 calc_crc16_new_protocol 的声明


// uint16_t crc16_ccitt(uint8_t *data, size_t length) {
//     uint16_t crc = 0xFFFF; // 初始值
//     for (size_t i = 0; i < length; i++) {
//         crc ^= (uint16_t)data[i] << 8; // 将当前字节与CRC寄存器的高8位进行异或
//         for (uint8_t j = 0; j < 8; j++) {
//             if (crc & 0x8000) { // 如果最高位为1
//                 crc = (crc << 1) ^ 0x1021; // 左移并异或多项式0x1021
//             } else {
//                 crc <<= 1; // 左移
//             }
//         }
//     }
//     return crc;
// }

// 和校验函数
static uint8_t calculate_checksum_v1(const header_v1_t *header) {
    uint8_t sum = 0;
    sum += header->sof;
    sum += (uint8_t)(header->ver_len.len & 0xFF);          // VER_LEN 的低字节
    sum += (uint8_t)((header->ver_len.len >> 8) & 0x03) |  // VER_LEN 的高字节 (len部分)
           (uint8_t)((header->ver_len.ver << 2) & 0xFC);   // VER_LEN 的高字节 (ver部分)
    return sum;
}


// 和校验函数 (用于帧头前3字节: SOF + 2字节的VER_LEN)
static uint8_t calculate_header_checksum_v1(const uint8_t* header_start) {
    // header_start 指向 SOF
    // header_start[0] 是 SOF
    // header_start[1] 是 VER_LEN 的第一个字节 (小端时是低字节)
    // header_start[2] 是 VER_LEN 的第二个字节 (小端时是高字节)
    return header_start[0] + header_start[1] + header_start[2];
}

/**
 * @brief 封装V1协议的飞控心跳包 (使用用户提供的完整帧结构体)
 *
 * @param fcs_payload_data  指向包含飞控心跳特定数据字段的结构体指针
 * @param seq               本条消息的序列号
 * @param sender_id         发送方ID (例如 SENDER_ID_FC_V1)
 * @param receiver_id       接收方ID (例如 RECEIVER_ID_GIMBAL_V1)
 * @param out_frame         指向待填充的完整帧结构体 (packet_fcs_heartbeat_v1_t) 的指针
 * @return int              成功则返回总帧长 (sizeof(packet_fcs_heartbeat_v1_t))，失败则返回0或负数
 */
int package_fcs_heartbeat_v1_complete(
    const fcs_heartbeat_data_fields_t *fcs_payload_data,
    uint16_t seq,
    uint8_t sender_id,
    uint8_t receiver_id,
    packet_fcs_heartbeat_v1_t *out_frame)
{
    if (fcs_payload_data == NULL || out_frame == NULL) {
        printf("错误: 输入参数为空指针\n");
        return -1;
    }

    // 获取总帧长，这个长度将写入header.ver_len.len
    uint16_t total_frame_length = sizeof(packet_fcs_heartbeat_v1_t); // 应为 51 字节

    // --- 1. 填充帧头 (out_frame->header) ---
    out_frame->header.sof = 0xAA;
    out_frame->header.ver_len.ver = 1; // 协议版本固定为1
    out_frame->header.ver_len.len = total_frame_length ;

    // 计算并填充 CHECKSUM (对SOF + VER_LEN的两个字节)
    // VER_LEN 是一个16位成员，包含两个位域。在小端机器上，它的内存表示是低字节在前。
    // 我们需要对SOF和这两个字节求和。
    // (uint8_t*)out_frame 指向SOF
    out_frame->header.check_sum = calculate_header_checksum_v1((uint8_t*)out_frame);

    //out_frame->header.seq = htole16(seq); // 确保序列号是小端
    out_frame->header.seq = (seq); // 确保序列号是小端


    out_frame->header.status.encrypt = 0; // 心跳包数据通常不加密
    out_frame->header.status.reserved = 0;
    out_frame->header.status.reserved1 = 0;

    // SENDER 和 RECEIVER (结构体赋值或memcpy)
    // 假设sender_id 和 receiver_id 的位域结构与header中的定义匹配
    //memcpy(&out_frame->header.sender, &sender_id, sizeof(out_frame->header.sender));
    //memcpy(&out_frame->header.receiver, &receiver_id, sizeof(out_frame->header.receiver));
    out_frame->header.sender.device = 0x2;
    out_frame->header.sender.index = 0x0;
    out_frame->header.receiver.device = 0x1;
    out_frame->header.receiver.index = 0x0;

    out_frame->header.cmd_set = CMD_SET_FCS_V1;      // 飞控指令集 (0x02)
    out_frame->header.cmd_id = CMD_ID_FCS_HEARTBEAT_V1; // 心跳包指令ID (0x00)

    // --- 2. 填充飞控心跳的特定数据字段 ---
    // 使用 memcpy 将 fcs_payload_data 拷贝到 out_frame 中对应的数据字段位置
    // 这些字段紧跟在 header 之后
    memcpy((uint8_t*)out_frame + sizeof(header_v1_t),
           fcs_payload_data,
           sizeof(fcs_heartbeat_data_fields_t));
    // 或者逐个赋值 (如果字段名匹配且你想更清晰地看到赋值过程)
    // out_frame->fcs_pitch = htole16(fcs_payload_data->fcs_pitch);
    // out_frame->fcs_roll = htole16(fcs_payload_data->fcs_roll);
    // ... 对所有 int16_t 和 int8_t 字段进行赋值 (注意字节序)
    // memcpy(out_frame->reserve, fcs_payload_data->reserve, sizeof(out_frame->reserve));


    // --- 3. 计算并填充帧尾的 CRC16 ---
    // CRC16 覆盖范围: 从SOF开始，到DATA的末尾 (即整个帧除去最后2字节的CRC16本身)
    // 长度 = total_frame_length - sizeof(out_frame->crc16)
    //       = sizeof(header_v1_t) + sizeof(fcs_heartbeat_data_fields_t)
    uint16_t crc_calculation_len = total_frame_length - sizeof(out_frame->crc16); // 49 字节

    // 调用你项目中实现的V1协议CRC16函数
    uint16_t calculated_crc16_be = calc_crc16((uint8_t*)out_frame, crc_calculation_len);

    // calc_crc16_v1_protocol 返回的是 (high_byte << 8 | low_byte) 即大端
    // 我们需要将其以小端方式存入 out_frame->crc16
    // 如果 out_frame->crc16 是 uint16_t，并且当前系统是小端，我们需要存入字节交换后的值
    // 或者直接将字节放入正确位置
    // out_frame->crc16 = ((calculated_crc16_be & 0xFF00) >> 8) | // Low byte
    //                    ((calculated_crc16_be & 0x00FF) << 8);  // High byte
    // 更简单的方式（假设目标是小端存储）:
    //out_frame->crc16 = htole16(calculated_crc16_be);
    out_frame->crc16 = (calculated_crc16_be);


    return total_frame_length;
}


// 示例：
// extern uint16_t get_next_v1_sequence(void); // 获取V1协议序列号的函数
// extern void send_v1_message(uint8_t *buffer, int length); // 实际发送函数
// extern uint16_t calc_crc16_new_protocol(uint8_t *pbuf, uint16_t len); // 确保此函数已链接

// void send_fcs_heartbeat_v1_example() {
//     fcs_heartbeat_data_fields_t current_fcs_data;
//     // 1. 填充 current_fcs_data 的所有字段
//     current_fcs_data.fcs_pitch = htole16(123); // 1.23度, 假设输入是主机序, 转为小端
//     current_fcs_data.fcs_roll = htole16(45);  // -0.45度
//     current_fcs_data.fcs_yaw = htole16(17); // 175.00度
//     current_fcs_data.fcs_yaw_ref = htole16(18);
//     current_fcs_data.fcs_accx = htole16(10);   // 0.01 G (假设单位处理后)
//     current_fcs_data.fcs_accy = htole16(20);
//     current_fcs_data.fcs_accz = htole16(91);  // 0.981 G (假设单位处理后)
//     current_fcs_data.gb_ctrl_mode = 2; // 速度模式
//     current_fcs_data.gb_pitch_ref = htole16(40);     
//     current_fcs_data.gb_roll_ref = htole16(40);
//     current_fcs_data.gb_yaw_ref = htole16(50); // 5 度/秒
//     current_fcs_data.fcs_state = 0x81; // 飞机起飞, 飞控数据有效
//     current_fcs_data.fcs_yaw_w_ref = htole16(0);
//     current_fcs_data.fcs_yaw_rc_cmd = 0;
//     memset(current_fcs_data.reserve, 0, sizeof(current_fcs_data.reserve));

//     packet_fcs_heartbeat_v1_t frame_to_send;
//     uint16_t seq_to_send = g_v1_send_seq++; // 获取并递增序列号

//     int frame_length = package_fcs_heartbeat_v1_complete(
//         &current_fcs_data,
//         seq_to_send,
//         SENDER_ID_FC,        // 飞控作为发送者
//         RECEIVER_ID_GIMBAL,  // 云台作为接收者 (根据实际情况修改)
//         &frame_to_send
//     );

//     if (frame_length > 0) {
//         printf("V1 飞控心跳包 (修订版) 封装成功, 长度: %d bytes.\n", frame_length);
//         printf("  SOF: 0x%02X\n", frame_to_send.header.sof);
//         printf("  Ver: %u, Len: %u\n", frame_to_send.header.ver_len.ver, frame_to_send.header.ver_len.len);
//         printf("  Checksum: 0x%02X\n", frame_to_send.header.check_sum);
//         printf("  Seq: %u\n", htole16(frame_to_send.header.seq)); // 转回主机序打印
//         printf("  Sender: Dev=%u, Idx=%u\n", frame_to_send.header.sender.device, frame_to_send.header.sender.index);
//         printf("  Receiver: Dev=%u, Idx=%u\n", frame_to_send.header.receiver.device, frame_to_send.header.receiver.index);
//         printf("  CmdSet: 0x%02X, CmdID: 0x%02X\n", frame_to_send.header.cmd_set, frame_to_send.header.cmd_id);
//         printf("  FCS Pitch: %.2f\n", (float)htole16(frame_to_send.fcs_pitch)/100.0);
//         // ... 打印其他数据字段 (记得转换字节序回来如果需要)
//         printf("  CRC16: 0x%04X\n", htole16(frame_to_send.crc16)); // 转回主机序打印 (假设原CRC函数返回BE)

//         print_fuffer_in_Hex(&frame_to_send,frame_length);
//         send_gimbal_message(&frame_to_send, frame_length);
//         // actual_serial_send((uint8_t*)&frame_to_send, frame_length);
//     } else {
//         printf("V1 飞控心跳包 (修订版) 封装失败.\n");
//     }
// }

// 全局变量，保持云台状态上下文
static fcs_heartbeat_data_fields_t g_fcs_data = {0};

// 通用的发送函数，接收控制参数
static void send_fcs_command(const fcs_heartbeat_data_fields_t *cmd_data) 
{
    packet_fcs_heartbeat_v1_t frame_to_send;
    uint16_t seq_to_send = g_v1_send_seq++; // 获取并递增序列号

    int frame_length = package_fcs_heartbeat_v1_complete(
        cmd_data,
        seq_to_send,
        SENDER_ID_FC,        // 飞控作为发送者
        RECEIVER_ID_GIMBAL,  // 云台作为接收者
        &frame_to_send
    );

    if (frame_length > 0) {
        //printf("  CRC16: 0x%04X\n", (frame_to_send.crc16));
        //print_fuffer_in_Hex(&frame_to_send, frame_length);
        send_gimbal_message(&frame_to_send, frame_length);
    } else {
        printf("V1 飞控心跳包封装失败.\n");
    }
}

// 初始化默认的心跳数据
static void init_default_fcs_data(fcs_heartbeat_data_fields_t *data)
{
    memset(data, 0, sizeof(fcs_heartbeat_data_fields_t));
    // 只需要初始化必要的字段，其他保持为0
    data->fcs_state = 0;
}

// 更新全局状态
static void update_gimbal_state(int yaw, int pitch, uint8_t ctrl_mode)
{
    // 保留其他字段的值，只更新需要的字段
    g_fcs_data.gb_ctrl_mode = ctrl_mode;
    g_fcs_data.gb_pitch_ref = pitch * 100;
    g_fcs_data.gb_yaw_ref = yaw * 100;
}

// 返回中心位置
void send_fcs_return_center() 
{
    init_default_fcs_data(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 3;
    g_fcs_data.gb_yaw_ref = 0;
    g_fcs_data.gb_pitch_ref = 0; // 特定值，保持原代码逻辑
    //g_fcs_data.gb_pitch_ref = 0x0bb8; // 特定值，保持原代码逻辑
    send_fcs_command(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 0;
}

// 立即向下看
void send_fcs_look_down_at_once() 
{
    init_default_fcs_data(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 2;
    g_fcs_data.gb_pitch_ref = -(0x2823); // 特定值，保持原代码逻辑
    send_fcs_command(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 0;
}

// 向左看
void send_fcs_look_left() 
{
    g_fcs_data.gb_ctrl_mode = 2;
    //g_fcs_data.gb_yaw_ref = g_fcs_data.gb_yaw_ref - 100;
    //printf("yaw_dial_speed - (int)(zoom_factor * 10) : %d\n", yaw_dial_speed - (int)(zoom_factor * 10));
    g_fcs_data.gb_yaw_ref = g_fcs_data.gb_yaw_ref - (yaw_dial_speed - (int)(zoom_factor * 10)); // 减少1度
    //printf("left : %d\n", g_fcs_data.gb_yaw_ref);
    send_fcs_command(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 0;
}

// 向右看
void send_fcs_look_right() 
{
    g_fcs_data.gb_ctrl_mode = 2;
    //g_fcs_data.gb_yaw_ref = g_fcs_data.gb_yaw_ref + 100;
    //printf("yaw_dial_speed - (int)(zoom_factor * 10) : %d\n", yaw_dial_speed - (int)(zoom_factor * 10));
    g_fcs_data.gb_yaw_ref = g_fcs_data.gb_yaw_ref + (yaw_dial_speed - (int)(zoom_factor * 10)); // 增加1度
    //printf("right : %d\n", g_fcs_data.gb_yaw_ref);
    send_fcs_command(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 0;
}

// 向上看
void send_fcs_look_up() 
{
    g_fcs_data.gb_ctrl_mode = 2;
    g_fcs_data.gb_pitch_ref = g_fcs_data.gb_pitch_ref + (pitch_dial_speed - (int)(zoom_factor * 10)); // 增加1度
    send_fcs_command(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 0;
}

// 向下看
void send_fcs_look_down() 
{
    g_fcs_data.gb_ctrl_mode = 2;
    g_fcs_data.gb_pitch_ref = g_fcs_data.gb_pitch_ref - (pitch_dial_speed - (int)(zoom_factor * 10)); // 减少1度
    send_fcs_command(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 0;
}

// 原始示例函数，保持兼容性
void send_fcs_heartbeat_v1_example() 
{
    init_default_fcs_data(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 3;
    g_fcs_data.gb_pitch_ref = 0x0bb8;
    send_fcs_command(&g_fcs_data);
    g_fcs_data.gb_ctrl_mode = 0;
}


#if 0
void send_fcs_heartbeat_v1_example() {

    /**
     * 
     AA 35 04 E3 00 00 00 10
     08 02 00 00 00 00 00 00 
     00 00 00 00 00 00 00 00 
     00 02 B8 0B 00 00 00 00 
     00 00 00 00 00 00 00 00 
     00 00 00 00 00 00 00 00 
     00 00 00 3B E8
     */
    fcs_heartbeat_data_fields_t current_fcs_data;
    // 1. 填充 current_fcs_data 的所有字段
    current_fcs_data.fcs_pitch = 0; // 1.23度, 假设输入是主机序, 转为小端
    current_fcs_data.fcs_roll = 0;  // -0.45度
    current_fcs_data.fcs_yaw = 0; // 175.00度
    current_fcs_data.fcs_yaw_ref = 0;
    current_fcs_data.fcs_accx = 0;   // 0.01 G (假设单位处理后)
    current_fcs_data.fcs_accy = (0);
    current_fcs_data.fcs_accz = (0);  // 0.981 G (假设单位处理后)
    current_fcs_data.gb_ctrl_mode = 3; // 速度模式
    current_fcs_data.gb_pitch_ref = (0x0bb8);     
    current_fcs_data.gb_roll_ref = (0);
    current_fcs_data.gb_yaw_ref = (0); // 5 度/秒
    current_fcs_data.fcs_state = 0; // 飞机起飞, 飞控数据有效
    current_fcs_data.fcs_yaw_w_ref = (0);
    current_fcs_data.fcs_yaw_rc_cmd = 0;
    memset(current_fcs_data.reserve, 0, sizeof(current_fcs_data.reserve));

    packet_fcs_heartbeat_v1_t frame_to_send;
    uint16_t seq_to_send = g_v1_send_seq++; // 获取并递增序列号

    int frame_length = package_fcs_heartbeat_v1_complete(
        &current_fcs_data,
        seq_to_send,
        SENDER_ID_FC,        // 飞控作为发送者
        RECEIVER_ID_GIMBAL,  // 云台作为接收者 (根据实际情况修改)
        &frame_to_send
    );

    if (frame_length > 0) {
        printf("V1 飞控心跳包 (修订版) 封装成功, 长度: %d bytes.\n", frame_length);
        printf("  SOF: 0x%02X\n", frame_to_send.header.sof);
        printf("  Ver: %u, Len: %u\n", frame_to_send.header.ver_len.ver, frame_to_send.header.ver_len.len);
        printf("  Checksum: 0x%02X\n", frame_to_send.header.check_sum);
        printf("  Seq: %u\n", (frame_to_send.header.seq)); // 转回主机序打印
        printf("  Sender: Dev=%u, Idx=%u\n", frame_to_send.header.sender.device, frame_to_send.header.sender.index);
        printf("  Receiver: Dev=%u, Idx=%u\n", frame_to_send.header.receiver.device, frame_to_send.header.receiver.index);
        printf("  CmdSet: 0x%02X, CmdID: 0x%02X\n", frame_to_send.header.cmd_set, frame_to_send.header.cmd_id);
        printf("  FCS Pitch: %.2f\n", (float)(frame_to_send.fcs_pitch)/100.0);
        // ... 打印其他数据字段 (记得转换字节序回来如果需要)
        printf("  CRC16: 0x%04X\n", (frame_to_send.crc16)); // 转回主机序打印 (假设原CRC函数返回BE)

        print_fuffer_in_Hex(&frame_to_send,frame_length);
        send_gimbal_message(&frame_to_send, frame_length);
        // actual_serial_send((uint8_t*)&frame_to_send, frame_length);
    } else {
        printf("V1 飞控心跳包 (修订版) 封装失败.\n");
    }
}
#endif


void send_fcs_reboot_example() {
    //char test[] = {0xAA, 0x09, 0x00, 0xB3, 0x8B, 0xE8, 0x03, 0x49, 0x3C};

    // unsigned char test[] = {
    // 0xaa, 0x2e, 0x04, 0xdc, 0x00, 0x00, 0x00, 0x10, 
    // 0x08, 0x01, 0x01, 0xf1, 0x00, 0x00, 0x00, 0x00, 
    // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    // 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    // 0x00, 0x00, 0x00, 0x00, 0x17, 0x97
    // };;
    // send_gimbal_message(test, 46);
    // printf("---------------send  reboot command to gimbal\n");
    // return;


//     unsigned char test[] = {
//     0xAA, 0x35, 0x04, 0xE3, 0x00, 0x00, 0x00, 0x10,
//         0x08, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//         0x00, 0x02, 0xB8, 0x0B, 0x00, 0x00, 0x00, 0x00,
//         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//         0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//         0x00, 0x00, 0x00, 0x3B, 0xE8
// };;
//     send_gimbal_message(test, 53);
//     printf("---------------send  reboot command to gimbal\n");
//     return;

    uint16_t seq_to_send = g_v1_send_seq++; // 获取并递增序列号
    gb_general_setup_struct current_fcs_data = {0};
    current_fcs_data.cmd = 0xf1;

        // 获取总帧长，这个长度将写入header.ver_len.len
    printf("###gb_general_setup_struct size: %d\n", sizeof(gb_general_setup_struct));
    uint16_t total_frame_length = sizeof(gb_general_setup_struct); // 应为 51 字节

    // --- 1. 填充帧头 (out_frame->header) ---
    current_fcs_data.header.sof = 0xAA;
    current_fcs_data.header.ver_len.ver = 1; // 协议版本固定为1
    current_fcs_data.header.ver_len.len = total_frame_length ;

    // 计算并填充 CHECKSUM (对SOF + VER_LEN的两个字节)
    // VER_LEN 是一个16位成员，包含两个位域。在小端机器上，它的内存表示是低字节在前。
    // 我们需要对SOF和这两个字节求和。
    // (uint8_t*)out_frame 指向SOF
    current_fcs_data.header.check_sum = calculate_header_checksum_v1((uint8_t*)&current_fcs_data);

    //current_fcs_data.header.seq = htole16(seq); // 确保序列号是小端
    current_fcs_data.header.seq = (seq_to_send); // 确保序列号是小端


    current_fcs_data.header.status.encrypt = 0; // 心跳包数据通常不加密
    current_fcs_data.header.status.reserved = 0;
    current_fcs_data.header.status.reserved1 = 0;

    // SENDER 和 RECEIVER (结构体赋值或memcpy)
    // 假设sender_id 和 receiver_id 的位域结构与header中的定义匹配
    //memcpy(&current_fcs_data.header.sender, &sender_id, sizeof(current_fcs_data.header.sender));
    //memcpy(&current_fcs_data.header.receiver, &receiver_id, sizeof(current_fcs_data.header.receiver));
    current_fcs_data.header.sender.device = 0x2;
    current_fcs_data.header.sender.index = 0x0;
    current_fcs_data.header.receiver.device = 0x1;
    current_fcs_data.header.receiver.index = 0x0;

    current_fcs_data.header.cmd_set = 1;      // 
    current_fcs_data.header.cmd_id = 1; // 


    // --- 3. 计算并填充帧尾的 CRC16 ---
    // CRC16 覆盖范围: 从SOF开始，到DATA的末尾 (即整个帧除去最后2字节的CRC16本身)
    // 长度 = total_frame_length - sizeof(out_frame->crc16)
    //       = sizeof(header_v1_t) + sizeof(fcs_heartbeat_data_fields_t)
    uint16_t crc_calculation_len = total_frame_length - 2; // 49 字节

    // 调用你项目中实现的V1协议CRC16函数
    uint16_t calculated_crc16_be = calc_crc16((uint8_t*)&current_fcs_data, crc_calculation_len);

    // calc_crc16_v1_protocol 返回的是 (high_byte << 8 | low_byte) 即大端
    // 我们需要将其以小端方式存入 out_frame->crc16
    // 如果 out_frame->crc16 是 uint16_t，并且当前系统是小端，我们需要存入字节交换后的值
    // 或者直接将字节放入正确位置
    // out_frame->crc16 = ((calculated_crc16_be & 0xFF00) >> 8) | // Low byte
    //                    ((calculated_crc16_be & 0x00FF) << 8);  // High byte
    // 更简单的方式（假设目标是小端存储）:
    //out_frame->crc16 = htole16(calculated_crc16_be);
    current_fcs_data.crc16 = (calculated_crc16_be);



    print_fuffer_in_Hex(&current_fcs_data, sizeof(gb_general_setup_struct));
    send_gimbal_message(&current_fcs_data, sizeof(gb_general_setup_struct));
}


void handle_gimbal_heartbeat(const char *data)
{
    payload_gb_heartbeat_v1_t hb = {0};
    //const header_v1_t *header = (const header_v1_t *)data;
    memcpy(&hb, data, sizeof(payload_gb_heartbeat_v1_t));
    set_gimbal_status(&hb);
#if 0
    //const payload_gb_heartbeat_v1_t *hb = (const payload_gb_heartbeat_v1_t *)data;
    printf("--- 收到V1云台心跳包 (SEQ: %u, FromDev:0x%02X, FromIdx:0x%01X) -ver : %d--\n",
           hb.header.seq, hb.header.sender.device, hb.header.sender.index, hb.header.ver_len.ver); // 假设seq是小端，直接打印

    // 打印姿态角 (单位已转换为度)
    printf("  云台姿态: Pitch=%.2f, Roll=%.2f, Yaw=%.2f (度)\n",
           (float)hb.gb_pitch / 100.0f,
           (float)hb.gb_roll / 100.0f,
           (float)hb.gb_yaw / 100.0f);

    // 打印状态 (解析位域)
    printf("  云台状态 (0x%08X):\n", hb.status); // 直接打印整个32位状态字
    printf("    姿态就绪: %s\n", hb.status.atti_ready ? "是" : "否");
    printf("    关节初始化完成: %s\n", hb.status.joint_init_ok ? "是" : "否");
    printf("    欠压: %s\n", hb.status.under_volt ? "是" : "否");
    printf("    IMU缺失: %s\n", hb.status.mems_lost ? "是" : "否");
    printf("    关节驱动: %u (0:正常, 1:堵转, 2:关闭)\n", hb.status.actuator);
    printf("    软件限位: %s\n", hb.status.soft_limit ? "是" : "否");
    printf("    控制模式: %u (0:力矩, 1:速度, 2:位置, 3:姿态)\n", hb.status.ctrl_mode);
    printf("    状态机: %u\n", hb.status.state_machine);

    // 打印关节角度
    printf("  关节角度: 外=%.2f, 中=%.2f, 内=%.2f (度)\n",
           (float)hb.joint_outter_angle / 100.0f,
           (float)hb.joint_middle_angle / 100.0f,
           (float)hb.joint_inner_angle / 100.0f);

    // 打印关节速度
    printf("  关节速度: 外=%.2f, 中=%.2f, 内=%.2f (度/s)\n",
           (float)hb.joint_outter_speed / 100.0f,
           (float)hb.joint_middle_speed / 100.0f,
           (float)hb.joint_inner_speed / 100.0f);

    // 打印IMU角速度
    printf("  IMU角速度: Wx=%.2f, Wy=%.2f, Wz=%.2f (度/s)\n",
           (float)hb.imu_w_x / 100.0f,
           (float)hb.imu_w_y / 100.0f,
           (float)hb.imu_w_z / 100.0f);

    printf("------------------------------------------------------------\n");
#endif
    // 在此根据心跳包数据执行你的应用逻辑
    // 例如，更新云台状态，检查是否有错误等
}




void handle_gimbal_log(const char *data)
{

}






void set_gimbal_speed(unsigned int pitch, unsigned int yaw)
{
    pitch_dial_speed = pitch;
    yaw_dial_speed = yaw;
}


void send_fcs_info_to_gimbal(int drone_desired_yaw, 
                            int drone_pitch, 
                            int drone_roll, 
                            int drone_yaw, 
                            int accel_x, 
                            int accel_y, 
                            int accel_z, 
                            int takeoff_status_fc_data_valid) 
{
    //g_fcs_data.fcs_desired_yaw = drone_desired_yaw;
    g_fcs_data.fcs_pitch = drone_pitch;
    g_fcs_data.fcs_roll = drone_roll;
    g_fcs_data.fcs_yaw = drone_yaw;
    g_fcs_data.fcs_accx = accel_x;
    g_fcs_data.fcs_accy = accel_y;
    g_fcs_data.fcs_accz = accel_z;
    g_fcs_data.fcs_state = takeoff_status_fc_data_valid;
    g_fcs_data.gb_ctrl_mode = 1;
    send_fcs_command(&g_fcs_data);

}



void set_gimbal_speed_factor(float current_zoom_factor)
{
    zoom_factor = current_zoom_factor/100.0;
}



/**
 * @brief 将MAVLink姿态消息转发到云台
 * 
 * @param attitude MAVLink姿态消息结构体指针
 */
void forward_attitude_to_gimbal(const mavlink_attitude_t *attitude)
{
    if (!attitude) {
        return;
    }
    
    // 将弧度转换为度，并乘以100以符合云台协议要求
    int16_t roll_deg_x100 = (int16_t)(attitude->roll * 180.0f / M_PI * 100.0f);
    int16_t pitch_deg_x100 = (int16_t)(attitude->pitch * 180.0f / M_PI * 100.0f);
    int16_t yaw_deg_x100 = (int16_t)(attitude->yaw * 180.0f / M_PI * 100.0f);
    
    // 角速度也需要从弧度/秒转换为度/秒，并乘以100
    int16_t roll_rate_deg_x100 = (int16_t)(attitude->rollspeed * 180.0f / M_PI * 100.0f);
    int16_t pitch_rate_deg_x100 = (int16_t)(attitude->pitchspeed * 180.0f / M_PI * 100.0f);
    int16_t yaw_rate_deg_x100 = (int16_t)(attitude->yawspeed * 180.0f / M_PI * 100.0f);
    
    // 调用send_fcs_info_to_gimbal函数将姿态信息发送到云台
    // 注意：这里假设send_fcs_info_to_gimbal函数的参数顺序是：
    // drone_desired_yaw, drone_pitch, drone_roll, drone_yaw, accel_x, accel_y, accel_z, takeoff_status_fc_data_valid
    
    // 由于MAVLink姿态消息中没有加速度信息，我们可以将加速度设为0
    // 或者从其他MAVLink消息中获取加速度信息
    int accel_x = 0;
    int accel_y = 0;
    int accel_z = 0;
    
    // 飞行状态设为有效(1)
    int takeoff_status_fc_data_valid = 1;
    
    // 调用发送函数
    send_fcs_info_to_gimbal(
        yaw_deg_x100,        // 期望偏航角 (当前偏航角作为期望值)
        pitch_deg_x100,      // 飞机俯仰角
        roll_deg_x100,       // 飞机横滚角
        yaw_deg_x100,        // 飞机偏航角
        accel_x,             // X轴加速度
        accel_y,             // Y轴加速度
        accel_z,             // Z轴加速度
        takeoff_status_fc_data_valid  // 飞行状态有效
    );
    
    printf("已将姿态信息转发到云台: Roll=%.2f°, Pitch=%.2f°, Yaw=%.2f°\n", 
           roll_deg_x100/100.0f, pitch_deg_x100/100.0f, yaw_deg_x100/100.0f);
    
    // 如果需要，还可以发送云台控制命令
    // 例如，可以根据姿态信息调整云台的稳定模式或跟踪模式
    // send_gimbal_control_command(...);
}




void handle_mavlink_hearbeat_message(mavlink_message_t *msg)
{

    // 解码心跳消息
    mavlink_heartbeat_t heartbeat;
    mavlink_msg_heartbeat_decode(msg, &heartbeat);
    
    LOG_I("收到MAVLink心跳消息: 系统类型=%d, 自动驾驶仪=%d, 系统模式=%d 系统状态=%d\n", 
            heartbeat.type, heartbeat.autopilot, heartbeat.base_mode, heartbeat.system_status);

    if(heartbeat.system_status == MAV_STATE_ACTIVE)
    {
        g_fcs_data.fcs_state |= 1;
    }
    else if (heartbeat.system_status == MAV_STATE_STANDBY)
    {
        g_fcs_data.fcs_state &= ~1;
    }
    send_fcs_command(&g_fcs_data);
}



void handle_mavlink_attitude_message(mavlink_message_t *msg)
{
    // 解码姿态消息
    g_fcs_data.fcs_state |= (1 << 7);  // 设置bit7为1
    g_fcs_data.fcs_state |= (1 << 6); // 设置bit6为1
    g_fcs_data.fcs_state |= (1 << 5);  // 设置bit5为1
    mavlink_attitude_t attitude;
    mavlink_msg_attitude_decode(msg, &attitude);
    
    LOG_I("收到MAVLink姿态消息: 横滚=%.2f, 俯仰=%.2f, 偏航=%.2f\n", 
            attitude.roll, attitude.pitch, attitude.yaw);

    // // 将弧度转换为度以便于阅读
    // float roll_deg = attitude.roll * 180.0f / M_PI;
    // float pitch_deg = attitude.pitch * 180.0f / M_PI;
    // float yaw_deg = attitude.yaw * 180.0f / M_PI;
    
    // printf("收到MAVLink姿态消息:\n");
    // printf("  时间戳: %u ms\n", attitude.time_boot_ms);
    // printf("  横滚(Roll): %.2f° (%.4f rad)\n", roll_deg, attitude.roll);
    // printf("  俯仰(Pitch): %.2f° (%.4f rad)\n", pitch_deg, attitude.pitch);
    // printf("  偏航(Yaw): %.2f° (%.4f rad)\n", yaw_deg, attitude.yaw);
    // printf("  角速度: Roll=%.2f rad/s, Pitch=%.2f rad/s, Yaw=%.2f rad/s\n", 
    //         attitude.rollspeed, attitude.pitchspeed, attitude.yawspeed);
    // 调用转发函数
    //forward_attitude_to_gimbal(&attitude);
    // g_fcs_data.fcs_yaw = attitude.yaw * 100;
    // g_fcs_data.fcs_roll = attitude.roll * 100;
    // g_fcs_data.fcs_pitch = attitude.pitch * 100;
    g_fcs_data.fcs_roll = (int16_t)(attitude.roll * 180.0f / M_PI * 100.0f);
    g_fcs_data.fcs_pitch = (int16_t)(attitude.pitch * 180.0f / M_PI * 100.0f);
    g_fcs_data.fcs_yaw = (int16_t)(attitude.yaw * 180.0f / M_PI * 100.0f);


    send_fcs_command(&g_fcs_data);

}


void handle_mavlink_attitude_target_message(mavlink_message_t *msg)
{
    // 解码姿态目标消息
    g_fcs_data.fcs_state |= (1 << 7);  // 设置bit7为1
    g_fcs_data.fcs_state |= (1 << 6); // 设置bit6为1
    g_fcs_data.fcs_state |= (1 << 5);  // 设置bit5为1
    mavlink_attitude_target_t attitude_target;
    mavlink_msg_attitude_target_decode(msg, &attitude_target);
    
    LOG_I("收到MAVLink姿态目标消息: 横滚角速度=%.2f, 俯仰角速度=%.2f, 偏航角速度=%.2f\n", 
            attitude_target.body_roll_rate, attitude_target.body_pitch_rate, attitude_target.body_yaw_rate);


    // printf("收到MAVLink姿态目标消息:\n");
    // printf("  时间戳: %u ms\n", attitude_target.time_boot_ms);
    // printf("  类型掩码: 0x%X\n", attitude_target.type_mask);
    // printf("  四元数(w, x, y, z): %.4f, %.4f, %.4f, %.4f\n", 
    //         attitude_target.q[0], attitude_target.q[1], attitude_target.q[2], attitude_target.q[3]);
    // printf("  角速度(rad/s): Roll=%.2f, Pitch=%.2f, Yaw=%.2f\n", 
    //         attitude_target.body_roll_rate, attitude_target.body_pitch_rate, attitude_target.body_yaw_rate);
    // printf("  集体推力: %.2f\n", attitude_target.thrust);
    // // 调用转发函数
    // forward_attitude_target_to_gimbal(&attitude_target);
    //g_fcs_data.fcs_yaw_w_ref = attitude_target.body_yaw_rate * 100;
    g_fcs_data.fcs_yaw_w_ref =(int16_t)(attitude_target.body_yaw_rate * 180.0f / M_PI * 100.0f);
    send_fcs_command(&g_fcs_data);
}

void handle_mavlink_scaled_imu_message(mavlink_message_t *msg)
{
    // 解码imu消息
    mavlink_scaled_imu_t imu;
    mavlink_msg_scaled_imu_decode(msg, &imu);
    
    LOG_I("收到MAVLink imu消息: 加速度X=%.2f, 加速度Y=%.2f, 加速度Z=%.2f\n", 
            imu.xacc, imu.yacc, imu.zacc);
    
    // 调用转发函数
    //forward_imu_to_gimbal(&imu);
    send_fcs_command(&g_fcs_data);
}

void handle_mavlink_postion_target_message(mavlink_message_t *msg)
{
    // 解码位置目标消息
    mavlink_position_target_local_ned_t pos_target;
    mavlink_msg_position_target_local_ned_decode(msg, &pos_target);
    
    LOG_I("收到MAVLink位置目标消息: 位置X=%.2f, 位置Y=%.2f, 位置Z=%.2f\n", 
            pos_target.x, pos_target.y, pos_target.z);
    
    //g_fcs_data.fcs_yaw_ref = pos_target.yaw * 100;
    g_fcs_data.fcs_yaw_ref = (int16_t)(pos_target.yaw * 180.0f / M_PI * 100.0f);
    //mavlink_position_target_local_ned_t pos_target;
    //mavlink_msg_position_target_local_ned_decode(msg, &pos_target);
    
    // // 创建一个字符串来表示坐标系
    // const char* coordinate_frame;
    // switch (pos_target.coordinate_frame) {
    //     case MAV_FRAME_LOCAL_NED:
    //         coordinate_frame = "LOCAL_NED";
    //         break;
    //     case MAV_FRAME_LOCAL_OFFSET_NED:
    //         coordinate_frame = "LOCAL_OFFSET_NED";
    //         break;
    //     case MAV_FRAME_BODY_NED:
    //         coordinate_frame = "BODY_NED";
    //         break;
    //     case MAV_FRAME_BODY_OFFSET_NED:
    //         coordinate_frame = "BODY_OFFSET_NED";
    //         break;
    //     default:
    //         coordinate_frame = "未知";
    //         break;
    // }
    
    // // 创建一个字符串来表示类型掩码
    // char type_mask_str[256] = {0};
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_X_IGNORE)
    //     strcat(type_mask_str, "X_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_Y_IGNORE)
    //     strcat(type_mask_str, "Y_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_Z_IGNORE)
    //     strcat(type_mask_str, "Z_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_VX_IGNORE)
    //     strcat(type_mask_str, "VX_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_VY_IGNORE)
    //     strcat(type_mask_str, "VY_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_VZ_IGNORE)
    //     strcat(type_mask_str, "VZ_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_AX_IGNORE)
    //     strcat(type_mask_str, "AX_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_AY_IGNORE)
    //     strcat(type_mask_str, "AY_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_AZ_IGNORE)
    //     strcat(type_mask_str, "AZ_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_FORCE_SET)
    //     strcat(type_mask_str, "FORCE_SET ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_YAW_IGNORE)
    //     strcat(type_mask_str, "YAW_IGNORE ");
    // if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_YAW_RATE_IGNORE)
    //     strcat(type_mask_str, "YAW_RATE_IGNORE ");
    
    // printf("收到MAVLink位置目标消息(LOCAL_NED):\n");
    // printf("  时间戳: %u ms\n", pos_target.time_boot_ms);
    // printf("  坐标系: %s\n", coordinate_frame);
    // printf("  类型掩码: 0x%X (%s)\n", pos_target.type_mask, type_mask_str);
    // printf("  位置(m): X=%.2f, Y=%.2f, Z=%.2f\n", 
    //         pos_target.x, pos_target.y, pos_target.z);
    // printf("  速度(m/s): VX=%.2f, VY=%.2f, VZ=%.2f\n", 
    //         pos_target.vx, pos_target.vy, pos_target.vz);
    // printf("  加速度(m/s²): AX=%.2f, AY=%.2f, AZ=%.2f\n", 
    //         pos_target.afx, pos_target.afy, pos_target.afz);
    // printf("  偏航(rad): %.2f, 偏航速率(rad/s): %.2f\n", 
    //         pos_target.yaw, pos_target.yaw_rate);

    // // 调用转发函数
    // forward_position_target_to_gimbal(&pos_target);

    send_fcs_command(&g_fcs_data);
}





void start_gimbal()
{
    //start serial message proc
    start_gimbal_message_proc("/dev/ttyAMA5");

    REGISTER_GIMBAL_MESSAGE_CALLBACK(0x1, E_GIMBAL_HEARTBEAT, handle_gimbal_heartbeat);
    REGISTER_GIMBAL_MESSAGE_CALLBACK(0x1, E_GIMBAL_LOG, handle_gimbal_log);


    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_HEARTBEAT, handle_mavlink_hearbeat_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_ATTITUDE, handle_mavlink_attitude_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_ATTITUDE_TARGET, handle_mavlink_attitude_target_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_SCALED_IMU, handle_mavlink_scaled_imu_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_POSITION_TARGET_LOCAL_NED, handle_mavlink_postion_target_message);
}
