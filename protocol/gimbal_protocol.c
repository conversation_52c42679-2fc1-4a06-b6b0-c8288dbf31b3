#include "gimbal_protocol.h"
#include "gimbal_message.h"
#include <stdint.h>
#include <stddef.h>
#include "mavlink.h"
#include "dronepod_k40t_message.h"
#include "report_message.h"
#define LOG_LEVEL LOG_LEVEL_ERROR
#include "log_utils.h"
#include "dronepod_status.h"
#include "heartbeat_queue.h"

static volatile int exit_flag = 0;
static uint16_t g_v1_send_seq = 0;
static unsigned int pitch_dial_speed = 155;
static unsigned int yaw_dial_speed = 155;
static float zoom_factor = 1;


// 和校验函数
static uint8_t calculate_checksum_v1(const header_v1_t *header) {
    uint8_t sum = 0;
    sum += header->sof;
    sum += (uint8_t)(header->ver_len.len & 0xFF);          // VER_LEN 的低字节
    sum += (uint8_t)((header->ver_len.len >> 8) & 0x03) |  // VER_LEN 的高字节 (len部分)
           (uint8_t)((header->ver_len.ver << 2) & 0xFC);   // VER_LEN 的高字节 (ver部分)
    return sum;
}


// 和校验函数 (用于帧头前3字节: SOF + 2字节的VER_LEN)
static uint8_t calculate_header_checksum_v1(const uint8_t* header_start) {
    // header_start 指向 SOF
    // header_start[0] 是 SOF
    // header_start[1] 是 VER_LEN 的第一个字节 (小端时是低字节)
    // header_start[2] 是 VER_LEN 的第二个字节 (小端时是高字节)
    return header_start[0] + header_start[1] + header_start[2];
}

/**
 * @brief 封装V1协议的飞控心跳包 (使用用户提供的完整帧结构体)
 *
 * @param fcs_payload_data  指向包含飞控心跳特定数据字段的结构体指针
 * @param seq               本条消息的序列号
 * @param sender_id         发送方ID (例如 SENDER_ID_FC_V1)
 * @param receiver_id       接收方ID (例如 RECEIVER_ID_GIMBAL_V1)
 * @param out_frame         指向待填充的完整帧结构体 (packet_fcs_heartbeat_v1_t) 的指针
 * @return int              成功则返回总帧长 (sizeof(packet_fcs_heartbeat_v1_t))，失败则返回0或负数
 */
int package_fcs_heartbeat_v1_complete(
    const fcs_heartbeat_data_fields_t *fcs_payload_data,
    uint16_t seq,
    uint8_t sender_id,
    uint8_t receiver_id,
    packet_fcs_heartbeat_v1_t *out_frame)
{
    if (fcs_payload_data == NULL || out_frame == NULL) {
        printf("错误: 输入参数为空指针\n");
        return -1;
    }

    // 获取总帧长，这个长度将写入header.ver_len.len
    uint16_t total_frame_length = sizeof(packet_fcs_heartbeat_v1_t); // 应为 51 字节

    // --- 1. 填充帧头 (out_frame->header) ---
    out_frame->header.sof = 0xAA;
    out_frame->header.ver_len.ver = 1; // 协议版本固定为1
    out_frame->header.ver_len.len = total_frame_length ;

    // 计算并填充 CHECKSUM (对SOF + VER_LEN的两个字节)
    // VER_LEN 是一个16位成员，包含两个位域。在小端机器上，它的内存表示是低字节在前。
    // 我们需要对SOF和这两个字节求和。
    // (uint8_t*)out_frame 指向SOF
    out_frame->header.check_sum = calculate_header_checksum_v1((uint8_t*)out_frame);

    //out_frame->header.seq = htole16(seq); // 确保序列号是小端
    out_frame->header.seq = (seq); // 确保序列号是小端


    out_frame->header.status.encrypt = 0; // 心跳包数据通常不加密
    out_frame->header.status.reserved = 0;
    out_frame->header.status.reserved1 = 0;

    // SENDER 和 RECEIVER (结构体赋值或memcpy)
    // 假设sender_id 和 receiver_id 的位域结构与header中的定义匹配
    //memcpy(&out_frame->header.sender, &sender_id, sizeof(out_frame->header.sender));
    //memcpy(&out_frame->header.receiver, &receiver_id, sizeof(out_frame->header.receiver));
    out_frame->header.sender.device = 0x2;
    out_frame->header.sender.index = 0x0;
    out_frame->header.receiver.device = 0x1;
    out_frame->header.receiver.index = 0x0;

    out_frame->header.cmd_set = CMD_SET_FCS_V1;      // 飞控指令集 (0x02)
    out_frame->header.cmd_id = CMD_ID_FCS_HEARTBEAT_V1; // 心跳包指令ID (0x00)

    // --- 2. 填充飞控心跳的特定数据字段 ---
    // 使用 memcpy 将 fcs_payload_data 拷贝到 out_frame 中对应的数据字段位置
    // 这些字段紧跟在 header 之后
    memcpy((uint8_t*)out_frame + sizeof(header_v1_t),
           fcs_payload_data,
           sizeof(fcs_heartbeat_data_fields_t));

    uint16_t crc_calculation_len = total_frame_length - sizeof(out_frame->crc16); // 49 字节

    // 调用你项目中实现的V1协议CRC16函数
    uint16_t calculated_crc16_be = calc_crc16((uint8_t*)out_frame, crc_calculation_len);

    // calc_crc16_v1_protocol 返回的是 (high_byte << 8 | low_byte) 即大端
    // 我们需要将其以小端方式存入 out_frame->crc16
    // 如果 out_frame->crc16 是 uint16_t，并且当前系统是小端，我们需要存入字节交换后的值
    // 或者直接将字节放入正确位置
    // out_frame->crc16 = ((calculated_crc16_be & 0xFF00) >> 8) | // Low byte
    //                    ((calculated_crc16_be & 0x00FF) << 8);  // High byte
    // 更简单的方式（假设目标是小端存储）:
    //out_frame->crc16 = htole16(calculated_crc16_be);
    out_frame->crc16 = (calculated_crc16_be);


    return total_frame_length;
}



// 全局变量，保持云台状态上下文
static fcs_heartbeat_data_fields_t g_fcs_data = {0};

// 通用的发送函数，接收控制参数
static void send_fcs_command(const fcs_heartbeat_data_fields_t *cmd_data) 
{
    packet_fcs_heartbeat_v1_t frame_to_send;
    uint16_t seq_to_send = g_v1_send_seq++; // 获取并递增序列号

    int frame_length = package_fcs_heartbeat_v1_complete(
        cmd_data,
        seq_to_send,
        SENDER_ID_FC,        // 飞控作为发送者
        RECEIVER_ID_GIMBAL,  // 云台作为接收者
        &frame_to_send
    );

    if (frame_length > 0) {
        send_gimbal_message(&frame_to_send, frame_length);
    } else {
        printf("V1 飞控心跳包封装失败.\n");
    }
}

// 初始化默认的心跳数据
static void init_default_fcs_data(fcs_heartbeat_data_fields_t *data)
{
    memset(data, 0, sizeof(fcs_heartbeat_data_fields_t));
    data->fcs_state = 0;
}

// 更新全局状态
static void update_gimbal_state(int yaw, int pitch, uint8_t ctrl_mode)
{
    // 保留其他字段的值，只更新需要的字段
    g_fcs_data.gb_ctrl_mode = ctrl_mode;
    g_fcs_data.gb_pitch_ref = pitch * 100;
    g_fcs_data.gb_yaw_ref = yaw * 100;
}


int calc_yaw_dial_speed(int dial_speed, float zoom_factor)
{
    // 参数保护
    if (dial_speed < 1)  dial_speed = 1;
    if (dial_speed > 30) dial_speed = 30;
    if (zoom_factor < 1.0f) zoom_factor = 1.0f;
    if (zoom_factor > 15.0f) zoom_factor = 15.0f;

    // 原速为 1 就不再减小
    if (dial_speed == 1) return 1;

    float real;
    if (zoom_factor <= 10.0f)
    {
        // 1.0 ~ 10.0：线性从 dial_speed 降到 2
        real = dial_speed - (dial_speed - 2.0f) * (zoom_factor - 1.0f) / 9.0f;
    }
    else
    {
        // 10.0 ~ 15.0：线性从 2 降到 1
        real = 2.0f - 1.0f * (zoom_factor - 10.0f) / 5.0f;
    }

    // 结果不能小于 1
    if (real < 1.0f) real = 1.0f;

    return (int)real;   // 直接截断取整
}

// 返回中心位置
void send_fcs_return_center() 
{
    fcs_heartbeat_data_fields_t fcs_hb = {0};
    memcpy(&fcs_hb, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    fcs_hb.gb_yaw_ref = 0;
    fcs_hb.gb_pitch_ref = 0; // 特定值，保持原代码逻辑
    fcs_hb.gb_ctrl_mode = 3;
    send_fcs_command(&fcs_hb);
}

// 立即向下看
void send_fcs_look_down_at_once() 
{
    fcs_heartbeat_data_fields_t fcs_hb = {0};
    memcpy(&fcs_hb, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    fcs_hb.gb_ctrl_mode = 2;
    g_fcs_data.gb_pitch_ref = -(9000); // 特定值，保持原代码逻辑
    send_fcs_command(&fcs_hb);
}

// 向左看
void send_fcs_look_left() 
{
    dronepod_status_t * status = get_dronepod_status();
    int speed = status->yaw_dial_speed;
    int final_speed = calc_yaw_dial_speed(speed,zoom_factor) * 100;
    fcs_heartbeat_data_fields_t fcs_hb = {0};
    memcpy(&fcs_hb, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    fcs_hb.gb_ctrl_mode = 1;
    //fcs_hb.gb_yaw_ref = -3000 + (int)((zoom_factor - 1.0) * 200);//g_fcs_data.gb_yaw_ref - (yaw_dial_speed - (int)(zoom_factor * 10)); // 减少1度
    fcs_hb.gb_yaw_ref = -(final_speed);//g_fcs_data.gb_yaw_ref - (yaw_dial_speed - (int)(zoom_factor * 10)); // 减少1度
    fcs_hb.gb_pitch_ref = 0;
    send_fcs_command(&fcs_hb);
}

// 向右看
void send_fcs_look_right() 
{
    dronepod_status_t * status = get_dronepod_status();
    int speed = status->yaw_dial_speed;
    int final_speed = calc_yaw_dial_speed(speed,zoom_factor) * 100;

    fcs_heartbeat_data_fields_t fcs_hb = {0};
    memcpy(&fcs_hb, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    fcs_hb.gb_ctrl_mode = 1;
//    fcs_hb.gb_yaw_ref = 3000 - (int)((zoom_factor - 1.0) * 200);//g_fcs_data.gb_yaw_ref + (yaw_dial_speed - (int)(zoom_factor * 10)); // 增加1度
    fcs_hb.gb_yaw_ref = final_speed;//g_fcs_data.gb_yaw_ref + (yaw_dial_speed - (int)(zoom_factor * 10)); // 增加1度
    fcs_hb.gb_pitch_ref = 0;
    send_fcs_command(&fcs_hb);
}

// 向上看
void send_fcs_look_up() 
{
    dronepod_status_t * status = get_dronepod_status();
    int speed = status->pitch_dial_speed;
    int final_speed = calc_yaw_dial_speed(speed,zoom_factor) * 100;
    fcs_heartbeat_data_fields_t fcs_hb = {0};
    memcpy(&fcs_hb, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    fcs_hb.gb_ctrl_mode = 1;
    fcs_hb.gb_yaw_ref = 0;
//    fcs_hb.gb_pitch_ref = 3000 - (int)((zoom_factor - 1.0) * 200);//g_fcs_data.gb_pitch_ref + (pitch_dial_speed - (int)(zoom_factor * 10)); // 增加1度
    fcs_hb.gb_pitch_ref = final_speed;//g_fcs_data.gb_pitch_ref + (pitch_dial_speed - (int)(zoom_factor * 10)); // 增加1度
    send_fcs_command(&fcs_hb);

}

// 向下看
void send_fcs_look_down() 
{
    dronepod_status_t * status = get_dronepod_status();
    int speed = status->pitch_dial_speed;
    int final_speed = calc_yaw_dial_speed(speed,zoom_factor) * 100;

    fcs_heartbeat_data_fields_t fcs_hb = {0};
    memcpy(&fcs_hb, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    fcs_hb.gb_ctrl_mode = 1;
    fcs_hb.gb_yaw_ref = 0;
//    fcs_hb.gb_pitch_ref = -3000 + (int)((zoom_factor - 1.0) * 200);//g_fcs_data.gb_pitch_ref - (pitch_dial_speed - (int)(zoom_factor * 10)); // 减少1度
    fcs_hb.gb_pitch_ref = -(final_speed);//g_fcs_data.gb_pitch_ref - (pitch_dial_speed - (int)(zoom_factor * 10)); // 减少1度
    send_fcs_command(&fcs_hb);
}

// 指定角度运动
void send_fcs_to_special_angle(int yaw_ref, int pitch_ref) 
{
    fcs_heartbeat_data_fields_t fcs_hb = {0};
    memcpy(&fcs_hb, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    fcs_hb.gb_yaw_ref = yaw_ref * 100;
    fcs_hb.gb_pitch_ref = pitch_ref * 100;//g_fcs_data.gb_pitch_ref - (pitch_dial_speed - (int)(zoom_factor * 10)); // 减少1度
    fcs_hb.gb_ctrl_mode = 2;
    send_fcs_command(&fcs_hb);
}




void send_fcs_reboot_example() 
{

    uint16_t seq_to_send = g_v1_send_seq++; // 获取并递增序列号
    gb_general_setup_struct current_fcs_data = {0};
    current_fcs_data.cmd = 0xf1;

        // 获取总帧长，这个长度将写入header.ver_len.len
    printf("###gb_general_setup_struct size: %d\n", sizeof(gb_general_setup_struct));
    uint16_t total_frame_length = sizeof(gb_general_setup_struct); // 应为 51 字节

    // --- 1. 填充帧头 (out_frame->header) ---
    current_fcs_data.header.sof = 0xAA;
    current_fcs_data.header.ver_len.ver = 1; // 协议版本固定为1
    current_fcs_data.header.ver_len.len = total_frame_length ;

    // 计算并填充 CHECKSUM (对SOF + VER_LEN的两个字节)
    // VER_LEN 是一个16位成员，包含两个位域。在小端机器上，它的内存表示是低字节在前。
    // 我们需要对SOF和这两个字节求和。
    // (uint8_t*)out_frame 指向SOF
    current_fcs_data.header.check_sum = calculate_header_checksum_v1((uint8_t*)&current_fcs_data);

    //current_fcs_data.header.seq = htole16(seq); // 确保序列号是小端
    current_fcs_data.header.seq = (seq_to_send); // 确保序列号是小端


    current_fcs_data.header.status.encrypt = 0; // 心跳包数据通常不加密
    current_fcs_data.header.status.reserved = 0;
    current_fcs_data.header.status.reserved1 = 0;

    // SENDER 和 RECEIVER (结构体赋值或memcpy)
    // 假设sender_id 和 receiver_id 的位域结构与header中的定义匹配
    //memcpy(&current_fcs_data.header.sender, &sender_id, sizeof(current_fcs_data.header.sender));
    //memcpy(&current_fcs_data.header.receiver, &receiver_id, sizeof(current_fcs_data.header.receiver));
    current_fcs_data.header.sender.device = 0x2;
    current_fcs_data.header.sender.index = 0x0;
    current_fcs_data.header.receiver.device = 0x1;
    current_fcs_data.header.receiver.index = 0x0;

    current_fcs_data.header.cmd_set = 1;      // 
    current_fcs_data.header.cmd_id = 1; // 


    // --- 3. 计算并填充帧尾的 CRC16 ---
    // CRC16 覆盖范围: 从SOF开始，到DATA的末尾 (即整个帧除去最后2字节的CRC16本身)
    // 长度 = total_frame_length - sizeof(out_frame->crc16)
    //       = sizeof(header_v1_t) + sizeof(fcs_heartbeat_data_fields_t)
    uint16_t crc_calculation_len = total_frame_length - 2; // 49 字节

    // 调用你项目中实现的V1协议CRC16函数
    uint16_t calculated_crc16_be = calc_crc16((uint8_t*)&current_fcs_data, crc_calculation_len);

    // calc_crc16_v1_protocol 返回的是 (high_byte << 8 | low_byte) 即大端
    // 我们需要将其以小端方式存入 out_frame->crc16
    // 如果 out_frame->crc16 是 uint16_t，并且当前系统是小端，我们需要存入字节交换后的值
    // 或者直接将字节放入正确位置
    // out_frame->crc16 = ((calculated_crc16_be & 0xFF00) >> 8) | // Low byte
    //                    ((calculated_crc16_be & 0x00FF) << 8);  // High byte
    // 更简单的方式（假设目标是小端存储）:
    //out_frame->crc16 = htole16(calculated_crc16_be);
    current_fcs_data.crc16 = (calculated_crc16_be);



    print_fuffer_in_Hex(&current_fcs_data, sizeof(gb_general_setup_struct));
    send_gimbal_message(&current_fcs_data, sizeof(gb_general_setup_struct));
}


void handle_gimbal_heartbeat(const char *data)
{
    payload_gb_heartbeat_v1_t hb = {0};
    //const header_v1_t *header = (const header_v1_t *)data;
    memcpy(&hb, data, sizeof(payload_gb_heartbeat_v1_t));
    set_gimbal_status(&hb);
#if 0
    //const payload_gb_heartbeat_v1_t *hb = (const payload_gb_heartbeat_v1_t *)data;
    printf("--- 收到V1云台心跳包 (SEQ: %u, FromDev:0x%02X, FromIdx:0x%01X) -ver : %d--\n",
           hb.header.seq, hb.header.sender.device, hb.header.sender.index, hb.header.ver_len.ver); // 假设seq是小端，直接打印

    // 打印姿态角 (单位已转换为度)
    printf("  云台姿态: Pitch=%.2f, Roll=%.2f, Yaw=%.2f (度)\n",
           (float)hb.gb_pitch / 100.0f,
           (float)hb.gb_roll / 100.0f,
           (float)hb.gb_yaw / 100.0f);

    // 打印状态 (解析位域)
    printf("  云台状态 (0x%08X):\n", hb.status); // 直接打印整个32位状态字
    printf("    姿态就绪: %s\n", hb.status.atti_ready ? "是" : "否");
    printf("    关节初始化完成: %s\n", hb.status.joint_init_ok ? "是" : "否");
    printf("    欠压: %s\n", hb.status.under_volt ? "是" : "否");
    printf("    IMU缺失: %s\n", hb.status.mems_lost ? "是" : "否");
    printf("    关节驱动: %u (0:正常, 1:堵转, 2:关闭)\n", hb.status.actuator);
    printf("    软件限位: %s\n", hb.status.soft_limit ? "是" : "否");
    printf("    控制模式: %u (0:力矩, 1:速度, 2:位置, 3:姿态)\n", hb.status.ctrl_mode);
    printf("    状态机: %u\n", hb.status.state_machine);

    // 打印关节角度
    printf("  关节角度: 外=%.2f, 中=%.2f, 内=%.2f (度)\n",
           (float)hb.joint_outter_angle / 100.0f,
           (float)hb.joint_middle_angle / 100.0f,
           (float)hb.joint_inner_angle / 100.0f);

    // 打印关节速度
    printf("  关节速度: 外=%.2f, 中=%.2f, 内=%.2f (度/s)\n",
           (float)hb.joint_outter_speed / 100.0f,
           (float)hb.joint_middle_speed / 100.0f,
           (float)hb.joint_inner_speed / 100.0f);

    // 打印IMU角速度
    printf("  IMU角速度: Wx=%.2f, Wy=%.2f, Wz=%.2f (度/s)\n",
           (float)hb.imu_w_x / 100.0f,
           (float)hb.imu_w_y / 100.0f,
           (float)hb.imu_w_z / 100.0f);

    printf("------------------------------------------------------------\n");
#endif
    // 在此根据心跳包数据执行你的应用逻辑
    // 例如，更新云台状态，检查是否有错误等
}




void handle_gimbal_log(const char *data)
{

}



void set_gimbal_speed(unsigned int pitch, unsigned int yaw)
{
    pitch_dial_speed = pitch;
    yaw_dial_speed = yaw;
}


// void send_fcs_info_to_gimbal(int drone_desired_yaw, 
//                             int drone_pitch, 
//                             int drone_roll, 
//                             int drone_yaw, 
//                             int accel_x, 
//                             int accel_y, 
//                             int accel_z, 
//                             int takeoff_status_fc_data_valid) 
// {
//     //g_fcs_data.fcs_desired_yaw = drone_desired_yaw;
//     g_fcs_data.fcs_pitch = drone_pitch;
//     g_fcs_data.fcs_roll = drone_roll;
//     g_fcs_data.fcs_yaw = drone_yaw;
//     g_fcs_data.fcs_accx = accel_x;
//     g_fcs_data.fcs_accy = accel_y;
//     g_fcs_data.fcs_accz = accel_z;
//     g_fcs_data.fcs_state = takeoff_status_fc_data_valid;
//     g_fcs_data.gb_ctrl_mode = 1;
//     send_fcs_command(&g_fcs_data);

// }



void set_gimbal_speed_factor(float current_zoom_factor)
{
    zoom_factor = current_zoom_factor/100.0;
    //printf("-----zoom_factor------- : %f \n", zoom_factor);
}




void handle_mavlink_hearbeat_message(mavlink_message_t *msg)
{

    // 解码心跳消息
    mavlink_heartbeat_t heartbeat;
    mavlink_msg_heartbeat_decode(msg, &heartbeat);
    
    LOG_I("收到MAVLink心跳消息: 系统类型=%d, 自动驾驶仪=%d, 系统模式=%d 系统状态=%d\n", 
            heartbeat.type, heartbeat.autopilot, heartbeat.base_mode, heartbeat.system_status);

    if(heartbeat.system_status == MAV_STATE_ACTIVE)
    {
        g_fcs_data.fcs_state |= 1;
    }
    else if (heartbeat.system_status == MAV_STATE_STANDBY)
    {
        g_fcs_data.fcs_state &= ~1;
    }

    // fcs_heartbeat_data_fields_t tmp = {0};
    // memcpy(&tmp, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    // tmp.gb_ctrl_mode = 0;
    // global_heartbeat_queue_push(&tmp);
    send_fcs_command(&g_fcs_data);
}



void handle_mavlink_attitude_message(mavlink_message_t *msg)
{
    // 解码姿态消息
    g_fcs_data.fcs_state |= (1 << 7);  // 设置bit7为1
    g_fcs_data.fcs_state |= (1 << 6); // 设置bit6为1
    g_fcs_data.fcs_state |= (1 << 5);  // 设置bit5为1
    mavlink_attitude_t attitude;
    mavlink_msg_attitude_decode(msg, &attitude);
    
    LOG_I("收到MAVLink姿态消息: 横滚=%.2f, 俯仰=%.2f, 偏航=%.2f\n", 
            attitude.roll, attitude.pitch, attitude.yaw);

    g_fcs_data.fcs_pitch = (int16_t)(attitude.pitch * 180.0f / M_PI * 100.0f);
    g_fcs_data.fcs_roll = (int16_t)(attitude.roll * 180.0f / M_PI * 100.0f);
    g_fcs_data.fcs_yaw = (int16_t)(attitude.yaw * 180.0f / M_PI * 100.0f);


    //fcs_heartbeat_data_fields_t tmp = {0};
    //memcpy(&tmp, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    //tmp.gb_ctrl_mode = 0;
    //global_heartbeat_queue_push(&tmp);
    send_fcs_command(&g_fcs_data);

}


void handle_mavlink_attitude_target_message(mavlink_message_t *msg)
{
    // 解码姿态目标消息
    g_fcs_data.fcs_state |= (1 << 7);  // 设置bit7为1
    g_fcs_data.fcs_state |= (1 << 6); // 设置bit6为1
    g_fcs_data.fcs_state |= (1 << 5);  // 设置bit5为1
    mavlink_attitude_target_t attitude_target;
    mavlink_msg_attitude_target_decode(msg, &attitude_target);
    
    LOG_I("收到MAVLink姿态目标消息: 横滚角速度=%.2f, 俯仰角速度=%.2f, 偏航角速度=%.2f\n", 
            attitude_target.body_roll_rate, attitude_target.body_pitch_rate, attitude_target.body_yaw_rate);

    g_fcs_data.fcs_yaw_w_ref =(int16_t)(attitude_target.body_yaw_rate * 180.0f / M_PI * 100.0f);

    //fcs_heartbeat_data_fields_t tmp = {0};
    //memcpy(&tmp, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    //tmp.gb_ctrl_mode = 0;
    //global_heartbeat_queue_push(&tmp);
    send_fcs_command(&g_fcs_data);

}

void handle_mavlink_scaled_imu_message(mavlink_message_t *msg)
{
    // 解码imu消息
    mavlink_scaled_imu_t imu;
    mavlink_msg_scaled_imu_decode(msg, &imu);
    
    LOG_I("收到MAVLink imu消息: 加速度X=%.2f, 加速度Y=%.2f, 加速度Z=%.2f\n", 
            imu.xacc, imu.yacc, imu.zacc);
    
    // 调用转发函数
    //forward_imu_to_gimbal(&imu);

    //fcs_heartbeat_data_fields_t tmp = {0};
    //memcpy(&tmp, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    //tmp.gb_ctrl_mode = 0;
    //global_heartbeat_queue_push(&tmp);
    send_fcs_command(&g_fcs_data);
}

void handle_mavlink_postion_target_message(mavlink_message_t *msg)
{
    // 解码位置目标消息
    mavlink_position_target_local_ned_t pos_target;
    mavlink_msg_position_target_local_ned_decode(msg, &pos_target);
    
    LOG_I("收到MAVLink位置目标消息: 位置X=%.2f, 位置Y=%.2f, 位置Z=%.2f\n", 
            pos_target.x, pos_target.y, pos_target.z);
    
    //g_fcs_data.fcs_yaw_ref = pos_target.yaw * 100;
    g_fcs_data.fcs_yaw_ref = (int16_t)(pos_target.yaw * 180.0f / M_PI * 100.0f);

    //fcs_heartbeat_data_fields_t tmp = {0};
    //memcpy(&tmp, &g_fcs_data, sizeof(fcs_heartbeat_data_fields_t));
    //tmp.gb_ctrl_mode = 0;
    //global_heartbeat_queue_push(&tmp);
    send_fcs_command(&g_fcs_data);
}

void handle_mavlink_gps_message(mavlink_message_t *msg)
{
    mavlink_global_position_int_t pos;
    mavlink_msg_global_position_int_decode(msg, &pos);
    
    LOG_I("收到MAVLink位置消息: 纬度=%d, 经度=%d, 高度=%d mm, 相对高度=%d mm\n", 
            pos.lat, pos.lon, pos.alt, pos.relative_alt);
    
    dronepod_status_t * status = get_dronepod_status();
    status->plane_lontitude = pos.lon;
    status->plane_latitude = pos.lat;
    //status->plane_altitude = pos.alt / 100;
}

void handle_mavlink_altitude_message(mavlink_message_t *msg)
{
    mavlink_altitude_t altitude;
    mavlink_msg_altitude_decode(msg, &altitude);
    
    LOG_I("收到MAVLink高度消息: 相对高度=%.2f, 绝对高度=%.2f\n", 
            altitude.altitude_relative, altitude.altitude_amsl);
    dronepod_status_t * status = get_dronepod_status();
    status->plane_altitude = altitude.altitude_relative;
}

void *gimbalmsg_consumer_thread(void *arg) 
{
    while (!exit_flag)
    {
        fcs_heartbeat_data_fields_t data;
        
        // 阻塞式取出数据（队列空时等待）
        global_heartbeat_queue_pop(&data);

        send_fcs_command(&data);
    }
    
}

void start_gimbalmsg_consumer_proc()
{
    // 初始化队列
    if (start_heartbeat_queue() != 0) {
        fprintf(stderr, "Failed to initialize heartbeat queue\n");
        return EXIT_FAILURE;
    }

    pthread_t consumer_tid;
    // 创建消费者线程
    if (pthread_create(&consumer_tid, NULL, gimbalmsg_consumer_thread, NULL) != 0) {
        perror("Failed to create consumer thread");
        stop_heartbeat_queue();
        return EXIT_FAILURE;
    }

}


void stop_gimbal()
{
    exit_flag = 1;
    //stop_heartbeat_queue();
}


void start_gimbal()
{
    //start_gimbalmsg_consumer_proc();

    //start serial message proc
    start_gimbal_message_proc("/dev/ttyAMA5");

    REGISTER_GIMBAL_MESSAGE_CALLBACK(0x1, E_GIMBAL_HEARTBEAT, handle_gimbal_heartbeat);
    REGISTER_GIMBAL_MESSAGE_CALLBACK(0x1, E_GIMBAL_LOG, handle_gimbal_log);


    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_HEARTBEAT, handle_mavlink_hearbeat_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_ATTITUDE, handle_mavlink_attitude_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_ATTITUDE_TARGET, handle_mavlink_attitude_target_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_SCALED_IMU, handle_mavlink_scaled_imu_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_POSITION_TARGET_LOCAL_NED, handle_mavlink_postion_target_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_GLOBAL_POSITION_INT, handle_mavlink_gps_message);
    REGISTER_FC_MESSAGE_CALLBACK(MAVLINK_MSG_ID_ALTITUDE, handle_mavlink_altitude_message);
    
}
