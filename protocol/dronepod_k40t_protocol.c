

#include "dronepod_k40t_protocol.h"
#include "dronepod_k40t_message.h"
#include "snap_photo.h"
#include "gimbal_message.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#define LOG_LEVEL LOG_LEVEL_ERROR
#include "log_utils.h"

extern void handle_stop_track_cmd();
extern void handle_start_track_cmd(unsigned int x, unsigned int y, unsigned int w, unsigned int h);




uint8_t g_send_seq = 0; // 全局发送序列号示例
uint8_t get_next_send_sequence(void) {
    return g_send_seq++; // 注意处理翻转 (0-255)
}


/**
 * @brief 封装一个通用的ACK响应消息 (支持自定义载荷结构体)
 *
 * @param dest_sys_id       目标系统ID
 * @param dest_comp_id      目标组件ID
 * @param src_sys_id        源系统ID (本机发送方)
 * @param src_comp_id       源组件ID (本机发送方)
 * @param seq               本条消息的序列号
 * @param ack_msg_id        要发送的ACK消息的完整MSG ID (例如 MSG_ID_ACK_GIMBAL_CONTROL)
 * @param ack_payload_data  指向ACK载荷数据的指针 (可以是任何结构体)
 * @param ack_payload_len   ACK载荷数据的长度 (字节)
 * @param out_buffer        用于存放封装好的消息帧的缓冲区指针
 * @param out_buffer_max_len out_buffer 的最大长度
 * @return int              成功则返回封装后消息帧的实际长度，失败则返回0或负数错误码
 */
int package_custom_ack(
    uint8_t dest_sys_id,
    uint8_t dest_comp_id,
    uint8_t src_sys_id,
    uint8_t src_comp_id,
    uint8_t seq,
    uint32_t ack_msg_id,
    const void *ack_payload_data,  // 自定义载荷数据指针
    uint8_t ack_payload_len,       // 自定义载荷长度
    uint8_t *out_buffer,
    int out_buffer_max_len)
{
    // 1. 检查载荷数据和长度
    if (ack_payload_data == NULL && ack_payload_len > 0) {
        printf("错误: ACK载荷数据为空，但长度大于0\n");
        return -1; // 参数错误
    }
    if (ack_payload_len > 243) { // K40T协议最大载荷长度
        printf("错误: ACK载荷长度 %d 超过最大限制243\n", ack_payload_len);
        return -2; // 载荷过长
    }

    // 2. 计算总帧长: STX(1) + LEN(1) + 固定头(8) + 载荷(ack_payload_len) + CRC(2) = 12 + ack_payload_len
    int total_frame_len = 12 + ack_payload_len;

    // 3. 检查输出缓冲区大小
    if (out_buffer_max_len < total_frame_len) {
        printf("错误: 输出缓冲区太小，需要 %d, 提供 %d\n", total_frame_len, out_buffer_max_len);
        return 0; // 或返回一个特定的错误码
    }

    // 4. 填充帧头到输出缓冲区
    out_buffer[0] = 0xFD;                               // STX
    out_buffer[1] = ack_payload_len;                    // LEN (载荷长度)
    out_buffer[2] = dest_sys_id;                        // 目标系统ID
    out_buffer[3] = dest_comp_id;                       // 目标组件ID
    out_buffer[4] = seq;                                // 序列号
    out_buffer[5] = src_sys_id;                         // 源系统ID
    out_buffer[6] = src_comp_id;                        // 源组件ID
    out_buffer[7] = (uint8_t)(ack_msg_id & 0xFF);       // MSG ID Low
    out_buffer[8] = (uint8_t)((ack_msg_id >> 8) & 0xFF);  // MSG ID Mid
    out_buffer[9] = (uint8_t)((ack_msg_id >> 16) & 0xFF); // MSG ID High

    // 5. 拷贝载荷 (如果载荷长度大于0)
    // 偏移量10 = STX(1) + LEN(1) + dest_sys(1) + dest_comp(1) + seq(1) + src_sys(1) + src_comp(1) + msg_id(3)
    if (ack_payload_len > 0 && ack_payload_data != NULL) {
        memcpy(out_buffer + 10, ack_payload_data, ack_payload_len);
    }

    // 6. 计算CRC校验和
    // 校验数据从0XFD之后开始，即从LEN字段开始，到Payload的末尾
    // 参与CRC计算的数据长度 = 1 (LEN字段) + 8 (LEN之后到MSG_ID_High的固定头) + ack_payload_len
    uint16_t crc_data_len = 1 + 8 + ack_payload_len;
    uint16_t checksum = k_crc_calculate(out_buffer + 1, crc_data_len); // 使用文档指定的CRC函数

    // 7. 填充CRC校验和 (小端模式)
    int crc_offset = 10 + ack_payload_len;
    out_buffer[crc_offset] = (uint8_t)(checksum & 0xFF);       // CRC Low
    out_buffer[crc_offset + 1] = (uint8_t)((checksum >> 8) & 0xFF); // CRC High

    // 8. 返回总帧长
    return total_frame_len;
}

/**
 * @brief 封装一个通用的ACK响应消息 (原有函数，保持兼容性)
 *
 * @param dest_sys_id       目标系统ID
 * @param dest_comp_id      目标组件ID
 * @param src_sys_id        源系统ID (本机发送方)
 * @param src_comp_id       源组件ID (本机发送方)
 * @param seq               本条消息的序列号
 * @param ack_msg_id        要发送的ACK消息的完整MSG ID (例如 MSG_ID_ACK_GIMBAL_CONTROL)
 * @param response_code     响应码 (例如 0x0000 表示成功)
 * @param out_buffer        用于存放封装好的消息帧的缓冲区指针
 * @param buffer_len        out_buffer 的最大长度
 * @return int              成功则返回封装后消息帧的实际长度，失败则返回0或负数错误码
 */
int package_generic_ack(
    uint8_t dest_sys_id,
    uint8_t dest_comp_id,
    uint8_t src_sys_id,
    uint8_t src_comp_id,
    uint8_t seq,
    uint32_t ack_msg_id,
    uint16_t response_code,
    uint8_t *out_buffer,
    int out_buffer_max_len)
{
    // 1. 定义载荷
    Ack_Generic_Response_t ack_payload;
    ack_payload.response_code = response_code; // uint16_t, 在小端机器上直接赋值即可满足协议要求

    // 2. 使用新的通用函数
    return package_custom_ack(
        dest_sys_id,
        dest_comp_id,
        src_sys_id,
        src_comp_id,
        seq,
        ack_msg_id,
        &ack_payload,                           // 载荷数据指针
        sizeof(Ack_Generic_Response_t),         // 载荷长度
        out_buffer,
        out_buffer_max_len
    );
}

// (可以将此函数添加到 dronepod_k40t_message.c 文件中)

/**
 * @brief 封装一个通用的状态帧消息
 *
 * @param dest_sys_id           目标系统ID
 * @param dest_comp_id          目标组件ID
 * @param src_sys_id            源系统ID (本机发送方)
 * @param src_comp_id           源组件ID (本机发送方)
 * @param seq                   本条消息的序列号
 * @param state_msg_id          要发送的状态消息的完整MSG ID (例如 MSG_ID_STATE_CAM_SHOOT)
 * @param state_payload_data    指向实际状态载荷数据的指针 (const void* 以接受任何类型的结构体)
 * @param state_payload_len     实际状态载荷数据的长度 (字节)
 * @param out_buffer            用于存放封装好的消息帧的缓冲区指针
 * @param out_buffer_max_len    out_buffer 的最大长度
 * @return int                  成功则返回封装后消息帧的实际长度，失败则返回0或负数错误码
 */
int package_state_message(
    uint8_t dest_sys_id,
    uint8_t dest_comp_id,
    uint8_t src_sys_id,
    uint8_t src_comp_id,
    uint8_t seq,
    uint32_t state_msg_id,
    const void *state_payload_data, // 指向实际的状态数据结构体
    uint8_t state_payload_len,      // 状态数据结构体的长度
    uint8_t *out_buffer,
    int out_buffer_max_len)
{
    // 1. 检查载荷数据和长度
    if (state_payload_data == NULL && state_payload_len > 0) {
        printf("错误: 状态载荷数据为空，但长度大于0\n");
        return -1; // 参数错误




    }
    if (state_payload_len > 243) { // K40T协议最大载荷长度
        printf("错误: 状态载荷长度 %d 超过最大限制243\n", state_payload_len);
        return -2; // 载荷过长
    }

    // 2. 计算总帧长: STX(1) + LEN(1) + 固定头(8) + 载荷(state_payload_len) + CRC(2) = 12 + state_payload_len
    int total_frame_len = 12 + state_payload_len;

    // 3. 检查输出缓冲区大小
    if (out_buffer_max_len < total_frame_len) {
        printf("错误: 输出缓冲区太小，需要 %d, 提供 %d\n", total_frame_len, out_buffer_max_len);
        return 0;
    }

    // 4. 填充帧头到输出缓冲区
    out_buffer[0] = 0xFD;                                   // STX
    out_buffer[1] = state_payload_len;                      // LEN (实际载荷长度)
    out_buffer[2] = dest_sys_id;                            // 目标系统ID
    out_buffer[3] = dest_comp_id;                           // 目标组件ID
    out_buffer[4] = seq;                                    // 序列号
    out_buffer[5] = src_sys_id;                             // 源系统ID
    out_buffer[6] = src_comp_id;                            // 源组件ID
    out_buffer[7] = (uint8_t)(state_msg_id & 0xFF);         // MSG ID Low
    out_buffer[8] = (uint8_t)((state_msg_id >> 8) & 0xFF);  // MSG ID Mid
    out_buffer[9] = (uint8_t)((state_msg_id >> 16) & 0xFF); // MSG ID High

    // 5. 拷贝载荷 (如果载荷长度大于0)
    // 偏移量10 = STX(1) + LEN(1) + ... + msg_id(3)
    if (state_payload_len > 0) {
        memcpy(out_buffer + 10, state_payload_data, state_payload_len);
    }

    // 6. 计算CRC校验和
    // 校验数据从0XFD之后开始，即从LEN字段开始，到Payload的末尾
    // 参与CRC计算的数据长度 = 1 (LEN字段) + 8 (LEN之后到MSG_ID_High的固定头) + state_payload_len
    uint16_t crc_data_len = 1 + 8 + state_payload_len;
    uint16_t checksum = k_crc_calculate(out_buffer + 1, crc_data_len); // 使用文档指定的CRC函数

    // 7. 填充CRC校验和 (小端模式)
    int crc_offset = 10 + state_payload_len;
    out_buffer[crc_offset] = (uint8_t)(checksum & 0xFF);       // CRC Low
    out_buffer[crc_offset + 1] = (uint8_t)((checksum >> 8) & 0xFF); // CRC High

    // 8. 返回总帧长
    return total_frame_len;
}

// (可以放在 fpv_protocol_parse 函数之前，或其他合适的位置)

/**
 * @brief 处理云台状态消息的回调函数 云台状态消息 (MsgID: 0x000001)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_gimbal_status_message(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    // 检查payload长度是否足够，header->len 应该是实际payload的长度
    if (header->len < sizeof(Payload_Gimbal_Status_0x000001_t)) {
        printf("Error: Gimbal Status Message (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_GIMBAL_STATUS, sizeof(Payload_Gimbal_Status_0x000001_t), header->len);
        return;
    }

    Payload_Gimbal_Status_0x000001_t *status_payload = (Payload_Gimbal_Status_0x000001_t *)header->payload;

    printf("--- Received Gimbal Status (0x%06X) ---\n", MSG_ID_GIMBAL_STATUS);
    printf("  Gimbal/Camera Status: 0x%02X (Gimbal: %s, Camera: %s)\n",
           status_payload->gimbal_camera_status,
           (status_payload->gimbal_camera_status & 0x0F) == 0 ? "OK" : "Fail",
           (status_payload->gimbal_camera_status >> 4) == 0 ? "OK" : "Fail");
    printf("  Upgrade Status: 0x%02X\n", status_payload->upgrade_status);
    printf("  Self-Check Result: 0x%02X\n", status_payload->self_check_result);
    // 可以进一步解析 self_check_result 的各个比特位
    // 例如:
    // printf("    IR Core: %s\n", (status_payload->self_check_result & 0x01) ? "Normal" : "Abnormal");
    // printf("    Tele VL Core: %s\n", (status_payload->self_check_result & 0x02) ? "Normal" : "Abnormal");
    // ...
    printf("  Stabilization Status: 0x%02X\n", status_payload->stabilization_status);
    printf("----------------------------------------\n");

    // 在这里根据云台状态执行相应的逻辑
}




/**
 * @brief 上报飞控云台姿态信息的回调函数  云台姿态信息 (MsgID: 0x000002)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_gimbal_attitude_message_report( 
    uint16_t joint_yaw_angle,        // 字节1-2: 云台偏航角度(关节角), 无符号整型, 单位: 度*100
    uint16_t joint_roll_angle,       // 字节3-4: 云台横滚角度(关节角), 无符号整型, 单位: 度*100
    uint16_t joint_pitch_angle,      // 字节5-6: 云台俯仰角度(关节角), 无符号整型, 单位: 度*100
    uint16_t attitude_yaw_angle,     // 字节7-8: 云台偏航角度(姿态角), 无符号整型, 单位: 度*100
    uint16_t attitude_roll_angle,    // 字节9-10: 云台横滚角度(姿态角), 无符号整型, 单位: 度*100
    uint16_t attitude_pitch_angle,   // 字节11-12: 云台俯仰角度(姿态角), 无符号整型, 单位: 度*100
    int16_t  yaw_angular_velocity,   // 字节13-14: 云台偏航角速度, int16, 单位: (度/秒)*100
    int16_t  pitch_angular_velocity, // 字节15-16: 云台俯仰角速度, int16, 单位: (度/秒)*100
    int16_t  roll_angular_velocity  // 字节17-18: 云台横滚角速度, int16, 单位: (度/秒)*100) 
    ){

    Payload_Gimbal_Attitude_0x000002_t attitude_payload = {0};//(Payload_Gimbal_Attitude_0x000002_t *)header->payload;

    //printf("--- ready to Send Gimbal Attitude (0x%06X) ---\n", MSG_ID_GIMBAL_ATTITUDE);
    // // 关节角 (单位: 度*100)
    // printf("  Joint Angles (deg*100): Yaw=%.2f, Roll=%.2f, Pitch=%.2f\n",
    //        (float)attitude_payload->joint_yaw_angle / 100.0f,
    //        (float)attitude_payload->joint_roll_angle / 100.0f,
    //        (float)attitude_payload->joint_pitch_angle / 100.0f);
    // // 姿态角 (单位: 度*100)
    // printf("  Attitude Angles (deg*100): Yaw=%.2f, Roll=%.2f, Pitch=%.2f\n",
    //        (float)attitude_payload->attitude_yaw_angle / 100.0f,
    //        (float)attitude_payload->attitude_roll_angle / 100.0f,
    //        (float)attitude_payload->attitude_pitch_angle / 100.0f);
    // // 角速度 (单位: (度/秒)*100)
    // printf("  Angular Velocities ((deg/s)*100): Yaw=%.2f, Pitch=%.2f, Roll=%.2f\n",
    //        (float)attitude_payload->yaw_angular_velocity / 100.0f,
    //        (float)attitude_payload->pitch_angular_velocity / 100.0f,
    //        (float)attitude_payload->roll_angular_velocity / 100.0f);
    // printf("------------------------------------------\n");
    attitude_payload.attitude_pitch_angle = attitude_pitch_angle;
    attitude_payload.attitude_roll_angle = attitude_roll_angle;
    attitude_payload.attitude_yaw_angle = attitude_yaw_angle;
    attitude_payload.joint_pitch_angle = joint_pitch_angle;
    attitude_payload.joint_roll_angle = joint_roll_angle;
    attitude_payload.joint_yaw_angle = joint_yaw_angle;
    attitude_payload.yaw_angular_velocity = yaw_angular_velocity;
    attitude_payload.pitch_angular_velocity = pitch_angular_velocity;
    attitude_payload.roll_angular_velocity = roll_angular_velocity;


    // 在这里根据云台姿态信息执行相应的逻辑
    uint8_t status_tx_buffer[256] = {0}; // 状态帧可能比ACK大
    uint8_t status_seq = get_next_send_sequence(); // 新的序列号


    //printf("  准备发送云台姿态帧 (0x%06X) ", MSG_ID_GIMBAL_ATTITUDE);


    int status_frame_len = package_state_message(
        SYS_ID_FC,                          // 目标是原始指令的发送者
        COMP_ID_FC,
        SYS_ID_GIMBAL,                     // 本机ID
        COMP_ID_GIMBAL,
        status_seq,
        MSG_ID_GIMBAL_ATTITUDE,             // 云台姿态帧
        &attitude_payload,
        sizeof(Payload_Gimbal_Attitude_0x000002_t),
        status_tx_buffer,
        sizeof(status_tx_buffer)
    );

    if (status_frame_len > 0) {
        send_fpv_message((char*)status_tx_buffer, status_frame_len); // 实际发送
        //printf("  模拟发送状态帧数据: "); for(int i=0; i<status_frame_len; ++i) printf("%02X ", status_tx_buffer[i]); printf("\n");
    } else {
        LOG_I("错误: 云台状态帧 (0x%06X) 封装失败.\n", MSG_ID_GIMBAL_ATTITUDE);
    }

}


// (Place these callback function definitions before fpv_protocol_parse or in a logical section)

/**
 * @brief 处理通用ACK消息的回调函数
 * @param data 接收到的完整协议帧数据指针
 * @param ack_msg_id 具体的ACK消息ID (用于打印日志)
 */
void handle_generic_ack_message(const char *data, uint32_t ack_msg_id) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Ack_Generic_Response_t)) {
        printf("Error: ACK Message (0x%06X) payload too short. Expected %zu, got %d\n",
               ack_msg_id, sizeof(Ack_Generic_Response_t), header->len);
        return;
    }

    Ack_Generic_Response_t *ack_payload = (Ack_Generic_Response_t *)header->payload;

    printf("--- Received ACK (0x%06X) ---\n", ack_msg_id);
    printf("  Response Code: 0x%04X (%s)\n", ack_payload->response_code,
           ack_payload->response_code == 0x0000 ? "OK" : "Error/Specific");
    printf("-----------------------------------\n");

    // 根据 ack_msg_id 和 response_code 执行特定逻辑
    // 例如, 更新命令发送状态，重试等

    // char tx_buffer[256] = {0};
    // int frame_len = package_generic_ack(
    //     header->src_sys_id,         // 源系统ID (即原始命令的接收者)
    //     header->src_comp_id,        // 源组件ID
    //     header->dest_sys_id,        // 本机appid SYS_ID_APP, ?
    //     header->dest_comp_id,       // 本机组件ID
    //     get_next_send_sequence(),   // 本条ACK的序列号
    //     ack_msg_id,                 // ACK的消息ID (0x010010)
    //     ack,                        // 响应码: 0x0000 表示成功
    //     tx_buffer,                  // 输出缓冲区
    //     sizeof(tx_buffer)           // 输出缓冲区的大小
    // );

    // if (frame_len > 0) {
    //     printf("封装ACK成功, 长度: %d bytes. 准备发送...\n", frame_len);
    //     // print_fuffer_in_Hex(tx_buffer, frame_len); // 打印十六进制数据以供调试
    //     // serial_write(tx_buffer, frame_len); // 通过串口发送数据
    //     send_fpv_message((char *)&tx_buffer, frame_len);
    // } else {
    //     printf("封装ACK失败.\n");
    // }
}


void handle_generic_ack_message_ex(const char *data, uint32_t ack_msg_id, uint32_t ack) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    // if (header->len < sizeof(Ack_Generic_Response_t)) {
    //     printf("Error: ACK Message (0x%06X) payload too short. Expected %zu, got %d\n",
    //            ack_msg_id, sizeof(Ack_Generic_Response_t), header->len);
    //     return;
    // }

    // Ack_Generic_Response_t *ack_payload = (Ack_Generic_Response_t *)header->payload;

    // printf("--- Received ACK (0x%06X) ---\n", ack_msg_id);
    // printf("  Response Code: 0x%04X (%s)\n", ack_payload->response_code,
    //        ack_payload->response_code == 0x0000 ? "OK" : "Error/Specific");
    // printf("-----------------------------------\n");

    // 根据 ack_msg_id 和 response_code 执行特定逻辑
    // 例如, 更新命令发送状态，重试等

    char tx_buffer[256] = {0};
    int frame_len = package_generic_ack(
        header->src_sys_id,         // 源系统ID (即原始命令的接收者)
        header->src_comp_id,        // 源组件ID
        header->dest_sys_id,        // 本机appid SYS_ID_APP, ?
        header->dest_comp_id,       // 本机组件ID
        get_next_send_sequence(),   // 本条ACK的序列号
        ack_msg_id,                 // ACK的消息ID (0x010010)
        ack,                        // 响应码: 0x0000 表示成功
        tx_buffer,                  // 输出缓冲区
        sizeof(tx_buffer)           // 输出缓冲区的大小
    );

    if (frame_len > 0) {
        printf("封装ACK成功, 长度: %d bytes. 准备发送...\n", frame_len);
        // print_fuffer_in_Hex(tx_buffer, frame_len); // 打印十六进制数据以供调试
        // serial_write(tx_buffer, frame_len); // 通过串口发送数据
        send_fpv_message((char *)&tx_buffer, frame_len);
    } else {
        printf("封装ACK失败.\n");
    }
}

// Specific ACK Callbacks (can call the generic one or have unique logic)

void handle_ack_gimbal_control(const char *data) {
    printf("-----handle_ack_gimbal_control-----\n");
    // MSG_ID_ACK_GIMBAL_CONTROL is defined as 0x010010 in your list
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_Gimbal_Control_Req_0x000010_t *req = (Payload_Gimbal_Control_Req_0x000010_t*)header->payload;
    printf("work_mode_quick_func : %d\n",req->work_mode_quick_func);
    printf("yaw_control: %d\n",req->yaw_control);
    printf("pitch_control: %d\n", req->pitch_control);
    if(req->work_mode_quick_func == 16){
        printf("-----send_fcs_return_center-----\n");
        send_fcs_return_center();
    }else if(req->work_mode_quick_func == 32){
        printf("-----send_fcs_return_center-----\n");
        send_fcs_look_down_at_once();
    }
    else if(req->work_mode_quick_func == 0){
        if (req->yaw_control == 0)
        {
            //left
            send_fcs_look_left();
        }else if (req->yaw_control == 1)
        {
            //right
            send_fcs_look_right();
        }
        
        if (req->pitch_control == 0)
        {
            send_fcs_look_up();
            //up
        }else if (req->pitch_control == 1)
        {
            //down
            send_fcs_look_down();
        }
        
    }
    handle_generic_ack_message_ex(data, MSG_ID_ACK_GIMBAL_CONTROL,0);
}

void handle_ack_gimbal_angle_set(const char *data) {
    // MSG_ID_ACK_GIMBAL_ANGLE_SET is 0x010012
    handle_generic_ack_message(data, MSG_ID_ACK_GIMBAL_ANGLE_SET);
}

void handle_ack_gimbal_calibrate(const char *data) {
    // MSG_ID_ACK_GIMBAL_CALIBRATE is 0x010013
    handle_generic_ack_message(data, MSG_ID_ACK_GIMBAL_CALIBRATE);
}

void handle_ack_gimbal_speed_set(const char *data) {
    // MSG_ID_ACK_GIMBAL_SPEED_SET is 0x010017
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_Gimbal_Dial_Speed_Req_0x000017_t *payload = (Payload_Gimbal_Dial_Speed_Req_0x000017_t *)header->payload;
    printf("-----handle_ack_gimbal_speed_set-----\n");
    printf("pitch_dial_speed: %d\n", payload->pitch_dial_speed);
    printf("yaw_dial_speed: %d\n", payload->yaw_dial_speed);
    //set_gimbal_speed(payload->pitch_dial_speed, payload->yaw_dial_speed);
    handle_generic_ack_message_ex(data, MSG_ID_ACK_GIMBAL_SPEED_SET, 0);
}

void handle_ack_gimbal_point_align(const char *data) {
    // MSG_ID_ACK_GIMBAL_POINT_ALIGN is 0x01002C
    handle_generic_ack_message(data, MSG_ID_ACK_GIMBAL_POINT_ALIGN);
}

void handle_ack_gimbal_servo_off(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_GIMBAL_SERVO_OFF);
}

void handle_ack_gimbal_linear_cal(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_GIMBAL_LINEAR_CAL);
}

void handle_ack_gimbal_reboot(const char *data) {

    printf("-----handle_ack_gimbal_reboot-----\n");
    send_fcs_reboot_example();
    handle_generic_ack_message_ex(data, MSG_ID_ACK_GIMBAL_REBOOT, 0);
}

void handle_ack_gimbal_fc_attitude_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_GIMBAL_FC_ATT_SET);
}

void handle_ack_gimbal_acc_cal(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_GIMBAL_ACC_CAL);
}

void handle_ack_fc_info_to_gimbal(const char* data) {
    printf("-----handle_ack_fc_info_to_gimbal-----\n");
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_FC_Info_To_Gimbal_Req_0x000032_t *payload = (Payload_FC_Info_To_Gimbal_Req_0x000032_t *)header->payload;
    printf("drone_desired_yaw: %d\n", payload->drone_desired_yaw);
    printf("drone_pitch: %d\n", payload->drone_pitch);
    printf("drone_roll: %d\n", payload->drone_roll);
    printf("drone_yaw: %d\n", payload->drone_yaw);
    printf("accel_x: %d\n", payload->accel_x);
    printf("accel_y: %d\n", payload->accel_y);
    printf("accel_z: %d\n", payload->accel_z);
    printf("takeoff_status_fc_data_valid: %d\n", payload->takeoff_status_fc_data_valid);
    
    send_fcs_info_to_gimbal(payload->drone_desired_yaw, 
    payload->drone_pitch, payload->drone_roll, 
    payload->drone_yaw, 
    payload->accel_x, 
    payload->accel_y, 
    payload->accel_z, 
    payload->takeoff_status_fc_data_valid);
    
    handle_generic_ack_message_ex(data, MSG_ID_ACK_FC_INFO_TO_GIMBAL, 0);
}

void handle_ack_gimbal_stabilize_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_GIMBAL_STABILIZE_SET);
}


/**
 * @brief 处理获取云台版本号的ACK消息
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_gimbal_version_req(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;

    printf("--- 处理云台版本获取请求 (0x%06X) ---\n", MSG_ID_GIMBAL_VERSION_REQ);

    // 创建版本信息ACK载荷
    Ack_Gimbal_Get_Version_0x000018_t version_ack = {0};
    version_ack.response_code = 0x0000;  // 成功

    // 填充版本信息 (示例数据，实际应从系统获取)
    version_ack.hw_major = 1;
    version_ack.hw_minor = 2;
    version_ack.hw_patch = 3;
    version_ack.sw_major = 2;
    version_ack.sw_minor = 1;
    version_ack.sw_patch = 0;

    printf("  硬件版本: %d.%d.%d\n", version_ack.hw_major, version_ack.hw_minor, version_ack.hw_patch);
    printf("  软件版本: %d.%d.%d\n", version_ack.sw_major, version_ack.sw_minor, version_ack.sw_patch);

    // 使用新的自定义ACK函数发送响应
    uint8_t tx_buffer[256] = {0};
    int frame_len = package_custom_ack(
        header->src_sys_id,         // 目标是原始指令的发送者
        header->src_comp_id,        // 目标组件ID
        header->dest_sys_id,        // 本机系统ID
        header->dest_comp_id,       // 本机组件ID
        get_next_send_sequence(),   // 序列号
        MSG_ID_ACK_GIMBAL_VERSION_REQ,  // ACK消息ID
        &version_ack,               // 自定义ACK载荷
        sizeof(Ack_Gimbal_Get_Version_0x000018_t),  // 载荷长度
        tx_buffer,
        sizeof(tx_buffer)
    );

    if (frame_len > 0) {
        send_fpv_message((char*)tx_buffer, frame_len);
        printf("  云台版本ACK发送成功, 长度: %d bytes\n", frame_len);
    } else {
        printf("  错误: 云台版本ACK封装失败\n");
    }

    printf("--------------------------------------------------\n");
}








/**
 * @brief 处理相机系统状态反馈 (MSG_ID_CAM_SYS_STATUS 0x000003)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_cam_sys_status_report(Payload_Camera_System_Status_0x000003_t *payload)
 {

    LOG_I("--- 收到相机系统状态 (0x%06X) ---\n", MSG_ID_CAM_SYS_STATUS);
    LOG_I("  拍照/录像模式: %s\n", payload->photo_video_mode == 0 ? "拍照" : "录像");
    LOG_I("  网络出图分辨率: %s\n", payload->network_resolution == 0 ? "1080P30fps" : "720P30fps");
    LOG_I("  视频编码: %s\n", payload->video_encoding == 0 ? "H264" : "H265");
    LOG_I("  推流模式: 0x%02X\n", payload->streaming_mode);
    LOG_I("  视频输出码率: %d\n", payload->video_output_bitrate); // 具体值对应实际码率
    LOG_I("  拍照模式: %d (0:单拍, 1:连拍, 2:延时)\n", payload->photo_mode);
    LOG_I("  延时拍照间隔: %d秒\n", payload->timed_photo_interval);
    LOG_I("  连拍张数: %d\n", payload->burst_count);
    LOG_I("  SD卡状态: %d (0:正常, 1:异常, 2:慢, 3:未插入, 4:满, 5:格式错误)\n", payload->sd_card_status);
    LOG_I("  SD卡总容量: %.2f GB\n", (float)payload->sd_total_capacity * 10 / 1024.0f);
    LOG_I("  SD卡剩余容量: %.2f GB\n", (float)payload->sd_remaining_capacity * 10 / 1024.0f);
    LOG_I("  SD卡已用容量: %.2f GB\n", (float)payload->sd_used_capacity * 10 / 1024.0f);
    LOG_I("--------------------------------------\n");



    uint8_t status_tx_buffer[256] = {0}; // 状态帧可能比ACK大
    uint8_t status_seq = get_next_send_sequence(); // 新的序列号

    int status_frame_len = package_state_message(
        SYS_ID_FC,                          // 目标是原始指令的发送者
        COMP_ID_FC,
        SYS_ID_GIMBAL,                         // 本机ID
        COMP_ID_GIMBAL,
        status_seq,
        MSG_ID_CAM_SYS_STATUS,              // 红外状态帧
        payload,
        sizeof(Payload_Camera_System_Status_0x000003_t),
        status_tx_buffer,
        sizeof(status_tx_buffer)
    );

    if (status_frame_len > 0) {
        send_fpv_message((char*)status_tx_buffer, status_frame_len); // 实际发送
    } else {
        LOG_E("错误: 系统状态帧 (0x%06X) 封装失败.\n", MSG_ID_CAM_SYS_STATUS);
    }
}

/**
 * @brief 处理红外相机状态反馈 (MSG_ID_IR_STATUS 0x000004)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ir_status_report(Payload_IR_Camera_Status_0x000004_t *payload)
 {


    LOG_I("--- 准备上报红外相机状态 (0x%06X) ---\n", MSG_ID_IR_STATUS);
    LOG_I("  区域最高温: %.1f C (X:%u, Y:%u)\n", (float)payload->area_max_temp / 10.0f, payload->area_max_temp_x, payload->area_max_temp_y);
    LOG_I("  区域最低温: %.1f C (X:%u, Y:%u)\n", (float)payload->area_min_temp / 10.0f, payload->area_min_temp_x, payload->area_min_temp_y);
    LOG_I("  区域中心温: %.1f C (X:%u, Y:%u)\n", (float)payload->area_center_temp / 10.0f, payload->area_center_temp_x, payload->area_center_temp_y);
    LOG_I("  点测温: %.1f C (X:%u, Y:%u)\n", (float)payload->spot_temp / 10.0f, payload->spot_temp_x, payload->spot_temp_y);
    LOG_I("  区域平均温: %.1f C\n", (float)payload->area_avg_temp / 10.0f);
    LOG_I("  高温预警: %s\n", payload->high_temp_alert ? "是" : "否");
    LOG_I("  低温预警: %s\n", payload->low_temp_alert ? "是" : "否");
    LOG_I("  温差预警: %s\n", payload->temp_diff_alert ? "是" : "否");
    LOG_I("  阈值预警: %s\n", payload->threshold_temp_alert ? "是" : "否");
    LOG_I("--------------------------------------\n");
    // 在此处理红外相机状态

    uint8_t status_tx_buffer[256] = {0}; // 状态帧可能比ACK大
    uint8_t status_seq = get_next_send_sequence(); // 新的序列号

    int status_frame_len = package_state_message(
        SYS_ID_FC,                          // 目标是原始指令的发送者
        COMP_ID_FC,
        SYS_ID_GIMBAL,                         // 本机ID
        COMP_ID_GIMBAL,
        status_seq,
        MSG_ID_IR_STATUS,                   //红外状态帧
        payload,
        sizeof(Payload_IR_Camera_Status_0x000004_t),
        status_tx_buffer,
        sizeof(status_tx_buffer)
    );

    if (status_frame_len > 0) {
        send_fpv_message((char*)status_tx_buffer, status_frame_len); // 实际发送
    } else {
        LOG_I("错误: 红外状态帧 (0x%06X) 封装失败.\n", MSG_ID_IR_STATUS);
    }


}

/**
 * @brief 处理可见光相机状态反馈 (MSG_ID_VISIBLE_STATUS 0x000005)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_visible_status_report( Payload_VL_Camera_Status_0x000005_t * payload)
{
    //Payload_VL_Camera_Status_0x000005_t payload = {0};

    LOG_I("--- 收到可见光相机状态 (0x%06X) ---\n", MSG_ID_VISIBLE_STATUS);
    LOG_I("  变倍状态: %s\n", payload->zoom_status == 0x00 ? "完成" : "正在变倍");
    LOG_I("  焦距: %.2f mm\n", (float)payload->focal_length / 100.0f);
    LOG_I("  混合变倍倍率: %.1f x\n", (float)payload->hybrid_zoom_ratio / 10.0f);

    // printf("  EV值: 0x%02X\n", payload.ev_value);
    // printf("  ISO值: %u (单位0.1dB?)\n", payload.iso_value);
    // printf("  电子快门: %u us\n", payload.shutter_speed);
    // printf("  AE LOCK状态: %s\n", payload.ae_lock_status == 0x01 ? "开" : "关");
    // printf("  对焦状态: %s\n", payload.focus_status == 0x00 ? "完成" : "正在对焦");
    // printf("  精准复拍焦距: %u\n", payload.precise_refocus_focal_length);
    // printf("-------------------------------------------\n");
    // 在此处理可见光相机状态

    
    // 在这里根据云台姿态信息执行相应的逻辑
    uint8_t status_tx_buffer[256] = {0}; // 状态帧可能比ACK大
    uint8_t status_seq = get_next_send_sequence(); // 新的序列号


    int status_frame_len = package_state_message(
        SYS_ID_FC,                          // 目标是原始指令的发送者
        COMP_ID_FC,
        SYS_ID_GIMBAL,                     // 本机ID
        COMP_ID_GIMBAL,
        status_seq,
        MSG_ID_VISIBLE_STATUS,             // 云台姿态帧
        payload,
        sizeof(Payload_VL_Camera_Status_0x000005_t),
        status_tx_buffer,
        sizeof(status_tx_buffer)
    );

    if (status_frame_len > 0) {
        send_fpv_message((char*)status_tx_buffer, status_frame_len); // 实际发送
        //printf("  模拟发送状态帧数据: "); for(int i=0; i<status_frame_len; ++i) printf("%02X ", status_tx_buffer[i]); printf("\n");
    } else {
        LOG_I("错误: 云台状态帧 (0x%06X) 封装失败.\n", MSG_ID_VISIBLE_STATUS);
    }
}

/**
 * @brief 处理相机升级与修复状态反馈 (MSG_ID_CAM_UPGRADE_STATUS 0x000008)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_cam_upgrade_status(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Payload_Camera_Upgrade_Status_0x000008_t)) {
        printf("错误: 相机升级状态 (0x%06X) 载荷过短. 需要 %zu, 收到 %d\n",
               MSG_ID_CAM_UPGRADE_STATUS, sizeof(Payload_Camera_Upgrade_Status_0x000008_t), header->len);
        return;
    }
    Payload_Camera_Upgrade_Status_0x000008_t *payload = (Payload_Camera_Upgrade_Status_0x000008_t *)header->payload;
    const char *status_str[] = {"成功", "失败", "正在升级", "正在修复"};

    printf("--- 收到相机升级/修复状态 (0x%06X) ---\n", MSG_ID_CAM_UPGRADE_STATUS);
    printf("  状态: %s (%d)\n", (payload->status <= 3 ? status_str[payload->status] : "未知"), payload->status);
    printf("  进度: %d%%\n", payload->progress);
    printf("---------------------------------------------\n");
    // 在此处理相机升级状态
}

/**
 * @brief 处理播放状态反馈 (MSG_ID_PLAYBACK_STATUS 0x000009)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_playback_status(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Payload_Playback_Status_0x000009_t)) {
        printf("错误: 播放状态 (0x%06X) 载荷过短. 需要 %zu, 收到 %d\n",
               MSG_ID_PLAYBACK_STATUS, sizeof(Payload_Playback_Status_0x000009_t), header->len);
        return;
    }
    Payload_Playback_Status_0x000009_t *payload = (Payload_Playback_Status_0x000009_t *)header->payload;
    const char *mode_str = "未知";
    if (payload->playback_mode == 2) mode_str = "播放中";
    else if (payload->playback_mode == 3) mode_str = "暂停";
    else if (payload->playback_mode == 7) mode_str = "停止";

    printf("--- 收到播放状态 (0x%06X) ---\n", MSG_ID_PLAYBACK_STATUS);
    printf("  播放状态: %s (%u)\n", mode_str, payload->playback_mode);
    printf("  播放进度: %u%%\n", payload->video_play_process);
    printf("  视频总长度: %u ms\n", payload->video_length_ms);
    printf("  当前播放位置: %u ms\n", payload->play_pos_ms);
    printf("-----------------------------------\n");
    // 在此处理播放状态
}



/**
 * @brief 处理读取红外相机所有参数的ACK (MSG_ID_ACK_IR_CONFIG_READ 0x010100)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_ir_config_read(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_IR_Read_All_Settings_Req_0x000100_t *payload = (Payload_IR_Read_All_Settings_Req_0x000100_t *)header->payload;

    printf("--- 收到红外配置读取ACK (0x%06X) ---\n", MSG_ID_ACK_IR_CONFIG_READ);


    Ack_IR_Read_All_Settings_0x000100_t response = {0};
    response.response_code = 0;
    response.pseudo_color_setting = 1;
    response.temp_measure_ezoom_config = 0xe;
    response.ir_sharpen_param = 0;
    response.ir_gain_mode = 0;
    response.ir_contrast = 0;
    response.ir_brightness = 0;
    response.denoise_setting = 0;
    response.enhance_setting = 0;
    response.high_temp_alert_thresh = 0;
    response.low_temp_alert_thresh = 0;
    response.temp_diff_alert_thresh = 0;
    response.temp_diff_baseline = 0;
    response.temp_fluctuation_value = 0;
    response.threshold_temp_alert_switch = 0;



    // 使用新的自定义ACK函数发送响应
    uint8_t tx_buffer[256] = {0};
    int frame_len = package_custom_ack(
        header->src_sys_id,         // 目标是原始指令的发送者
        header->src_comp_id,        // 目标组件ID
        header->dest_sys_id,        // 本机系统ID
        header->dest_comp_id,       // 本机组件ID
        get_next_send_sequence(),   // 序列号
        MSG_ID_ACK_IR_CONFIG_READ,  // ACK消息ID
        &response,               // 自定义ACK载荷
        sizeof(Ack_IR_Read_All_Settings_0x000100_t),  // 载荷长度
        tx_buffer,
        sizeof(tx_buffer)
    );

    if (frame_len > 0) {
        send_fpv_message((char*)tx_buffer, frame_len);
        printf("  云台版本ACK发送成功, 长度: %d bytes\n", frame_len);
    } else {
        printf("  错误: 云台版本ACK封装失败\n");
    }
}


// 为其他简单的红外ACK创建回调 (它们都使用 Ack_Generic_Response_t)
void handle_ack_ir_zoom_set(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data; 
    Payload_IR_EZoom_Set_Req_0x000105_t *payload = (Payload_IR_EZoom_Set_Req_0x000105_t *)header->payload;
    set_zoom_factor(payload->ezoom_setting * 100);
    handle_generic_ack_message_ex(data, MSG_ID_ACK_IR_ZOOM_SET, 0);
}
void handle_ack_ir_colormap_set(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data; 
    Payload_IR_Pseudo_Color_Set_Req_0x000106_t *payload = (Payload_IR_Pseudo_Color_Set_Req_0x000106_t *)header->payload;
    handle_set_pseudo_color(payload->pseudo_color_setting);
    handle_generic_ack_message_ex(data, MSG_ID_ACK_IR_COLORMAP_SET, 0);
}
void handle_ack_ir_thermal_switch(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_THERMAL_SWITCH);
}
void handle_ack_ir_sharpen_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_SHARPEN_SET);
}
void handle_ack_ir_brightness_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_BRIGHTNESS_SET);
}
void handle_ack_ir_contrast_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_CONTRAST_SET);
}
void handle_ack_ir_denoise_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_DENOISE_SET);
}
void handle_ack_ir_enhance_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_ENHANCE_SET);
}
void handle_ack_ir_spot_thermal(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_SPOT_THERMAL);
}
void handle_ack_ir_area_thermal(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_AREA_THERMAL);
}
void handle_ack_ir_gain_mode_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_GAIN_MODE_SET);
}
void handle_ack_ir_alarm_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_ALARM_SET);
}
void handle_ack_ir_thermal_overlay_switch(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_THERMAL_OVERLAY_SWITCH);
}
void handle_ack_ir_threshold_temp_diff_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_IR_THRESHOLD_TEMP_DIFF_SET);
}







/**
 * @brief 获取可见光相机当前配置信息 (模拟函数，实际应从相机系统获取)
 * @param settings 输出配置信息结构体指针
 * @return 0:成功, -1:失败
 */
static int get_vl_camera_settings(Ack_VL_Read_All_Settings_0x000200_t *settings) {
    if (settings == NULL) {
        return -1;
    }

    // 清零结构体
    memset(settings, 0, sizeof(Ack_VL_Read_All_Settings_0x000200_t));

    // 模拟当前可见光相机配置 (实际应从相机系统API获取)
    settings->response_code = 0x0000;              // 成功
    settings->photo_resolution = 0x14;             // 8000*6000
    settings->video_resolution = 0x08;             // 1080p
    settings->video_bitrate = 0x05;                // 中等码率
    settings->white_balance = 0x01;                // 自动白平衡
    settings->reserved1 = 0x0000;                  // 预留
    settings->ev_value = 0x00;                     // Auto EV
    settings->iso_setting = 0x00;                  // AUTO ISO
    settings->shutter_speed = 0x01;                // 自动快门
    settings->zoom_finetune_value = 50;            // 变焦微调值50%
    settings->backlight_compensation = 0x80 | 30;  // 开启，值30
    settings->strong_light_suppression = 0x80 | 20; // 开启，值20
    settings->ae_lock_feedback = 0x02;             // AE LOCK关闭
    settings->osd_watermark_switch = 0x001F;       // 前5个水印开启
    settings->anti_flicker_status = 0x04;          // 自动抗闪烁
    settings->metering_mode_feedback = 0x02;       // 区域测光

    printf("获取可见光相机配置成功\n");
    return 0;
}

/**
 * @brief 处理读取可见光相机所有参数的请求 (MSG_ID_VL_ALL_SETTINGS_READ 0x000200)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_vl_all_settings_read(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;

    // 检查载荷长度
    if (header->len < sizeof(Payload_VL_Read_All_Settings_Req_0x000200_t)) {
        printf("错误: 可见光配置读取请求 (0x%06X) 载荷过短. 需要 %zu, 收到 %d\n",
               MSG_ID_VL_ALL_SETTINGS_READ, sizeof(Payload_VL_Read_All_Settings_Req_0x000200_t), header->len);
        handle_generic_ack_message_ex(data, MSG_ID_ACK_VL_ALL_SETTINGS_READ, 0x0001);  // 发送失败ACK
        return;
    }

    Payload_VL_Read_All_Settings_Req_0x000200_t *payload = (Payload_VL_Read_All_Settings_Req_0x000200_t *)header->payload;

    printf("--- 处理可见光配置读取请求 (0x%06X) ---\n", MSG_ID_VL_ALL_SETTINGS_READ);
    printf("  读取命令: 0x%02X\n", payload->read_all_cmd);

    // 创建ACK响应载荷
    Ack_VL_Read_All_Settings_0x000200_t vl_settings = {0};

    // 获取当前可见光相机配置
    int ret = get_vl_camera_settings(&vl_settings);

    if (ret != 0) {
        vl_settings.response_code = 0x0001;  // 失败
        printf("  获取可见光相机配置失败\n");
    } else {
        printf("  获取可见光相机配置成功:\n");
        printf("    拍照分辨率: 0x%02X\n", vl_settings.photo_resolution);
        printf("    录像分辨率: 0x%02X\n", vl_settings.video_resolution);
        printf("    录像码率: 0x%02X\n", vl_settings.video_bitrate);
        printf("    白平衡: 0x%02X\n", vl_settings.white_balance);
        printf("    EV值: 0x%02X\n", vl_settings.ev_value);
        printf("    ISO设置: 0x%02X\n", vl_settings.iso_setting);
        printf("    快门速度: 0x%02X\n", vl_settings.shutter_speed);
        printf("    变焦微调: %d\n", vl_settings.zoom_finetune_value);
        printf("    背光补偿: 0x%02X (开关:%s, 值:%d)\n",
               vl_settings.backlight_compensation,
               (vl_settings.backlight_compensation & 0x80) ? "开" : "关",
               vl_settings.backlight_compensation & 0x7F);
        printf("    强光抑制: 0x%02X (开关:%s, 值:%d)\n",
               vl_settings.strong_light_suppression,
               (vl_settings.strong_light_suppression & 0x80) ? "开" : "关",
               vl_settings.strong_light_suppression & 0x7F);
        printf("    AE锁定: 0x%02X (%s)\n", vl_settings.ae_lock_feedback,
               vl_settings.ae_lock_feedback == 0x01 ? "开" : "关");
        printf("    OSD水印: 0x%04X\n", vl_settings.osd_watermark_switch);
        printf("    抗闪烁: 0x%02X\n", vl_settings.anti_flicker_status);
        printf("    测光模式: 0x%02X\n", vl_settings.metering_mode_feedback);
    }

    // 使用新的自定义ACK函数发送响应
    uint8_t tx_buffer[256] = {0};
    int frame_len = package_custom_ack(
        header->src_sys_id,         // 目标是原始指令的发送者
        header->src_comp_id,        // 目标组件ID
        header->dest_sys_id,        // 本机系统ID
        header->dest_comp_id,       // 本机组件ID
        get_next_send_sequence(),   // 序列号
        MSG_ID_ACK_VL_ALL_SETTINGS_READ,  // ACK消息ID
        &vl_settings,               // 可见光配置ACK载荷
        sizeof(Ack_VL_Read_All_Settings_0x000200_t),  // 载荷长度
        tx_buffer,
        sizeof(tx_buffer)
    );

    if (frame_len > 0) {
        send_fpv_message((char*)tx_buffer, frame_len);
        printf("  可见光配置读取ACK发送成功, 长度: %d bytes\n", frame_len);
    } else {
        printf("  错误: 可见光配置读取ACK封装失败\n");
    }

    printf("---------------------------------------------\n");
}

// 为其他简单的可见光ACK创建回调
void handle_ack_vis_video_res_set(const char *data) { // 对应你的 MSG_ID_VIS_ZOOM_SET 的 ACK
    handle_generic_ack_message(data, MSG_ID_ACK_VIS_VIDEO_RES_SET);
}
void handle_ack_vis_photo_res_set(const char *data) { // 对应你的 MSG_ID_VIS_EZOOM_SET 的 ACK
    handle_generic_ack_message(data, MSG_ID_ACK_VIS_PHOTO_RES_SET);
}
void handle_ack_vis_iso_set(const char *data) { // 对应你的 MSG_ID_VIS_AE_MODE_SET 的 ACK
    handle_generic_ack_message(data, MSG_ID_ACK_VIS_ISO_SET);
}
void handle_ack_vis_shutter_set(const char *data) { // 对应你的 MSG_ID_VIS_FOCUS_MODE_SET 的 ACK
    handle_generic_ack_message(data, MSG_ID_ACK_VIS_SHUTTER_SET);
}
void handle_ack_vis_ev_set(const char *data) { // 对应你的 MSG_ID_VIS_IRCUT_SET 的 ACK
    handle_generic_ack_message(data, MSG_ID_ACK_VIS_EV_SET);
}
void handle_ack_vis_wb_set(const char *data) { // 对应你的 MSG_ID_VIS_WHITEBALANCE_SET 的 ACK
    handle_generic_ack_message(data, MSG_ID_ACK_VIS_WB_SET);
}
void handle_ack_vis_antiflicker_set(const char *data) { // 对应你的 MSG_ID_VIS_ANTI_FLICKER_SET 的 ACK
     handle_generic_ack_message(data, MSG_ID_ACK_VIS_ANTIFLICKER_SET);
}
void handle_ack_vis_strong_light_sup_set(const char *data) { // 对应你下一个ID（如MSG_ID_VIS_BLC_SET）的基础ID的ACK，假设用于强光抑制
     handle_generic_ack_message(data, MSG_ID_ACK_VIS_STRONG_LIGHT_SUP_SET);
}
void handle_ack_vis_blc_set(const char *data) { // 对应你的 MSG_ID_VIS_BLC_SET 的 ACK
     handle_generic_ack_message(data, MSG_ID_ACK_VIS_BLC_SET);
}
void handle_ack_vis_ae_lock_set(const char *data) { // 对应你的 MSG_ID_VIS_AE_LOCK_SET 的 ACK
    handle_generic_ack_message(data, MSG_ID_ACK_VIS_AE_LOCK_SET);
}
void handle_ack_vis_metering_set(const char *data) { // 对应你的 MSG_ID_VIS_METERING_SET 的 ACK
    handle_generic_ack_message(data, MSG_ID_ACK_VIS_METERING_SET);
}






/**
 * @brief 处理拍照/录像模式设置ACK (MSG_ID_ACK_CAM_MODE_SET 0x010300)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_cam_mode_set(const char *data) 
{
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_Photo_Video_Mode_Set_Req_0x000300_t *payload = (Payload_Photo_Video_Mode_Set_Req_0x000300_t *)header->payload;
    //printf("payload->mode_setting : %d\n", payload->mode_setting);
    setting_cam_snap_record_mode(payload->mode_setting);
    handle_generic_ack_message_ex(data, MSG_ID_ACK_CAM_MODE_SET,0);
}

/**
 * @brief 处理拍照参数设置ACK (MSG_ID_ACK_CAM_SHOOT_PARAM_SET 0x010301)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_cam_shoot_param_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_CAM_SHOOT_PARAM_SET);
}

/**
 * @brief 处理拍照指令ACK (MSG_ID_ACK_CAM_SHOOT 0x010302)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_cam_shoot(const char *data) {

    //send_fcs_reboot_example();
    //send_fcs_heartbeat_v1_example();
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(StatusFrame_Photo_Cmd_0x000302_t)) {
        printf("Error: Camera Shoot ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_CAM_SHOOT, sizeof(StatusFrame_Photo_Cmd_0x000302_t), header->len);
        return;
    }

    Payload_Photo_Cmd_Req_0x000302_t *payload = (Payload_Photo_Cmd_Req_0x000302_t *)header->payload;

    printf("payload.photo_mode : %d\n", payload->photo_mode);
    printf("payload.photo_command : %d\n", payload->photo_command);
    printf("payload.folder_name : %s\n", payload->folder_name);
    printf("payload.image_name : %s\n", payload->image_name);

    snap_photo_take_once();

    handle_generic_ack_message_ex(data,MSG_ID_ACK_CAM_SHOOT, 0);


    // printf("--- Received ACK for Camera Shoot (0x%06X) ---\n", MSG_ID_ACK_CAM_SHOOT);
    // printf("  Photo Mode: %d\n", payload->photo_mode_status);
    // printf("  Current Status: %d\n", payload->current_photo_feedback);
    // printf("  Burst Count: %d\n", payload->burst_count_feedback);
    // printf("--------------------------------------------\n");


    StatusFrame_Photo_Cmd_0x000302_t photo_status_payload = {0};
    //perform_actual_photo_taking(payload, &photo_status_payload);
    uint8_t status_tx_buffer[256] = {0}; // 状态帧可能比ACK大
    uint8_t status_seq = get_next_send_sequence(); // 新的序列号

    photo_status_payload.photo_mode_status = payload->photo_mode;
    photo_status_payload.current_photo_feedback = 0;
    photo_status_payload.burst_count_feedback = 1;

    printf("  准备发送拍照状态帧 (0x%06X) 给 SysID:0x%02X\n",
           MSG_ID_STATE_CAM_SHOOT, header->src_sys_id);

    int status_frame_len = package_state_message(
        header->src_sys_id,    // 目标是原始指令的发送者
        header->src_comp_id,
        SYS_ID_GIMBAL,                    // 本机ID
        COMP_ID_GIMBAL,
        status_seq,
        MSG_ID_STATE_CAM_SHOOT,        // 拍照的状态帧ID
        &photo_status_payload,
        sizeof(StatusFrame_Photo_Cmd_0x000302_t),
        status_tx_buffer,
        sizeof(status_tx_buffer)
    );



    if (status_frame_len > 0) {
        send_fpv_message((char*)status_tx_buffer, status_frame_len); // 实际发送
        printf("  模拟发送状态帧数据: "); for(int i=0; i<status_frame_len; ++i) printf("%02X ", status_tx_buffer[i]); printf("\n");
    } else {
        printf("错误: 拍照状态帧 (0x%06X) 封装失败.\n", MSG_ID_STATE_CAM_SHOOT);
    }

}

/**
 * @brief 处理录像指令ACK (MSG_ID_ACK_CAM_RECORD 0x010303)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_cam_record(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_Video_Cmd_Req_0x000303_t *payload = (Payload_Video_Cmd_Req_0x000303_t *)header->payload;

    LOG_D("--- Received Req for Camera Record (0x%06X) ---\n", MSG_ID_CAM_RECORD);
    LOG_D("  Video Mode: %d\n", payload->video_mode);
    LOG_D("  Current Status: %d\n", payload->video_command);
    LOG_D("  folder_name: %s \n", payload->folder_name);
    LOG_D("  video_name: %s \n", payload->video_name);
    LOG_D("----------------------------------------------\n");
    if(payload->video_command == 1)
    {
        LOG_D("start video record\n");
        start_video_record_with_mode(payload->video_mode);
    }else{
        LOG_D("stop video record\n");
        stop_video_record();
    }
    
    handle_generic_ack_message_ex(data, MSG_ID_ACK_CAM_RECORD, 0);
}

/**
 * @brief 处理指定混合变倍ACK (MSG_ID_ACK_ZOOM_SPECIFIC 0x010304)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_zoom_specific(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_Specify_Hybrid_Zoom_Req_0x000304_t *payload = (Payload_Specify_Hybrid_Zoom_Req_0x000304_t *)header->payload;
    
    set_zoom_factor(payload->hybrid_zoom_ratio*10);

    handle_generic_ack_message_ex(data, MSG_ID_ACK_ZOOM_SPECIFIC, 0);
    printf("--- Received ACK for Zoom Specific (0x%06X) ---payload->hybrid_zoom_ratio:%d\n", MSG_ID_ACK_ZOOM_SPECIFIC, payload->hybrid_zoom_ratio);
    printf("  Zoom Status: %s\n", payload->hybrid_zoom_ratio == 0x01 ? "Zooming" : "Completed");
    printf("---------------------------------------------\n");

    //MSG_ID_STATE_ZOOM_SPECIFIC
    //handle_generic_ack_message_ex(data, MSG_ID_ACK_ZOOM_CONTINUOUS, 0);
}

/**
 * @brief 处理连续混合变倍ACK (MSG_ID_ACK_ZOOM_CONTINUOUS 0x010306)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_zoom_continuous(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Payload_Continuous_Hybrid_Zoom_Req_0x000306_t)) {
        printf("Error: Zoom Continuous ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_ZOOM_CONTINUOUS, sizeof(Payload_Continuous_Hybrid_Zoom_Req_0x000306_t), header->len);
        
        handle_generic_ack_message_ex(data, MSG_ID_ACK_ZOOM_CONTINUOUS, 1);
        return;
    }
    
    Payload_Continuous_Hybrid_Zoom_Req_0x000306_t *payload = (Payload_Continuous_Hybrid_Zoom_Req_0x000306_t *)header->payload;
    if (payload->zoom_control == 0x00)
    {
        //zoom in
        vl_start_continuous_zoom(1);
    }
    else if (payload->zoom_control == 0x01)
    {
        //zoom out
        vl_start_continuous_zoom(2);
    }
    else if (payload->zoom_control == 0x02)
    {
        //zoom stop
        vl_stop_continuous_zoom();
    }
    else if (payload->zoom_control == 0x03)
    {
        //zoom in  single step
        vl_continuous_zoom_single_step(1);
    }
    else if (payload->zoom_control == 0x04)
    {
        //zoom out single step
        vl_continuous_zoom_single_step(2);
    }
    
    handle_generic_ack_message_ex(data, MSG_ID_ACK_ZOOM_CONTINUOUS, 0);
}

/**
 * @brief 处理精准复拍ACK (MSG_ID_ACK_PRECISE_RESHOOT 0x010307)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_precise_reshoot(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(StatusFrame_Precise_Refocus_Photo_0x000307_t)) {
        printf("Error: Precise Reshoot ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_PRECISE_RESHOOT, sizeof(StatusFrame_Precise_Refocus_Photo_0x000307_t), header->len);
        return;
    }

    StatusFrame_Precise_Refocus_Photo_0x000307_t *payload = (StatusFrame_Precise_Refocus_Photo_0x000307_t *)header->payload;

    printf("--- Received ACK for Precise Reshoot (0x%06X) ---\n", MSG_ID_ACK_PRECISE_RESHOOT);
    printf("  Photo Mode: %d\n", payload->photo_mode_status);
    printf("  Current Status: %d\n", payload->current_photo_feedback);
    printf("------------------------------------------------\n");
}

/**
 * @brief 处理码率设置ACK (MSG_ID_ACK_BITRATE_SET 0x010308)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_bitrate_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_BITRATE_SET);
}

/**
 * @brief 处理分辨率设置ACK (MSG_ID_ACK_RESOLUTION_SET 0x01030A)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_resolution_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_RESOLUTION_SET);
}

/**
 * @brief 处理编码格式设置ACK (MSG_ID_ACK_ENCODE_FORMAT_SET 0x01030B)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_encode_format_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_ENCODE_FORMAT_SET);
}

/**
 * @brief 处理TF卡升级ACK (MSG_ID_ACK_CAM_UPGRADE 0x01030C)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_cam_upgrade(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(StatusFrame_TF_Upgrade_0x00030C_t)) {
        printf("Error: Camera Upgrade ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_CAM_UPGRADE, sizeof(StatusFrame_TF_Upgrade_0x00030C_t), header->len);
        return;
    }

    StatusFrame_TF_Upgrade_0x00030C_t *payload = (StatusFrame_TF_Upgrade_0x00030C_t *)header->payload;

    printf("--- Received ACK for Camera Upgrade (0x%06X) ---\n", MSG_ID_ACK_CAM_UPGRADE);
    printf("  Upgrade Status: %d\n", payload->upgrade_status);
    printf("-----------------------------------------------\n");
}

/**
 * @brief 处理TF卡格式化ACK (MSG_ID_ACK_CAM_FORMAT 0x01030D)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_cam_format(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(StatusFrame_TF_Format_0x00030D_t)) {
        printf("Error: Camera Format ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_CAM_FORMAT, sizeof(StatusFrame_TF_Format_0x00030D_t), header->len);
        return;
    }

    StatusFrame_TF_Format_0x00030D_t *payload = (StatusFrame_TF_Format_0x00030D_t *)header->payload;

    printf("--- Received ACK for Camera Format (0x%06X) ---\n", MSG_ID_ACK_CAM_FORMAT);
    printf("  Format Status: %d\n", payload->format_status);
    printf("----------------------------------------------\n");
}

/**
 * @brief 处理授时指令ACK (MSG_ID_ACK_TIME_SYNC 0x01030E)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_time_sync(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Ack_Camera_Time_Sync_0x00030E_t)) {
        printf("Error: Time Sync ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_TIME_SYNC, sizeof(Ack_Camera_Time_Sync_0x00030E_t), header->len);
        return;
    }
    Ack_Camera_Time_Sync_0x00030E_t *payload = (Ack_Camera_Time_Sync_0x00030E_t *)header->payload;
    handle_generic_ack_message_ex(data, MSG_ID_ACK_TIME_SYNC, 0 );
}

/**
 * @brief 处理恢复出厂设置ACK (MSG_ID_ACK_FACTORY_RESET 0x01030F)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_factory_reset(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_FACTORY_RESET);
}

/**
 * @brief 处理请求GPS信息ACK (MSG_ID_ACK_GPS_REQUEST 0x010310)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_gps_request(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Ack_Camera_Req_GPS_From_FC_0x000310_t)) {
        printf("Error: GPS Request ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_GPS_REQUEST, sizeof(Ack_Camera_Req_GPS_From_FC_0x000310_t), header->len);
        return;
    }

    Ack_Camera_Req_GPS_From_FC_0x000310_t *payload = (Ack_Camera_Req_GPS_From_FC_0x000310_t *)header->payload;

    printf("--- Received ACK for GPS Request (0x%06X) ---\n", MSG_ID_ACK_GPS_REQUEST);
    printf("  Response Code: 0x%04X\n", payload->response_code);
    printf("  Time: %02d:%02d:%02d.%03d\n", 
           payload->hour_echo, payload->minute_echo, payload->second_echo, payload->millisecond_echo);
    printf("  Longitude: %.7f\n", (float)payload->longitude / 1e7f);
    printf("  Latitude: %.7f\n", (float)payload->latitude / 1e7f);
    printf("  Relative Altitude: %.1f m\n", (float)payload->relative_altitude / 10.0f);
    printf("  Absolute Altitude: %.1f m\n", (float)payload->absolute_altitude / 10.0f);
    printf("  Plane Attitude: Yaw=%.2f, Roll=%.2f, Pitch=%.2f\n",
           (float)payload->plane_yaw_angle / 100.0f,
           (float)payload->plane_roll_angle / 100.0f,
           (float)payload->plane_pitch_angle / 100.0f);
    printf("  Gimbal Attitude: Yaw=%.2f, Roll=%.2f, Pitch=%.2f\n",
           (float)payload->gimbal_yaw_angle / 100.0f,
           (float)payload->gimbal_roll_angle / 100.0f,
           (float)payload->gimbal_pitch_angle / 100.0f);
    printf("------------------------------------------------\n");
}

/**
 * @brief 处理IP设置ACK (MSG_ID_ACK_IP_SET 0x010311)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_ip_set(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;

    // 检查载荷长度
    if (header->len < sizeof(Payload_Camera_IP_Set_Req_0x000311_t)) {
        printf("错误: IP设置请求 (0x%06X) 载荷过短. 需要 %zu, 收到 %d\n",
               MSG_ID_IP_SET, sizeof(Payload_Camera_IP_Set_Req_0x000311_t), header->len);
        handle_generic_ack_message_ex(data, MSG_ID_ACK_IP_SET, 0x0001);  // 发送失败ACK
        return;
    }

    Payload_Camera_IP_Set_Req_0x000311_t *payload = (Payload_Camera_IP_Set_Req_0x000311_t *)header->payload;

    printf("--- 处理IP设置请求 (0x%06X) ---\n", MSG_ID_IP_SET);
    printf("  IP类型: %s\n", payload->ip_type == 0 ? "静态" : "动态");
    printf("  IP地址: %d.%d.%d.%d\n",
           payload->ip_address[0], payload->ip_address[1],
           payload->ip_address[2], payload->ip_address[3]);
    printf("  子网掩码: %d.%d.%d.%d\n",
           payload->subnet_mask[0], payload->subnet_mask[1],
           payload->subnet_mask[2], payload->subnet_mask[3]);
    printf("  默认网关: %d.%d.%d.%d\n",
           payload->default_gateway[0], payload->default_gateway[1],
           payload->default_gateway[2], payload->default_gateway[3]);

    uint16_t response_code = 0x0000;  // 默认成功

    // 只处理静态IP设置，动态IP暂不支持
    if (payload->ip_type == 0) {  // 静态IP
        // 调用设置网络信息函数
        int ret = set_device_network_info(payload->ip_address,
                                         payload->subnet_mask,
                                         payload->default_gateway);

        if (ret == 0) {
            printf("  网络配置设置成功\n");
            response_code = 0x0000;  // 成功

            // 可选：提示用户重启以使配置生效
            printf("  注意: 网络配置已保存，重启后生效\n");
        } else {
            printf("  网络配置设置失败\n");
            response_code = 0x0001;  // 失败
        }
    } else {
        printf("  动态IP模式暂不支持\n");
        response_code = 0x0002;  // 不支持的功能
    }

    // 发送ACK响应
    handle_generic_ack_message_ex(data, MSG_ID_ACK_IP_SET, response_code);

    printf("  IP设置处理完成，响应码: 0x%04X\n", response_code);
    printf("----------------------------------------\n");
}

/**
 * @brief 从fw_printenv获取网络参数值
 * @param param_name 参数名称 (如 "ipaddr", "netmask", "gatewayip")
 * @param value 输出参数值字符串
 * @param value_size 输出缓冲区大小
 * @return 0:成功, -1:失败
 */
static int get_fw_env_param(const char *param_name, char *value, size_t value_size) {
    FILE *fp;
    char line[256];
    char cmd[128];
    int found = 0;

    // 构造命令：fw_printenv | grep "param_name="
    snprintf(cmd, sizeof(cmd), "fw_printenv | grep '^%s='", param_name);

    fp = popen(cmd, "r");
    if (fp != NULL) {
        if (fgets(line, sizeof(line), fp)) {
            // 移除换行符
            line[strcspn(line, "\n")] = 0;

            // 查找等号位置
            char *equal_pos = strchr(line, '=');
            if (equal_pos && strlen(equal_pos + 1) > 0) {
                strncpy(value, equal_pos + 1, value_size - 1);
                value[value_size - 1] = '\0';
                found = 1;
            }
        }
        pclose(fp);
    }

    return found ? 0 : -1;
}

/**
 * @brief 设置fw_env网络参数
 * @param param_name 参数名称
 * @param value 参数值
 * @return 0:成功, -1:失败
 */
static int set_fw_env_param(const char *param_name, const char *value) {
    char cmd[256];
    int ret;

    // 构造fw_setenv命令
    snprintf(cmd, sizeof(cmd), "fw_setenv %s %s", param_name, value);

    printf("执行命令: %s\n", cmd);
    ret = system(cmd);

    return (ret == 0) ? 0 : -1;
}

/**
 * @brief 获取当前设备的网络配置信息 (使用fw_printenv)
 * @param ip_addr 输出IP地址数组 (4字节)
 * @param subnet_mask 输出子网掩码数组 (4字节)
 * @param gateway 输出默认网关数组 (4字节)
 * @return 0:成功, -1:失败
 */
static int get_device_network_info(uint8_t ip_addr[4], uint8_t subnet_mask[4], uint8_t gateway[4]) {
    char ip_str[32] = {0};
    char mask_str[32] = {0};
    char gw_str[32] = {0};
    int success_count = 0;

    // 默认值设置
    memset(ip_addr, 0, 4);
    memset(subnet_mask, 0, 4);
    memset(gateway, 0, 4);

    // 1. 获取IP地址
    if (get_fw_env_param("ipaddr", ip_str, sizeof(ip_str)) == 0) {
        int ip1, ip2, ip3, ip4;
        if (sscanf(ip_str, "%d.%d.%d.%d", &ip1, &ip2, &ip3, &ip4) == 4) {
            ip_addr[0] = (uint8_t)ip1;
            ip_addr[1] = (uint8_t)ip2;
            ip_addr[2] = (uint8_t)ip3;
            ip_addr[3] = (uint8_t)ip4;
            success_count++;
            printf("获取IP地址: %s\n", ip_str);
        }
    }

    // 2. 获取子网掩码
    if (get_fw_env_param("netmask", mask_str, sizeof(mask_str)) == 0) {
        int mask1, mask2, mask3, mask4;
        if (sscanf(mask_str, "%d.%d.%d.%d", &mask1, &mask2, &mask3, &mask4) == 4) {
            subnet_mask[0] = (uint8_t)mask1;
            subnet_mask[1] = (uint8_t)mask2;
            subnet_mask[2] = (uint8_t)mask3;
            subnet_mask[3] = (uint8_t)mask4;
            success_count++;
            printf("获取子网掩码: %s\n", mask_str);
        }
    }

    // 3. 获取网关
    if (get_fw_env_param("gatewayip", gw_str, sizeof(gw_str)) == 0) {
        int gw1, gw2, gw3, gw4;
        if (sscanf(gw_str, "%d.%d.%d.%d", &gw1, &gw2, &gw3, &gw4) == 4) {
            gateway[0] = (uint8_t)gw1;
            gateway[1] = (uint8_t)gw2;
            gateway[2] = (uint8_t)gw3;
            gateway[3] = (uint8_t)gw4;
            success_count++;
            printf("获取网关: %s\n", gw_str);
        }
    }

    return (success_count >= 2) ? 0 : -1;  // 至少获取到2个参数才算成功
}

/**
 * @brief 设置设备的网络配置信息 (使用fw_setenv)
 * @param ip_addr IP地址数组 (4字节)
 * @param subnet_mask 子网掩码数组 (4字节)
 * @param gateway 默认网关数组 (4字节)
 * @return 0:成功, -1:失败
 */
int set_device_network_info(const uint8_t ip_addr[4], const uint8_t subnet_mask[4], const uint8_t gateway[4]) {
    char ip_str[32];
    char mask_str[32];
    char gw_str[32];
    int success_count = 0;

    // 1. 设置IP地址
    snprintf(ip_str, sizeof(ip_str), "%d.%d.%d.%d",
             ip_addr[0], ip_addr[1], ip_addr[2], ip_addr[3]);
    if (set_fw_env_param("ipaddr", ip_str) == 0) {
        success_count++;
        printf("设置IP地址成功: %s\n", ip_str);
    } else {
        printf("设置IP地址失败: %s\n", ip_str);
    }

    // 2. 设置子网掩码
    snprintf(mask_str, sizeof(mask_str), "%d.%d.%d.%d",
             subnet_mask[0], subnet_mask[1], subnet_mask[2], subnet_mask[3]);
    if (set_fw_env_param("netmask", mask_str) == 0) {
        success_count++;
        printf("设置子网掩码成功: %s\n", mask_str);
    } else {
        printf("设置子网掩码失败: %s\n", mask_str);
    }

    // 3. 设置网关
    snprintf(gw_str, sizeof(gw_str), "%d.%d.%d.%d",
             gateway[0], gateway[1], gateway[2], gateway[3]);
    if (set_fw_env_param("gatewayip", gw_str) == 0) {
        success_count++;
        printf("设置网关成功: %s\n", gw_str);
    } else {
        printf("设置网关失败: %s\n", gw_str);
    }

    return (success_count == 3) ? 0 : -1;  // 所有参数都设置成功才算成功
}

/**
 * @brief 处理IP获取ACK (MSG_ID_ACK_IP_GET 0x010312)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_ip_get(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_Camera_IP_Get_Req_0x000312_t *payload = (Payload_Camera_IP_Get_Req_0x000312_t *)header->payload;

    printf("--- 处理IP获取请求 (0x%06X) ---\n", MSG_ID_IP_GET);
    printf("  查询命令: %d\n", payload->query_ip_cmd);

    // 创建ACK响应载荷
    Ack_Camera_IP_Get_0x000312_t ack_payload = {0};

    // 获取当前设备的网络信息
    int ret = get_device_network_info(ack_payload.ip_address,
                                     ack_payload.subnet_mask,
                                     ack_payload.default_gateway);

    if (ret == 0) {
        ack_payload.response_code = 0x0000;  // 成功
        printf("  获取网络信息成功:\n");
        printf("    IP地址: %d.%d.%d.%d\n",
               ack_payload.ip_address[0], ack_payload.ip_address[1],
               ack_payload.ip_address[2], ack_payload.ip_address[3]);
        printf("    子网掩码: %d.%d.%d.%d\n",
               ack_payload.subnet_mask[0], ack_payload.subnet_mask[1],
               ack_payload.subnet_mask[2], ack_payload.subnet_mask[3]);
        printf("    默认网关: %d.%d.%d.%d\n",
               ack_payload.default_gateway[0], ack_payload.default_gateway[1],
               ack_payload.default_gateway[2], ack_payload.default_gateway[3]);
    } else {
        ack_payload.response_code = 0x0001;  // 失败
        printf("  获取网络信息失败\n");
    }

    // 发送ACK响应
    uint8_t tx_buffer[256] = {0};
    int frame_len = package_custom_ack(
        header->src_sys_id,         // 目标是原始指令的发送者
        header->src_comp_id,        // 目标组件ID
        header->dest_sys_id,        // 本机系统ID
        header->dest_comp_id,       // 本机组件ID
        get_next_send_sequence(),   // 序列号
        MSG_ID_ACK_IP_GET,          // ACK消息ID
        &ack_payload,               // ACK载荷
        sizeof(Ack_Camera_IP_Get_0x000312_t),
        tx_buffer,
        sizeof(tx_buffer)
    );

    if (frame_len > 0) {
        send_fpv_message((char*)tx_buffer, frame_len);
        printf("  IP获取ACK发送成功, 长度: %d bytes\n", frame_len);
    } else {
        printf("  错误: IP获取ACK封装失败\n");
    }

    printf("----------------------------------------\n");
}

/**
 * @brief 处理调焦指令ACK (MSG_ID_ACK_FOCUS_SET 0x010313)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_focus_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_FOCUS_SET);
}

/**
 * @brief 处理OSD开关ACK (MSG_ID_ACK_OSD_SWITCH 0x010314)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_osd_switch(const char *data) {

    Payload_OSD_Watermark_Switch_Req_0x000314_t *payload = (Payload_OSD_Watermark_Switch_Req_0x000314_t *)data;
    set_watermark_switch(payload->watermark_switch);
    handle_generic_ack_message_ex(data, MSG_ID_ACK_OSD_SWITCH, 0);
}

/**
 * @brief 处理OSD文案设置ACK (MSG_ID_ACK_OSD_TEXT_SET 0x010315)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_osd_text_set(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_OSD_TEXT_SET);
}

/**
 * @brief 处理相机关机ACK (MSG_ID_ACK_CAM_SHUTDOWN 0x010316)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_cam_shutdown(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_CAM_SHUTDOWN);
}

/**
 * @brief 处理获取相机版本号ACK (MSG_ID_ACK_CAM_VERSION_REQ 0x010317)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_cam_version_req(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Ack_Camera_Get_Version_0x000317_t)) {
        printf("Error: Camera Version ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_CAM_VERSION_REQ, sizeof(Ack_Camera_Get_Version_0x000317_t), header->len);
        return;
    }

    Ack_Camera_Get_Version_0x000317_t *payload = (Ack_Camera_Get_Version_0x000317_t *)header->payload;

    printf("--- Received ACK for Camera Version (0x%06X) ---\n", MSG_ID_ACK_CAM_VERSION_REQ);
    printf("  Response Code: 0x%04X\n", payload->response_code);
    printf("  Version: %d.%d.%d\n", 
           payload->major_version, payload->minor_version, payload->patch_version);
    printf("-----------------------------------------------\n");
}

/**
 * @brief 处理图像模式设置ACK (MSG_ID_ACK_IMAGE_MODE_SET 0x010318)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_image_mode_set(const char *data) {

    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Payload_Image_Mode_Set_Req_0x000318_t)) {
        printf("Error: Zoom Continuous ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_ZOOM_CONTINUOUS, sizeof(Payload_Image_Mode_Set_Req_0x000318_t), header->len);
        
        handle_generic_ack_message_ex(data, MSG_ID_ACK_ZOOM_CONTINUOUS, 1);
        return;
    }
    
    Payload_Image_Mode_Set_Req_0x000318_t *payload = (Payload_Image_Mode_Set_Req_0x000318_t *)header->payload;
    printf("--- Received ACK for Image Mode Set (0x%06X) ---\n", MSG_ID_ACK_IMAGE_MODE_SET);
    handle_set_vo_mode(payload->image_mode);

    //Ack_Camera_Set_Image_Mode_0x010318_t *payload = (Ack_Camera_Set_Image_Mode_0x010318_t *)data;
    handle_generic_ack_message_ex(data, MSG_ID_ACK_IMAGE_MODE_SET, 0);
}

/**
 * @brief 处理AI识别ACK (MSG_ID_ACK_AI_DETECTION 0x010319)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_ai_detection(const char *data) {
    handle_generic_ack_message(data, MSG_ID_ACK_AI_DETECTION);
}

/**
 * @brief 处理AI识别状态帧 (MSG_ID_STATE_AI_DETECTION 0x020319)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_state_ai_detection(StatusFrame_AI_Recognition_0x000319_t *payload) {

    //StatusFrame_AI_Recognition_0x000319_t *payload = (StatusFrame_AI_Recognition_0x000319_t *)data;

    // printf("--- Received AI Detection State (0x%06X) ---\n", MSG_ID_STATE_AI_DETECTION);
    // printf("  Detection Status: %s\n", payload->detect_status == 0x00 ? "Success" : "No Detection");
    
    // // 解析目标数据 (这里简化处理，实际应根据packet_info处理分包)
    // for (int i = 0; i < MAX_AI_TARGETS_PER_PACKET; i++) {
    //     AI_Target_Data_t *target = &payload->targets[i];
    //     printf("  Target %d: Class=%d, Confidence=%d%%, Rect=(%d,%d,%d,%d)\n",
    //            i+1, target->class_id, target->confidence,
    //            target->top_left_x, target->top_left_y, 
    //            target->width, target->height);
    // }
    // printf("--------------------------------------------\n");

        // 在这里根据云台姿态信息执行相应的逻辑
    uint8_t status_tx_buffer[256] = {0}; // 状态帧可能比ACK大
    uint8_t status_seq = get_next_send_sequence(); // 新的序列号


    //printf("  准备发送云台姿态帧 (0x%06X) \n", MSG_ID_STATE_AI_DETECTION);


    int status_frame_len = package_state_message(
        SYS_ID_FC,                          // 目标是原始指令的发送者
        COMP_ID_FC,
        SYS_ID_GIMBAL,                         // 本机ID
        COMP_ID_GIMBAL,
        status_seq,
        MSG_ID_STATE_AI_DETECTION,             // 云台姿态帧
        payload,
        sizeof(Payload_Gimbal_Attitude_0x000002_t),
        status_tx_buffer,
        sizeof(status_tx_buffer)
    );

    if (status_frame_len > 0) {
        send_fpv_message((char*)status_tx_buffer, status_frame_len); // 实际发送
        //printf("  模拟发送状态帧数据: "); for(int i=0; i<status_frame_len; ++i) printf("%02X ", status_tx_buffer[i]); printf("\n");
    } else {
        printf("错误: 检测状态帧 (0x%06X) 封装失败.\n", MSG_ID_STATE_AI_DETECTION);
    }

}

/**
 * @brief 处理目标GPS请求ACK (MSG_ID_ACK_TARGET_GPS_REQ 0x010320)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_target_gps_req(const char *data) {
    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    if (header->len < sizeof(Ack_FC_Req_Target_GPS_0x000320_t)) {
        printf("Error: Target GPS Request ACK (0x%06X) payload too short. Expected %zu, got %d\n",
               MSG_ID_ACK_TARGET_GPS_REQ, sizeof(Ack_FC_Req_Target_GPS_0x000320_t), header->len);
        return;
    }

    Ack_FC_Req_Target_GPS_0x000320_t *payload = (Ack_FC_Req_Target_GPS_0x000320_t *)header->payload;

    printf("--- Received ACK for Target GPS Request (0x%06X) ---\n", MSG_ID_ACK_TARGET_GPS_REQ);
    printf("  Response Code: 0x%04X\n", payload->response_code);
    printf("  Timestamp: %llu ms\n", payload->utc_timestamp_ms);
    printf("  Longitude: %.7f\n", (float)payload->longitude_deg_e7 / 1e7f);
    printf("  Latitude: %.7f\n", (float)payload->latitude_deg_e7 / 1e7f);
    printf("  Relative Altitude: %.1f m\n", (float)payload->relative_alt_mm / 1000.0f);
    printf("  Absolute Altitude: %.1f m\n", (float)payload->absolute_alt_mm / 1000.0f);
    printf("---------------------------------------------------\n");
}

/**
 * @brief 处理AI追踪ACK (MSG_ID_ACK_AI_TRACKING 0x010324)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ack_ai_tracking(const char *data) {

    K40T_Protocol_Frame_Header_t *header = (K40T_Protocol_Frame_Header_t *)data;
    Payload_Box_Select_Track_Req_0x000324_t *payload = (Payload_Box_Select_Track_Req_0x000324_t *)header->payload;
    printf("handle_ack_ai_tracking: target_box_x = %d, target_box_y = %d, target_box_w = %d, target_box_h = %d, tracking_switch = %d\n",
           payload->target_box_x, payload->target_box_y, payload->target_box_w, payload->target_box_h, payload->tracking_switch);
    if (payload->tracking_switch == 0x01)//enable
    {
        printf("--- Received ACK for AI Tracking (0x%06X) ---\n", MSG_ID_AI_TRACKING);
        printf("  Tracking Status: %s\n", payload->tracking_switch == 0x01 ? "Enable" : "Disable");
        handle_start_track_cmd(payload->target_box_x,
                                payload->target_box_y,
                                payload->target_box_w,
                                payload->target_box_h);

    }
    else if (payload->tracking_switch == 0x02)//closed
    {
        handle_stop_track_cmd();
    }
    else
    {
        handle_generic_ack_message_ex(data, MSG_ID_ACK_AI_TRACKING, 1);
        return;
    }

    //todo postion is invalid
    handle_generic_ack_message_ex(data, MSG_ID_ACK_AI_TRACKING, 0);

   
}

/**
 * @brief 处理AI追踪状态帧 (MSG_ID_STATE_AI_TRACKING 0x020324)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_state_ai_tracking(  uint16_t top_left_x,  // 左上角X
                                uint16_t top_left_y,  // 左上角Y
                                uint16_t width,       // 宽
                                uint16_t height,      // 高
                                uint8_t  confidence,  // 目标置信度 (conf*100)
                                uint8_t  class_id    // 类别id
    ) {

    printf("  Confidence=%d%%, Rect=(%d,%d,%d,%d)\n",
           confidence,
           top_left_x, top_left_y,
           width, height);
    printf("------------------------------------------------\n");
    
    StatusFrame_Box_Select_Track_0x000324_t payload = {0};
    uint8_t status_tx_buffer[256] = {0}; // 状态帧可能比ACK大
    uint8_t status_seq = get_next_send_sequence(); // 新的序列号

    payload.detect_status = 0;
    payload.packet_info = 0x11;
    payload.target_data.top_left_x = top_left_x;
    payload.target_data.top_left_y = top_left_y;
    payload.target_data.width = width;
    payload.target_data.height = height;
    payload.target_data.confidence = confidence;
    payload.target_data.class_id = class_id;

    int status_frame_len = package_state_message(
        SYS_ID_FC,                      // 目标是原始指令的发送者
        COMP_ID_FC,
        SYS_ID_GIMBAL,                     // 本机ID
        COMP_ID_GIMBAL,
        status_seq,
        MSG_ID_STATE_AI_TRACKING,         // 拍照的状态帧ID
        &payload,
        sizeof(StatusFrame_Box_Select_Track_0x000324_t),
        status_tx_buffer,
        sizeof(status_tx_buffer)
    );



    if (status_frame_len > 0) {
        send_fpv_message((char*)status_tx_buffer, status_frame_len); // 实际发送
        //printf("  模拟发送状态帧数据: "); for(int i=0; i<status_frame_len; ++i) printf("%02X ", status_tx_buffer[i]); printf("\n");
    } else {
        printf("错误: 跟踪状态帧 (0x%06X) 封装失败.\n", MSG_ID_STATE_CAM_SHOOT);
    }

}



void start_fc()
{
    //start serial message proc
    start_fpv_message_proc("/dev/ttyAMA0");


    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_CONTROL, handle_ack_gimbal_control);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_ANGLE_SET, handle_ack_gimbal_angle_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_CALIBRATE, handle_ack_gimbal_calibrate);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_SPEED_SET, handle_ack_gimbal_speed_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_POINT_ALIGN, handle_ack_gimbal_point_align);
    
    // For Get Version, the ACK message ID is 0x010018
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_VERSION_REQ, handle_ack_gimbal_version_req);
    
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_SERVO_OFF, handle_ack_gimbal_servo_off);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_LINEAR_CAL, handle_ack_gimbal_linear_cal);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_REBOOT, handle_ack_gimbal_reboot);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_FC_ATTITUDE_SET, handle_ack_gimbal_fc_attitude_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_ACC_CAL, handle_ack_gimbal_acc_cal);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_FC_INFO_TO_GIMBAL, handle_ack_fc_info_to_gimbal);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GIMBAL_STABILIZE_SET, handle_ack_gimbal_stabilize_set);





    printf("注册相机周期性状态回调...\n");
    //REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_SYS_STATUS, handle_cam_sys_status);
    //REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_STATUS, handle_ir_status);
    //REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_VISIBLE_STATUS, handle_visible_status);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_UPGRADE_STATUS, handle_cam_upgrade_status);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_PLAYBACK_STATUS, handle_playback_status);




    printf("注册红外相机ACK回调...\n");
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_CONFIG_READ, handle_ack_ir_config_read);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_ZOOM_SET, handle_ack_ir_zoom_set); // 来自你的列表
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_COLORMAP_SET, handle_ack_ir_colormap_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_THERMAL_SWITCH, handle_ack_ir_thermal_switch);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_SHARPEN_SET, handle_ack_ir_sharpen_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_BRIGHTNESS_SET, handle_ack_ir_brightness_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_CONTRAST_SET, handle_ack_ir_contrast_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_DENOISE_SET, handle_ack_ir_denoise_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_ENHANCE_SET, handle_ack_ir_enhance_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_SPOT_THERMAL, handle_ack_ir_spot_thermal);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_AREA_THERMAL, handle_ack_ir_area_thermal);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_GAIN_MODE_SET, handle_ack_ir_gain_mode_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_ALARM_SET, handle_ack_ir_alarm_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_THERMAL_OVERLAY_SWITCH, handle_ack_ir_thermal_overlay_switch);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IR_THRESHOLD_TEMP_DIFF_SET, handle_ack_ir_threshold_temp_diff_set);



    printf("注册可见光相机ACK回调...\n");
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_VL_ALL_SETTINGS_READ, handle_ack_vl_all_settings_read);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_VIDEO_RES_SET, handle_ack_vis_video_res_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_PHOTO_RES_SET, handle_ack_vis_photo_res_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_ISO_SET, handle_ack_vis_iso_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_SHUTTER_SET, handle_ack_vis_shutter_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_EV_SET, handle_ack_vis_ev_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_WB_SET, handle_ack_vis_wb_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_ANTIFLICKER_SET, handle_ack_vis_antiflicker_set);
    // 注意：MSG_ID_ACK_VIS_STRONG_LIGHT_SUP_SET 的基础ID需要与你发送强光抑制命令时使用的ID一致
    // 这里假设 MSG_ID_VIS_ANTI_FLICKER_SET (0x000208) 是强光抑制的基础命令ID (文档3.2.3.9)
    // 而不是你列表中的 MSG_ID_VIS_BLC_SET (0x000209) 用于强光抑制。请根据你的实际命令ID调整。
    // 如果0x000208是强光抑制，则ACK是GEN_ACK_ID(0x000208)
    // 我们使用之前定义的 MSG_ID_ACK_VIS_STRONG_LIGHT_SUP_SET，它是基于你下一个ID (MSG_ID_VIS_BLC_SET) 的前一个ID，
    // 这部分定义可能需要你根据发送的命令ID来精确对应。
    // 为简单起见，我这里将使用你定义的MSG_ID宏，并假设它们的基础ID是正确的。
    // 假设你发送 MSG_ID_VIS_ANTI_FLICKER_SET (0x000208) 作为强光抑制命令
    // #define MSG_ID_ACK_VIS_STRONG_LIGHT_SUP_SET GEN_ACK_ID(0x000208) // 应该这样
    // REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_STRONG_LIGHT_SUP_SET, handle_ack_vis_strong_light_sup_set);
    // 或者如果你有特定的宏：
    // REGISTER_DRONEPOD_MESSAGE_CALLBACK(YOUR_MSG_ID_ACK_FOR_0x000208, handle_ack_vis_strong_light_sup_set);

    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_BLC_SET, handle_ack_vis_blc_set); // 假设对应0x000209的ACK
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_AE_LOCK_SET, handle_ack_vis_ae_lock_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ACK_VIS_METERING_SET, handle_ack_vis_metering_set);




    printf("注册通用指令回调...\n");
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_MODE_SET, handle_ack_cam_mode_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_SHOOT_PARAM_SET, handle_ack_cam_shoot_param_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_SHOOT, handle_ack_cam_shoot);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_RECORD, handle_ack_cam_record);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ZOOM_SPECIFIC, handle_ack_zoom_specific);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ZOOM_CONTINUOUS, handle_ack_zoom_continuous);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_PRECISE_RESHOOT, handle_ack_precise_reshoot);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_BITRATE_SET, handle_ack_bitrate_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_RESOLUTION_SET, handle_ack_resolution_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_ENCODE_FORMAT_SET, handle_ack_encode_format_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_UPGRADE, handle_ack_cam_upgrade);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_FORMAT, handle_ack_cam_format);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_TIME_SYNC, handle_ack_time_sync);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_FACTORY_RESET, handle_ack_factory_reset);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_GPS_REQUEST, handle_ack_gps_request);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IP_SET, handle_ack_ip_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IP_GET, handle_ack_ip_get);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_FOCUS_SET, handle_ack_focus_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_OSD_SWITCH, handle_ack_osd_switch);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_OSD_TEXT_SET, handle_ack_osd_text_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_SHUTDOWN, handle_ack_cam_shutdown);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_CAM_VERSION_REQ, handle_ack_cam_version_req);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_IMAGE_MODE_SET, handle_ack_image_mode_set);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_AI_DETECTION, handle_ack_ai_detection);
    //REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_STATE_AI_DETECTION, handle_state_ai_detection);
    //REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_TARGET_GPS_REQ, handle_state_ai_tracking);
    REGISTER_DRONEPOD_MESSAGE_CALLBACK(MSG_ID_AI_TRACKING, handle_ack_ai_tracking);

}
