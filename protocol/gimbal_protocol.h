#ifndef GIMBAL_PROTOCOL_H_
#define GIMBAL_PROTOCOL_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <string.h> // For memcpy


/**
 * 云台指令集
 */
typedef enum {
    E_GIMBAL_HEARTBEAT = 0,
    E_GIMBAL_LOG = 1,
    E_GIMBAL_BUTT
}E_GIMBAL_CMD_E;



/**
 * 飞控指令集
 *  
 */
typedef enum {
    E_FLIGHTCTRL_HEARTBEAT = 0,
    E_FLIGHTCTRL_BUTT
}E_FLIGHTCTRL_CMD_E;  


/**
 * 相机指令集
 */
typedef enum {
    E_CAM_COMMAND = 1,
    E_CAM_GIMBAL_BUTT
}E_GIMBALCAM_CMD_E;


#pragma pack(push, 1)


/**
 * message head
 */
typedef struct {

    // msg sof
    unsigned char sof;

    // length
    unsigned short ver_len;

    unsigned char checksum;

    unsigned short seq;

    unsigned char sts;

    unsigned char sender;

    unsigned char receiver;

    // 1-byte，指令集，指示当前指令属于哪一类指令集合，其中：
    // 0x00：通用指令集；
    // 0x01：云台指令集；
    // 0x02：飞控指令集；
    // 0xff~0x03：保留；
    unsigned char cmd_set;

    unsigned char cmd_id;

} header_v1_struct;
 

// 根据文档 source [24] 到 source [32] 定义 header_v1_struct
typedef struct {
    uint8_t sof;            // 起始标志 (0xAA)
    struct {
        uint16_t len : 10;  // 帧长度 (整条帧的字节数)
        uint16_t ver : 6;   // 协议版本号 (目前固定为1)
    } ver_len;
    uint8_t check_sum;      // 前面3个字节(SOF, VER_LEN)的和校验
    uint16_t seq;           // 帧的序列号
    struct {
        uint8_t reserved : 4;
        uint8_t encrypt : 1; // 0 - 数据段不加密, 1 - 数据段加密
        uint8_t reserved1 : 3;
    } status;
    struct { 
        uint8_t index : 3;  // 低3位
        uint8_t device : 5; // 高5位
    } sender;
    struct {
        uint8_t index : 3;
        uint8_t device : 5;
    } receiver;
    uint8_t cmd_set;
    uint8_t cmd_id;
} header_v1_t; // 重命名以避免与文档中的 typedef struct { header_v1_struct header; ... } 冲突

// 飞控心跳包 (CMD_SET: 0x02, CMD_ID: 0x00) 的DATA部分
// 根据文档 source [21], [22], [23]
// 飞控心跳包的纯数据字段 (38字节)
typedef struct {
    int16_t fcs_pitch;
    int16_t fcs_roll;
    int16_t fcs_yaw;
    int16_t fcs_yaw_ref;
    int16_t fcs_accx;
    int16_t fcs_accy;
    int16_t fcs_accz;
    uint8_t gb_ctrl_mode;
    int16_t gb_pitch_ref;
    int16_t gb_roll_ref;
    int16_t gb_yaw_ref;
    uint8_t fcs_state;
    int16_t fcs_yaw_w_ref;
    int8_t  fcs_yaw_rc_cmd;
    uint8_t reserve[15];
} fcs_heartbeat_data_fields_t;

// 你修改后的飞控心跳包完整帧结构体
typedef struct {
    header_v1_t header;                 // 11字节
    // --- FCS心跳包特定数据开始 (38字节) ---
    int16_t fcs_pitch;
    int16_t fcs_roll;
    int16_t fcs_yaw;
    int16_t fcs_yaw_ref;
    int16_t fcs_accx;
    int16_t fcs_accy;
    int16_t fcs_accz;
    uint8_t gb_ctrl_mode;
    int16_t gb_pitch_ref;
    int16_t gb_roll_ref;
    int16_t gb_yaw_ref;
    uint8_t fcs_state;
    int16_t fcs_yaw_w_ref;
    int8_t  fcs_yaw_rc_cmd;
    uint8_t reserve[15];
    // --- FCS心跳包特定数据结束 ---
    uint16_t crc16;                     // 2字节 (应为小端)
} packet_fcs_heartbeat_v1_t; // 重命名以表明是完整数据包


typedef struct
{
    header_v1_t header;
    uint8_t cmd;
    float paras[8];
    uint16_t crc16;
}gb_general_setup_struct;

// 云台心跳包 (CMD_SET: 0x01, CMD_ID: 0x00) 的DATA部分
// 根据文档 source [15] 到 [19]
typedef struct {
    header_v1_t header;
    int16_t gb_pitch;           // 云台姿态角，单位：0.01度 (小端)
    int16_t gb_roll;            // 云台姿态角，单位：0.01度 (小端)
    int16_t gb_yaw;             // 云台姿态角，单位：0.01度 (小端)
    struct {                    // 云台状态 (4字节, 小端)
        uint32_t atti_ready : 1;    // bit0：0-姿态未就绪，1-姿态就绪
        uint32_t joint_init_ok : 1; // bit1：0-关节角初始化未完成，1-关节初始化完成
        uint32_t under_volt : 1;    // bit2：0-电压正常，1-欠压
        uint32_t mems_lost : 1;     // bit3：0-IMU正常，1-IMU缺失
        uint32_t actuator : 2;      // bit5~bit4：0-关节正常驱动，1-关节堵转，2-关节关闭
        uint32_t soft_limit : 1;    // bit6：0-未到软件限位，1-到软件限位
        uint32_t ctrl_mode : 2;     // bit8~bit7：0-力矩模式，1-速度模式，2-位置模式，3-姿态模式
        uint32_t state_machine : 4; // bit12~bit9：状态机
        uint32_t reserve : 19;      // bit31-bit13：保留
    } status;
    int16_t joint_outter_angle; // 外框架角度，单位：0.01度 (小端)
    int16_t joint_middle_angle; // 中框架角度，单位：0.01度 (小端)
    int16_t joint_inner_angle;  // 内框架角度，单位：0.01度 (小端)
    int16_t joint_outter_speed; // 外框架角速度，单位：0.01度/s (小端)
    int16_t joint_middle_speed; // 中框架角速度，单位：0.01度/s (小端)
    int16_t joint_inner_speed;  // 内框架角速度，单位：0.01度/s (小端)
    int16_t imu_w_x;            // 陀螺仪x轴角速度，单位：0.01度/s (小端)
    int16_t imu_w_y;            // 陀螺仪y轴角速度，单位：0.01度/s (小端)
    int16_t imu_w_z;            // 陀螺仪z轴角速度，单位：0.01度/s (小端)
    uint8_t reserve[10];        // 保留
    uint16_t crc16;
} payload_gb_heartbeat_v1_t; // 云台心跳包载荷



typedef struct
{
    header_v1_struct header;
    uint8_t cmd;
    int32_t paras[8];
    uint16_t crc16;

}camera_general_setup_struct;


#pragma pack(pop)

// --- CRC16 和 CHECKSUM 函数声明 ---
// 你需要从文档中实现这个 CRC16 函数 (source [41] - [46])
// extern uint16_t calc_crc16_new_protocol(uint8_t *pbuf, uint16_t len);



// 模块编号定义 (参考文档 表4.1 source [47])
// 设备类型 (高5位)
#define DEVICE_TYPE_PC      (0x00 << 3)
#define DEVICE_TYPE_GIMBAL  (0x01 << 3)
#define DEVICE_TYPE_FC      (0x02 << 3)
// 索引 (低3位) - 通常为0
#define DEVICE_INDEX_0      0x00

#define SENDER_ID_FC      (DEVICE_TYPE_FC | DEVICE_INDEX_0)     // 飞控作为发送者: 0x10
#define RECEIVER_ID_GIMBAL (DEVICE_TYPE_GIMBAL | DEVICE_INDEX_0) // 云台作为接收者: 0x08

//#define SENDER_ID_FC_V1      (DEVICE_TYPE_FC_V1 | DEVICE_INDEX_0_V1)     // 飞控作为发送者: 0x10
//#define RECEIVER_ID_GIMBAL_V1 (DEVICE_TYPE_GIMBAL_V1 | DEVICE_INDEX_0_V1) // 云台作为接收者: 0x08
// (根据实际通信对象调整)

// 定义云台心跳包的 CMD_SET 和 CMD_ID
#define CMD_SET_GIMBAL_V1 0x01
#define CMD_ID_GB_HEARTBEAT_V1 0x00

// 飞控心跳包的 CMD_SET 和 CMD_ID
#define CMD_SET_FCS_V1 0x02
#define CMD_ID_FCS_HEARTBEAT_V1 0x00




void stop_gimbal();
void start_gimbal();
void send_fcs_heartbeat_v1_example();
void send_fcs_reboot_example();
void send_fcs_look_down();
void send_fcs_look_left();
void send_fcs_look_right();
void send_fcs_look_up() ;
void send_fcs_look_down_at_once();


void set_gimbal_speed(unsigned int pitch, unsigned int yaw);

void set_gimbal_speed_factor(float current_zoom_factor);

// 指定角度运动
void send_fcs_to_special_angle(int yaw_ref, int pitch_ref);

// void send_fcs_info_to_gimbal(int drone_desired_yaw, 
// int drone_pitch, 
// int drone_roll, 
// int drone_yaw, 
// int accel_x, 
// int accel_y, 
// int accel_z, 
// int takeoff_status_fc_data_valid);

#ifdef __cplusplus
}
#endif


#endif /* NEW_PROTOCOL_V1_H_ */