#ifndef DRONEPOD_K40T_PROTOCOL_H
#define DRONEPOD_K40T_PROTOCOL_H

#ifdef __cplusplus
extern "C"
{
#endif


#include <stddef.h>
#include <stdint.h>



/**********************************************************
 * K40T四光云台协议消息ID完整定义 (V1.4.12)
 * 注：所有消息ID均为24位小端格式（低字节在前）
 **********************************************************/

/*-------------------------------------------
 * 系统地址定义
 *------------------------------------------*/
#define SYS_ID_APP           0x01    // APP地址
#define COMP_ID_APP          0x01
#define SYS_ID_FC            0x02    // 飞控地址
#define COMP_ID_FC           0x01
#define SYS_ID_GIMBAL        0x04    // 云台地址
#define COMP_ID_GIMBAL       0x01

/*-------------------------------------------
 * 云台载荷协议 (Sysid:0x04)
 *------------------------------------------*/
// ===== 周期性上报消息 =====
#define MSG_ID_GIMBAL_STATUS         0x020001  // 云台状态（500ms）
#define MSG_ID_GIMBAL_ATTITUDE       0x020002  // 姿态信息（100ms）

// ===== 请求类消息 =====
#define MSG_ID_GIMBAL_CONTROL         0x000010  // 云台控制
#define MSG_ID_GIMBAL_ANGLE_SET       0x000012  // 指定角度控制
#define MSG_ID_GIMBAL_CALIBRATE       0x000013  // 一键校飘
#define MSG_ID_GIMBAL_SPEED_SET       0x000017  // 波轮速度设置
#define MSG_ID_GIMBAL_VERSION_REQ     0x000018  // 获取版本号
#define MSG_ID_GIMBAL_POINT_ALIGN     0x00002C  // 指点对准
#define MSG_ID_GIMBAL_SERVO_OFF       0x00002D  // 关闭伺服
#define MSG_ID_GIMBAL_LINEAR_CAL      0x00002E  // 线性校准
#define MSG_ID_GIMBAL_REBOOT          0x00002F  // 软重启
#define MSG_ID_GIMBAL_FC_ATTITUDE_SET 0x000030  // 使用飞控假姿态
#define MSG_ID_GIMBAL_ACC_CAL         0x000031  // 校准加速度偏置
#define MSG_ID_GIMBAL_STABILIZE_SET   0x000033  // 稳像设置

// ===== ACK消息 ===== 
#define MSG_ID_ACK_GIMBAL_CONTROL     0x010010  
#define MSG_ID_ACK_GIMBAL_ANGLE_SET   0x010012
#define MSG_ID_ACK_GIMBAL_CALIBRATE   0x010013
#define MSG_ID_ACK_GIMBAL_SPEED_SET   0x010017
#define MSG_ID_ACK_GIMBAL_POINT_ALIGN 0x01002C
// ...其他ACK按 0x01XXXX 规则生成

// ===== 状态帧消息 =====
#define MSG_ID_STATE_GIMBAL_CONTROL   0x020010
#define MSG_ID_STATE_GIMBAL_SPEED_SET 0x020017
// ...其他状态帧按 0x02XXXX 规则生成

/*-------------------------------------------
 * 相机载荷协议 (Sysid:0x04)
 *------------------------------------------*/
// ===== 周期性上报 =====
#define MSG_ID_CAM_SYS_STATUS         0x020003  // 系统状态（1s）
#define MSG_ID_IR_STATUS              0x020004  // 红外状态（200ms）
#define MSG_ID_VISIBLE_STATUS         0x020005  // 可见光状态（200ms）
#define MSG_ID_CAM_UPGRADE_STATUS     0x000008  // 升级状态（1s）
#define MSG_ID_PLAYBACK_STATUS        0x000009  // 播放状态（1s）

// ===== 红外设置指令 =====
#define MSG_ID_IR_CONFIG_READ         0x000100  // 读取所有参数
#define MSG_ID_IR_ZOOM_SET            0x000105  // 电子放大
#define MSG_ID_IR_COLORMAP_SET        0x000106  // 伪彩设置
#define MSG_ID_IR_THERMAL_SWITCH      0x000108  // 测温开关
#define MSG_ID_IR_SHARPEN_SET         0x00010A  // 锐化设置
#define MSG_ID_IR_BRIGHTNESS_SET      0x00010B  // 亮度设置
#define MSG_ID_IR_CONTRAST_SET        0x00010C  // 对比度设置
#define MSG_ID_IR_DENOISE_SET         0x00010D  // 去噪设置
#define MSG_ID_IR_ENHANCE_SET         0x00010E  // 增强设置
#define MSG_ID_IR_SPOT_THERMAL        0x00010F  // 点测温设置
#define MSG_ID_IR_AREA_THERMAL        0x000110  // 区域测温设置
#define MSG_ID_IR_GAIN_MODE_SET       0x000123  // 增益模式设置
#define MSG_ID_IR_ALARM_SET           0x000124  // 温度预警设置

// ===== 可见光设置指令 =====
#define MSG_ID_VIS_ZOOM_SET           0x000201  // 光学变倍设置
#define MSG_ID_VIS_EZOOM_SET          0x000202  // 电子变倍设置
#define MSG_ID_VIS_AE_MODE_SET        0x000203  // AE模式设置
#define MSG_ID_VIS_FOCUS_MODE_SET     0x000204  // 对焦模式
#define MSG_ID_VIS_IRCUT_SET          0x000205  // IRCUT切换
#define MSG_ID_VIS_EV_SET             0x000206  // EV值设置
#define MSG_ID_VIS_WHITEBALANCE_SET   0x000207  // 白平衡设置
#define MSG_ID_VIS_ANTI_FLICKER_SET   0x000208  // 抗闪烁设置
#define MSG_ID_VIS_BLC_SET            0x000209  // 背光补偿
#define MSG_ID_VIS_AE_LOCK_SET        0x00020A  // AE锁定
#define MSG_ID_VIS_METERING_SET       0x00020B  // 测光模式

// ===== 通用指令 =====
#define MSG_ID_CAM_MODE_SET           0x000300  // 拍照/录像模式
#define MSG_ID_CAM_SHOOT_PARAM_SET    0x000301  // 拍照参数设置
#define MSG_ID_CAM_SHOOT              0x000302  // 拍照指令
#define MSG_ID_CAM_RECORD             0x000303  // 录像指令
#define MSG_ID_ZOOM_SPECIFIC          0x000304  // 指定混合变倍
#define MSG_ID_ZOOM_CONTINUOUS        0x000306  // 连续变倍
#define MSG_ID_PRECISE_RESHOOT        0x000307  // 精准复拍
#define MSG_ID_BITRATE_SET            0x000308  // 码率设置
#define MSG_ID_RESOLUTION_SET         0x00030A  // 分辨率设置
#define MSG_ID_ENCODE_FORMAT_SET      0x00030B  // 编码格式设置
#define MSG_ID_CAM_UPGRADE            0x00030C  // TF卡升级
#define MSG_ID_CAM_FORMAT             0x00030D  // TF卡格式化
#define MSG_ID_TIME_SYNC              0x00030E  // 授时指令
#define MSG_ID_FACTORY_RESET          0x00030F  // 恢复出厂设置
#define MSG_ID_GPS_REQUEST            0x000310  // 请求GPS信息
#define MSG_ID_IP_SET                 0x000311  // IP地址设置
#define MSG_ID_IP_GET                 0x000312  // IP地址获取
#define MSG_ID_FOCUS_SET              0x000313  // 调焦指令
#define MSG_ID_OSD_SWITCH             0x000314  // OSD水印开关
#define MSG_ID_OSD_TEXT_SET           0x000315  // OSD文案设置
#define MSG_ID_CAM_SHUTDOWN           0x000316  // 相机关机
#define MSG_ID_CAM_VERSION_REQ        0x000317  // 获取版本号
#define MSG_ID_IMAGE_MODE_SET         0x000318  // 图像模式设置
#define MSG_ID_AI_DETECTION           0x000319  // AI识别
#define MSG_ID_TARGET_GPS_REQ         0x000320  // 目标GPS请求
#define MSG_ID_AI_TRACKING            0x000324  // 目标追踪

// ===== ACK消息 =====
#define MSG_ID_ACK_IR_ZOOM_SET       0x010105
#define MSG_ID_ACK_CAM_SHOOT         0x010302
#define MSG_ID_ACK_ZOOM_SPECIFIC     0x010304
#define MSG_ID_ACK_FACTORY_RESET     0x01030F
// ...其他ACK按相同规则生成

// ===== 状态帧消息 =====
#define MSG_ID_STATE_ZOOM_SPECIFIC   0x020304
#define MSG_ID_STATE_AI_DETECTION    0x020319
// ...其他状态帧按相同规则生成

/*-------------------------------------------
 * 激光协议 (0x000400-0x0004FF)
 *------------------------------------------*/
#define MSG_ID_LASER_RANGE_SET        0x000400  // 测距设置
#define MSG_ID_LASER_PERIODIC_SET     0x000406  // 周期测距

/*-------------------------------------------
 * SBUS通道协议 (0x000500-0x0005FF)
 *------------------------------------------*/
#define MSG_ID_SBUS_RANGE_SET         0x000500  // 通道范围设置
#define MSG_ID_SBUS_CHANNEL_MAP       0x000501  // 通道功能映射
#define MSG_ID_SBUS_CONFIG_GET        0x000502  // 配置获取

/**********************************************************
 * 工具宏定义（用于消息ID操作）
 **********************************************************/
// 生成ACK消息ID（高字节置0x01）
#define GEN_ACK_ID(orig_id)   ((orig_id & 0x00FFFF) | 0x010000)

// 生成状态帧消息ID（高字节置0x02） 
#define GEN_STATE_ID(orig_id) ((orig_id & 0x00FFFF) | 0x020000)

// 24位消息ID构造（低字节在前）
#define MSG_ID(h, m, l)        ((l << 16) | (m << 8) | h) 

// 示例：构造0x000001
#define MSG_ID_EXAMPLE         MSG_ID(0x00, 0x00, 0x01)





// Using the 0x01XXXX rule for ACKs from the document for gimbal commands:
#define MSG_ID_ACK_GIMBAL_VERSION_REQ   GEN_ACK_ID(MSG_ID_GIMBAL_VERSION_REQ) // 0x010018
#define MSG_ID_ACK_GIMBAL_SERVO_OFF     GEN_ACK_ID(MSG_ID_GIMBAL_SERVO_OFF)     // 0x01002D
#define MSG_ID_ACK_GIMBAL_LINEAR_CAL    GEN_ACK_ID(MSG_ID_GIMBAL_LINEAR_CAL)    // 0x01002E
#define MSG_ID_ACK_GIMBAL_REBOOT        GEN_ACK_ID(MSG_ID_GIMBAL_REBOOT)        // 0x01002F
#define MSG_ID_ACK_GIMBAL_FC_ATT_SET  GEN_ACK_ID(MSG_ID_GIMBAL_FC_ATTITUDE_SET) // 0x010030
#define MSG_ID_ACK_GIMBAL_ACC_CAL       GEN_ACK_ID(MSG_ID_GIMBAL_ACC_CAL)       // 0x010031
// For MSG_ID_FC_INFO_TO_GIMBAL (0x000032), the ACK would be 0x010032
// Let's assume a base ID if not in your list:
#define MSG_ID_FC_INFO_TO_GIMBAL      0x000032 // As per document 3.1.2.12
#define MSG_ID_ACK_FC_INFO_TO_GIMBAL  GEN_ACK_ID(MSG_ID_FC_INFO_TO_GIMBAL)      // 0x010032
#define MSG_ID_ACK_GIMBAL_STABILIZE_SET GEN_ACK_ID(MSG_ID_GIMBAL_STABILIZE_SET) // 0x010033

// It's good practice to have GEN_ACK_ID available. If it's not in a common header,
// you can define it in dronepod_k40t_message.c or its header:
#ifndef GEN_ACK_ID
#define GEN_ACK_ID(orig_id)   ((orig_id & 0x00FFFF) | 0x010000)
#endif
#ifndef GEN_STATE_ID
#define GEN_STATE_ID(orig_id) ((orig_id & 0x00FFFF) | 0x020000)
#endif




// (你可能已经在你的MSG ID头文件中定义了这些，或者可以用GEN_ACK_ID)
#define MSG_ID_ACK_IR_CONFIG_READ       GEN_ACK_ID(MSG_ID_IR_CONFIG_READ)      // 0x010100
// MSG_ID_ACK_IR_ZOOM_SET is 0x010105 (来自你的列表)
#define MSG_ID_ACK_IR_COLORMAP_SET      GEN_ACK_ID(MSG_ID_IR_COLORMAP_SET)     // 0x010106
#define MSG_ID_ACK_IR_THERMAL_SWITCH    GEN_ACK_ID(MSG_ID_IR_THERMAL_SWITCH)   // 0x010108
#define MSG_ID_ACK_IR_SHARPEN_SET       GEN_ACK_ID(MSG_ID_IR_SHARPEN_SET)      // 0x01010A
#define MSG_ID_ACK_IR_BRIGHTNESS_SET    GEN_ACK_ID(MSG_ID_IR_BRIGHTNESS_SET)   // 0x01010B
#define MSG_ID_ACK_IR_CONTRAST_SET      GEN_ACK_ID(MSG_ID_IR_CONTRAST_SET)     // 0x01010C
#define MSG_ID_ACK_IR_DENOISE_SET       GEN_ACK_ID(MSG_ID_IR_DENOISE_SET)      // 0x01010D
#define MSG_ID_ACK_IR_ENHANCE_SET       GEN_ACK_ID(MSG_ID_IR_ENHANCE_SET)      // 0x01010E
#define MSG_ID_ACK_IR_SPOT_THERMAL      GEN_ACK_ID(MSG_ID_IR_SPOT_THERMAL)     // 0x01010F
#define MSG_ID_ACK_IR_AREA_THERMAL      GEN_ACK_ID(MSG_ID_IR_AREA_THERMAL)     // 0x010110
#define MSG_ID_ACK_IR_GAIN_MODE_SET     GEN_ACK_ID(MSG_ID_IR_GAIN_MODE_SET)    // 0x010123
#define MSG_ID_ACK_IR_ALARM_SET         GEN_ACK_ID(MSG_ID_IR_ALARM_SET)        // 0x010124
// 文档3.2.2.14 红外相机测温度信息叠加开关 MSG_ID_IR_THERMAL_OVERLAY_SWITCH 0x000125
#define MSG_ID_IR_THERMAL_OVERLAY_SWITCH 0x000125 // 假设基础ID
#define MSG_ID_ACK_IR_THERMAL_OVERLAY_SWITCH GEN_ACK_ID(MSG_ID_IR_THERMAL_OVERLAY_SWITCH) // 0x010125
// 文档3.2.2.15 红外相机阈值温差设置 MSG_ID_IR_THRESHOLD_TEMP_DIFF_SET 0x000126
#define MSG_ID_IR_THRESHOLD_TEMP_DIFF_SET 0x000126 // 假设基础ID
#define MSG_ID_ACK_IR_THRESHOLD_TEMP_DIFF_SET GEN_ACK_ID(MSG_ID_IR_THRESHOLD_TEMP_DIFF_SET) // 0x010126






// (你可能已经在你的MSG ID头文件中定义了这些，或者可以用GEN_ACK_ID)
#define MSG_ID_ACK_IR_CONFIG_READ       GEN_ACK_ID(MSG_ID_IR_CONFIG_READ)      // 0x010100
// MSG_ID_ACK_IR_ZOOM_SET is 0x010105 (来自你的列表)
#define MSG_ID_ACK_IR_COLORMAP_SET      GEN_ACK_ID(MSG_ID_IR_COLORMAP_SET)     // 0x010106
#define MSG_ID_ACK_IR_THERMAL_SWITCH    GEN_ACK_ID(MSG_ID_IR_THERMAL_SWITCH)   // 0x010108
#define MSG_ID_ACK_IR_SHARPEN_SET       GEN_ACK_ID(MSG_ID_IR_SHARPEN_SET)      // 0x01010A
#define MSG_ID_ACK_IR_BRIGHTNESS_SET    GEN_ACK_ID(MSG_ID_IR_BRIGHTNESS_SET)   // 0x01010B
#define MSG_ID_ACK_IR_CONTRAST_SET      GEN_ACK_ID(MSG_ID_IR_CONTRAST_SET)     // 0x01010C
#define MSG_ID_ACK_IR_DENOISE_SET       GEN_ACK_ID(MSG_ID_IR_DENOISE_SET)      // 0x01010D
#define MSG_ID_ACK_IR_ENHANCE_SET       GEN_ACK_ID(MSG_ID_IR_ENHANCE_SET)      // 0x01010E
#define MSG_ID_ACK_IR_SPOT_THERMAL      GEN_ACK_ID(MSG_ID_IR_SPOT_THERMAL)     // 0x01010F
#define MSG_ID_ACK_IR_AREA_THERMAL      GEN_ACK_ID(MSG_ID_IR_AREA_THERMAL)     // 0x010110
#define MSG_ID_ACK_IR_GAIN_MODE_SET     GEN_ACK_ID(MSG_ID_IR_GAIN_MODE_SET)    // 0x010123
#define MSG_ID_ACK_IR_ALARM_SET         GEN_ACK_ID(MSG_ID_IR_ALARM_SET)        // 0x010124
// 文档3.2.2.14 红外相机测温度信息叠加开关 MSG_ID_IR_THERMAL_OVERLAY_SWITCH 0x000125
#define MSG_ID_IR_THERMAL_OVERLAY_SWITCH 0x000125 // 假设基础ID
#define MSG_ID_ACK_IR_THERMAL_OVERLAY_SWITCH GEN_ACK_ID(MSG_ID_IR_THERMAL_OVERLAY_SWITCH) // 0x010125
// 文档3.2.2.15 红外相机阈值温差设置 MSG_ID_IR_THRESHOLD_TEMP_DIFF_SET 0x000126
#define MSG_ID_IR_THRESHOLD_TEMP_DIFF_SET 0x000126 // 假设基础ID
#define MSG_ID_ACK_IR_THRESHOLD_TEMP_DIFF_SET GEN_ACK_ID(MSG_ID_IR_THRESHOLD_TEMP_DIFF_SET) // 0x010126






// (文档3.2.3.1 读取可见光所有参数 MSG_ID 0x000200)
#define MSG_ID_VL_ALL_SETTINGS_READ     0x000200
#define MSG_ID_ACK_VL_ALL_SETTINGS_READ GEN_ACK_ID(MSG_ID_VL_ALL_SETTINGS_READ) // 0x010200

// 你列表中的 MSG_ID_VIS_ZOOM_SET 是 0x000201 (对应文档3.2.3.2 可见光录像分辨率设置)
#define MSG_ID_ACK_VIS_VIDEO_RES_SET    GEN_ACK_ID(MSG_ID_VIS_ZOOM_SET)         // 0x010201 (使用你列表中的MSG_ID_VIS_ZOOM_SET作为基础)
// 你列表中的 MSG_ID_VIS_EZOOM_SET 是 0x000202 (对应文档3.2.3.3 可见光拍照分辨率设置)
#define MSG_ID_ACK_VIS_PHOTO_RES_SET    GEN_ACK_ID(MSG_ID_VIS_EZOOM_SET)        // 0x010202 (使用你列表中的MSG_ID_VIS_EZOOM_SET作为基础)
// 你列表中的 MSG_ID_VIS_AE_MODE_SET 是 0x000203 (对应文档3.2.3.4 可见光ISO设置)
#define MSG_ID_ACK_VIS_ISO_SET          GEN_ACK_ID(MSG_ID_VIS_AE_MODE_SET)      // 0x010203
// 你列表中的 MSG_ID_VIS_FOCUS_MODE_SET 是 0x000204 (对应文档3.2.3.5 可见光电子快门设置)
#define MSG_ID_ACK_VIS_SHUTTER_SET      GEN_ACK_ID(MSG_ID_VIS_FOCUS_MODE_SET)   // 0x010204
// 你列表中的 MSG_ID_VIS_IRCUT_SET 是 0x000205 (对应文档3.2.3.6 可见光EV设置) - 注意名称可能不匹配内容
#define MSG_ID_ACK_VIS_EV_SET           GEN_ACK_ID(MSG_ID_VIS_IRCUT_SET)        // 0x010205
// 你列表中的 MSG_ID_VIS_EV_SET 是 0x000206 (对应文档3.2.3.7 可见光白平衡设置) - 名称与你的列表冲突，这里用文档内容
#define MSG_ID_ACK_VIS_WB_SET           GEN_ACK_ID(MSG_ID_VIS_WHITEBALANCE_SET) // 0x010207 (使用你列表中的MSG_ID_VIS_WHITEBALANCE_SET)
// 你列表中的 MSG_ID_VIS_WHITEBALANCE_SET 是 0x000207 (对应文档3.2.3.8 可见光抗闪烁设置)
#define MSG_ID_ACK_VIS_ANTIFLICKER_SET  GEN_ACK_ID(MSG_ID_VIS_ANTI_FLICKER_SET) // 0x010208
// 你列表中的 MSG_ID_VIS_ANTI_FLICKER_SET 是 0x000208 (对应文档3.2.3.9 可见光强光抑制)
#define MSG_ID_ACK_VIS_STRONG_LIGHT_SUP_SET GEN_ACK_ID(MSG_ID_VIS_BLC_SET)      // 0x010209 (这里用你下一个ID的基础值，假设你的0x000208是指强光抑制)
// 你列表中的 MSG_ID_VIS_BLC_SET 是 0x000209 (对应文档3.2.3.10 可见光背光补偿)
#define MSG_ID_ACK_VIS_BLC_SET          GEN_ACK_ID(MSG_ID_VIS_BLC_SET)          // 0x010209
// 你列表中的 MSG_ID_VIS_AE_LOCK_SET 是 0x00020A (对应文档3.2.3.11 AE锁定)
#define MSG_ID_ACK_VIS_AE_LOCK_SET      GEN_ACK_ID(MSG_ID_VIS_AE_LOCK_SET)      // 0x01020A
// 你列表中的 MSG_ID_VIS_METERING_SET 是 0x00020B (对应文档3.2.3.12 测光模式)
#define MSG_ID_ACK_VIS_METERING_SET     GEN_ACK_ID(MSG_ID_VIS_METERING_SET)     // 0x01020B







// 在文档2的dronepod_k40t_protocol.h中添加以下定义

/*-------------------------------------------
 * 通用指令ACK消息ID (0x010300-0x0103FF)
 *------------------------------------------*/
#define MSG_ID_ACK_CAM_MODE_SET            GEN_ACK_ID(MSG_ID_CAM_MODE_SET)          // 0x010300
#define MSG_ID_ACK_CAM_SHOOT_PARAM_SET     GEN_ACK_ID(MSG_ID_CAM_SHOOT_PARAM_SET)   // 0x010301
#define MSG_ID_ACK_CAM_SHOOT               GEN_ACK_ID(MSG_ID_CAM_SHOOT)             // 0x010302
#define MSG_ID_ACK_CAM_RECORD              GEN_ACK_ID(MSG_ID_CAM_RECORD)            // 0x010303
#define MSG_ID_ACK_ZOOM_SPECIFIC           GEN_ACK_ID(MSG_ID_ZOOM_SPECIFIC)         // 0x010304
#define MSG_ID_ACK_ZOOM_CONTINUOUS         GEN_ACK_ID(MSG_ID_ZOOM_CONTINUOUS)       // 0x010306
#define MSG_ID_ACK_PRECISE_RESHOOT         GEN_ACK_ID(MSG_ID_PRECISE_RESHOOT)       // 0x010307
#define MSG_ID_ACK_BITRATE_SET             GEN_ACK_ID(MSG_ID_BITRATE_SET)          // 0x010308
#define MSG_ID_ACK_RESOLUTION_SET          GEN_ACK_ID(MSG_ID_RESOLUTION_SET)        // 0x01030A
#define MSG_ID_ACK_ENCODE_FORMAT_SET       GEN_ACK_ID(MSG_ID_ENCODE_FORMAT_SET)     // 0x01030B
#define MSG_ID_ACK_CAM_UPGRADE             GEN_ACK_ID(MSG_ID_CAM_UPGRADE)          // 0x01030C
#define MSG_ID_ACK_CAM_FORMAT              GEN_ACK_ID(MSG_ID_CAM_FORMAT)            // 0x01030D
#define MSG_ID_ACK_TIME_SYNC               GEN_ACK_ID(MSG_ID_TIME_SYNC)             // 0x01030E
#define MSG_ID_ACK_FACTORY_RESET           GEN_ACK_ID(MSG_ID_FACTORY_RESET)         // 0x01030F
#define MSG_ID_ACK_GPS_REQUEST             GEN_ACK_ID(MSG_ID_GPS_REQUEST)           // 0x010310
#define MSG_ID_ACK_IP_SET                  GEN_ACK_ID(MSG_ID_IP_SET)                // 0x010311
#define MSG_ID_ACK_IP_GET                  GEN_ACK_ID(MSG_ID_IP_GET)                // 0x010312
#define MSG_ID_ACK_FOCUS_SET               GEN_ACK_ID(MSG_ID_FOCUS_SET)             // 0x010313
#define MSG_ID_ACK_OSD_SWITCH              GEN_ACK_ID(MSG_ID_OSD_SWITCH)            // 0x010314
#define MSG_ID_ACK_OSD_TEXT_SET            GEN_ACK_ID(MSG_ID_OSD_TEXT_SET)          // 0x010315
#define MSG_ID_ACK_CAM_SHUTDOWN            GEN_ACK_ID(MSG_ID_CAM_SHUTDOWN)          // 0x010316
#define MSG_ID_ACK_CAM_VERSION_REQ         GEN_ACK_ID(MSG_ID_CAM_VERSION_REQ)       // 0x010317
#define MSG_ID_ACK_IMAGE_MODE_SET          GEN_ACK_ID(MSG_ID_IMAGE_MODE_SET)        // 0x010318
#define MSG_ID_ACK_AI_DETECTION            GEN_ACK_ID(MSG_ID_AI_DETECTION)          // 0x010319
#define MSG_ID_ACK_TARGET_GPS_REQ          GEN_ACK_ID(MSG_ID_TARGET_GPS_REQ)        // 0x010320
#define MSG_ID_ACK_AI_TRACKING             GEN_ACK_ID(MSG_ID_AI_TRACKING)           // 0x010324

/*-------------------------------------------
 * 通用指令状态帧消息ID (0x020300-0x0203FF)
 *------------------------------------------*/
#define MSG_ID_STATE_CAM_SHOOT             GEN_STATE_ID(MSG_ID_CAM_SHOOT)          // 0x020302
#define MSG_ID_STATE_CAM_RECORD            GEN_STATE_ID(MSG_ID_CAM_RECORD)         // 0x020303
#define MSG_ID_STATE_ZOOM_SPECIFIC         GEN_STATE_ID(MSG_ID_ZOOM_SPECIFIC)      // 0x020304
#define MSG_ID_STATE_PRECISE_RESHOOT        GEN_STATE_ID(MSG_ID_PRECISE_RESHOOT)    // 0x020307
#define MSG_ID_STATE_CAM_UPGRADE           GEN_STATE_ID(MSG_ID_CAM_UPGRADE)       // 0x02030C
#define MSG_ID_STATE_CAM_FORMAT            GEN_STATE_ID(MSG_ID_CAM_FORMAT)         // 0x02030D
#define MSG_ID_STATE_AI_DETECTION          GEN_STATE_ID(MSG_ID_AI_DETECTION)       // 0x020319
#define MSG_ID_STATE_AI_TRACKING           GEN_STATE_ID(MSG_ID_AI_TRACKING)        // 0x020324





// 确保结构体紧凑对齐 (无填充)
#pragma pack(push, 1)

/**
 * @brief K40T 协议帧头结构体
 * @note 对应文档 2.1 协议帧格式
 */
typedef struct {
    uint8_t stx;             // 0xFD, 数据包启动标记
    uint8_t len;             // 载荷长度 (0-243)
    uint8_t dest_sys_id;     // 接收者系统ID (0-255)
    uint8_t dest_comp_id;    // 接收者组件ID (0-255)
    uint8_t seq;             // 数据包序列号 (0-255)
    uint8_t src_sys_id;      // 发送者系统ID (1-255)
    uint8_t src_comp_id;     // 发送者组件ID (1-255)
    uint8_t msg_id_low;      // 消息ID (低字节)
    uint8_t msg_id_mid;      // 消息ID (中字节)
    uint8_t msg_id_high;     // 消息ID (高字节)
    uint8_t payload[243];    // 有效载荷数据 (实际长度由 len 决定)
    // uint16_t checksum;    // 校验和是在载荷之后，具体位置取决于 len 字段。
                            // 建议在编码/解码时单独处理校验和的计算与追加/验证。
} K40T_Protocol_Frame_Header_t;

/**
 * @brief 通用 ACK 响应结构体
 * @note 适用于多数指令的ACK响应
 */
typedef struct {
    uint16_t response_code; // 字节1-2: 响应码 (详见文档 3.3 ACK反馈表)
} Ack_Generic_Response_t;

// --- 云台载荷协议 (Sysid:0x03) ---

// --- 周期性上报消息 (0x000001-0x00000F) ---

/**
 * @brief 云台状态消息 (MsgID: 0x000001)
 * @note 上报周期: 2Hz (500ms)
 * @note 对应文档 3.1.1.1
 */
typedef struct {
    uint8_t gimbal_camera_status; // 字节1: 低4位:云台连接状态(0:正常,1:异常), 高4位:相机连接状态(0:正常,1:异常)
    uint8_t upgrade_status;       // 字节2: 升级状态 (0x00:正常, 0x01:内核升级未完成, 0x02:固件升级未完成)
    uint8_t self_check_result;    // 字节3: 自检结果 (Bit0:红外, Bit1:长焦可见光, Bit2:广角可见光, Bit3:激光; 1:正常,0:异常)
    uint8_t stabilization_status; // 字节4: 稳像状态 (0:未开启, 1:正在增稳, 2:增稳失败, 3:增稳特征丢失)
    uint8_t reserved1;            // 字节5: 预留
    uint16_t reserved2;           // 字节6-7: 预留
} Payload_Gimbal_Status_0x000001_t;

/**
 * @brief 云台姿态信息 (MsgID: 0x000002)
 * @note 上报周期: 10Hz (100ms)
 * @note 对应文档 3.1.1.2
 */
typedef struct {
    uint16_t joint_yaw_angle;        // 字节1-2: 云台偏航角度(关节角), 无符号整型, 单位: 度*100
    uint16_t joint_roll_angle;       // 字节3-4: 云台横滚角度(关节角), 无符号整型, 单位: 度*100
    uint16_t joint_pitch_angle;      // 字节5-6: 云台俯仰角度(关节角), 无符号整型, 单位: 度*100
    uint16_t attitude_yaw_angle;     // 字节7-8: 云台偏航角度(姿态角), 无符号整型, 单位: 度*100
    uint16_t attitude_roll_angle;    // 字节9-10: 云台横滚角度(姿态角), 无符号整型, 单位: 度*100
    uint16_t attitude_pitch_angle;   // 字节11-12: 云台俯仰角度(姿态角), 无符号整型, 单位: 度*100
    int16_t  yaw_angular_velocity;   // 字节13-14: 云台偏航角速度, int16, 单位: (度/秒)*100
    int16_t  pitch_angular_velocity; // 字节15-16: 云台俯仰角速度, int16, 单位: (度/秒)*100
    int16_t  roll_angular_velocity;  // 字节17-18: 云台横滚角速度, int16, 单位: (度/秒)*100
    uint16_t reserved;               // 字节19-20: 预留
} Payload_Gimbal_Attitude_0x000002_t;

// --- 请求类消息 (0x000010-0x0000FF) ---

/**
 * @brief 云台控制指令 (MsgID: 0x000010) - 请求
 * @note 对应文档 3.1.2.1
 */
typedef struct {
    uint8_t work_mode_quick_func; // 字节1: 高4位:工作模式(00:无,01:回中,02:下视90°), 低4位:预留
    uint8_t yaw_control;          // 字节2: 方位控制 (0:左, 1:右, 2:停)
    uint8_t pitch_control;        // 字节3: 俯仰控制 (0:上, 1:下, 2:停)
    uint8_t reserved;             // 字节4: 预留
} Payload_Gimbal_Control_Req_0x000010_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 指定云台角度控制指令 (MsgID: 0x000012) - 请求
 * @note 对应文档 3.1.2.2
 */
typedef struct {
    uint8_t  pitch_direction; // 字节1: 俯仰方向 (0:向上, 1:向下, 2:无)
    uint16_t pitch_angle;     // 字节2-3: 俯仰角度 (上30°,下90°) - 单位文档未明确, 假设度*100
    uint8_t  yaw_direction;   // 字节4: 偏航方向 (0:向左, 1:向右, 2:无)
    uint16_t yaw_angle;       // 字节5-6: 偏航角度 (左180°,右180°) - 单位文档未明确, 假设度*100
    uint8_t  reserved;        // 字节7: 预留
} Payload_Gimbal_Specify_Angle_Req_0x000012_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 云台一键校飘指令 (MsgID: 0x000013) - 请求
 * @note 对应文档 3.1.2.3
 */
typedef struct {
    uint8_t calibration_status; // 字节1: 0x01:开始校飘
    uint8_t reserved;           // 字节2: 预留
} Payload_Gimbal_Drift_Calibration_Req_0x000013_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 云台波轮速度设置指令 (MsgID: 0x000017) - 请求
 * @note 对应文档 3.1.2.4
 */
typedef struct {
    uint8_t pitch_dial_speed; // 字节1: 俯仰波轮速度 (5-150, 对应1-30度/秒)
    uint8_t yaw_dial_speed;   // 字节2: 方位波轮速度 (5-150, 对应1-30度/秒)
    uint8_t reserved;         // 字节3: 预留
} Payload_Gimbal_Dial_Speed_Req_0x000017_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 获取云台版本号 (MsgID: 0x000018) - 请求
 * @note 对应文档 3.1.2.5
 */
typedef struct {
    uint8_t get_version_cmd; // 字节1: 0x01:获取云台版本号
    uint8_t reserved;        // 字节2: 预留
} Payload_Gimbal_Get_Version_Req_0x000018_t;

/**
 * @brief 获取云台版本号 (MsgID: 0x000018) - ACK
 * @note 对应文档 3.1.2.5
 */
typedef struct {
    uint16_t response_code;      // 字节1-2: 响应码
    uint8_t  hw_major;           // 字节3: 云台硬件版本号(主)
    uint8_t  hw_minor;           // 字节4: 云台硬件版本号(次)
    uint8_t  hw_patch;           // 字节5: 云台硬件版本号(小)
    uint8_t  sw_major;           // 字节6: 云台软件版本号(主)
    uint8_t  sw_minor;           // 字节7: 云台软件版本号(次)
    uint8_t  sw_patch;           // 字节8: 云台软件版本号(小)
    uint8_t  reserved;           // 字节9: 预留
} Ack_Gimbal_Get_Version_0x000018_t;

/**
 * @brief 云台指点对准指令 (MsgID: 0x00002C) - 请求
 * @note 对应文档 3.1.2.6
 */
typedef struct {
    uint8_t  lens_selection;    // 字节1: 变倍镜头选择 (0x00:长焦, 0x01:广角, 0x02:红外)
    uint16_t hybrid_zoom_ratio; // 字节2-3: 混合变倍倍率, uint16, 单位0.1倍
    uint16_t target_x_coord;    // 字节4-5: 指点对准X坐标点 (1-1920, 中间值960), uint16
    uint16_t target_y_coord;    // 字节6-7: 指点对准Y坐标点 (1-1080, 中间值540), uint16
} Payload_Gimbal_Point_Align_Req_0x00002C_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 关闭云台伺服指令 (MsgID: 0x00002D) - 请求
 * @note 对应文档 3.1.2.7
 */
typedef struct {
    uint8_t close_servo_cmd; // 字节1: 0x00:关闭伺服
    uint8_t reserved;        // 字节2: 预留
} Payload_Gimbal_Close_Servo_Req_0x00002D_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 云台线性校准指令 (MsgID: 0x00002E) - 请求
 * @note 对应文档 3.1.2.8
 */
typedef struct {
    uint8_t linear_calib_cmd; // 字节1: 0x01:线性校准
    uint8_t reserved;         // 字节2: 预留
} Payload_Gimbal_Linear_Calib_Req_0x00002E_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 云台软重启指令 (MsgID: 0x00002F) - 请求
 * @note 对应文档 3.1.2.9
 */
typedef struct {
    uint8_t soft_reboot_cmd; // 字节1: 0x01:软重启
    uint8_t reserved;        // 字节2: 预留
} Payload_Gimbal_Soft_Reboot_Req_0x00002F_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 云台使用飞控假姿态指令 (MsgID: 0x000030) - 请求
 * @note 对应文档 3.1.2.10
 */
typedef struct {
    uint8_t use_fc_dummy_att_cmd; // 字节1: 0x01:使用飞控假姿态
    uint8_t reserved;             // 字节2: 预留
} Payload_Gimbal_Use_FC_Dummy_Att_Req_0x000030_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 云台校准运动加速度偏置指令 (MsgID: 0x000031) - 请求
 * @note 对应文档 3.1.2.11
 */
typedef struct {
    uint8_t calib_motion_accel_cmd; // 字节1: 0x01:校准运动加速度
    uint8_t reserved;               // 字节2: 预留
} Payload_Gimbal_Calib_Motion_Accel_Req_0x000031_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 飞控发送飞控信息给云台指令 (MsgID: 0x000032) - 请求
 * @note 频率: 10Hz
 * @note 对应文档 3.1.2.12
 */
typedef struct {
    int16_t  drone_desired_yaw;   // 字节1-2: 无人机期望航向角, 单位:度*100, int16_t
    int16_t  drone_pitch;         // 字节3-4: 无人机俯仰角, 单位:度*100, int16_t
    int16_t  drone_roll;          // 字节5-6: 无人机横滚角, 单位:度*100, int16_t
    int16_t  drone_yaw;           // 字节7-8: 无人机航向角(实时), 单位:度*100, int16_t
    uint16_t accel_x;             // 字节9-10: 无人机X轴加速度, 单位:速度*1000, uint16_t (速度单位需明确)
    uint16_t accel_y;             // 字节11-12: 无人机Y轴加速度, 单位:速度*1000, uint16_t
    uint16_t accel_z;             // 字节13-14: 无人机Z轴加速度, 单位:速度*1000, uint16_t
    uint8_t  takeoff_status_fc_data_valid; // 字节15: Bit0:起飞标志(0:未,1:已), Bit1-6:预留, Bit7:飞控数据有效性(0:无效,1:有效)
} Payload_FC_Info_To_Gimbal_Req_0x000032_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 云台稳像指令 (MsgID: 0x000033) - 请求
 * @note 对应文档 3.1.2.13
 */
typedef struct {
    uint8_t stabilization_cmd; // 字节1: 稳像 (0x01:开启, 0x00:关闭)
    uint8_t reserved;          // 字节2: 预留
} Payload_Gimbal_Stabilization_Req_0x000033_t;
// ACK: Ack_Generic_Response_t


// --- 相机载荷协议 (Sysid:0x04) ---

// --- 周期性上报消息(状态上报) (0x000001-0x00000F) --- (文档中相机部分从0x000003开始)

/**
 * @brief 相机系统状态反馈 (MsgID: 0x000003)
 * @note 上报周期: 1Hz (1s)
 * @note 对应文档 3.2.1.1
 */
typedef struct {
    uint8_t  photo_video_mode;    // 字节1: 拍照/录像模式 (0:拍照, 1:录像)
    uint8_t  network_resolution;  // 字节2: 网络出图分辨率 (0:1080P30fps, 1:720P30fps)
    uint8_t  video_encoding;      // 字节3: 视频编码格式 (0:H264, 1:H265)
    uint8_t  streaming_mode;      // 字节4: 推流模式 (0x00:红外, 0x05/0x06:可见光, 0x07:分屏)
    uint8_t  video_output_bitrate;// 字节5: 视频输出码率 (1:1M, 2:1.5M, 3:2M, 4:4M, 5:8M, 6:12M)
    uint8_t  photo_mode;          // 字节6: 拍照模式 (0:单拍, 1:连拍3/5张, 2:延时拍)
    uint8_t  timed_photo_interval;// 字节7: 延时拍照时间 (5/7/30/60 秒)
    uint8_t  burst_count;         // 字节8: 连拍张数 (3/5)
    uint8_t  sd_card_status;      // 字节9: SD卡状态 (0:正常, 1:异常, 2:慢速, 3:未插入, 4:已满, 5:格式错误)
    uint16_t sd_total_capacity;   // 字节10-11: SD卡总容量, 单位: MB*10
    uint16_t sd_remaining_capacity; // 字节12-13: SD卡剩余容量, 单位: MB*10
    uint16_t sd_used_capacity;    // 字节14-15: SD卡已用容量, 单位: MB*10
    uint8_t  reserved;            // 字节16: 预留
} Payload_Camera_System_Status_0x000003_t;

/**
 * @brief 红外相机状态反馈 (MsgID: 0x000004)
 * @note 上报周期: 5Hz (200ms)
 * @note 对应文档 3.2.1.2
 */
typedef struct {
    int16_t  area_max_temp;         // 字节1-2: 区域最高温度值, int16, 单位 0.1℃
    int16_t  area_min_temp;         // 字节3-4: 区域最低温度值, int16, 单位 0.1℃
    int16_t  area_center_temp;      // 字节5-6: 区域中心温度值, int16, 单位 0.1℃
    int16_t  spot_temp;             // 字节7-8: 点测温温度值, int16, 单位 0.1℃
    int16_t  area_avg_temp;         // 字节9-10: 区域平均温度值, int16, 单位 0.1℃
    uint16_t area_max_temp_x;       // 字节11-12: 区域最高温坐标X, uint16
    uint16_t area_max_temp_y;       // 字节13-14: 区域最高温坐标Y, uint16
    uint16_t area_min_temp_x;       // 字节15-16: 区域最低温坐标X, uint16
    uint16_t area_min_temp_y;       // 字节17-18: 区域最低温坐标Y, uint16
    uint16_t area_center_temp_x;    // 字节19-20: 区域中心温坐标X, uint16
    uint16_t area_center_temp_y;    // 字节21-22: 区域中心温坐标Y, uint16
    uint16_t spot_temp_x;           // 字节23-24: 点测温坐标X, uint16
    uint16_t spot_temp_y;           // 字节25-26: 点测温坐标Y, uint16
    uint8_t  high_temp_alert;       // 字节27: 高温预警标志 (0:未, 1:开始)
    uint8_t  low_temp_alert;        // 字节28: 低温预警标志 (0:未, 1:开始)
    uint8_t  temp_diff_alert;       // 字节29: 温度差预警标志 (0:未, 1:开始)
    uint8_t  threshold_temp_alert;  // 字节30: 阈值温度预警标志 (0:未, 1:开始)
    uint8_t  reserved[4];           // 字节31-34: 预留
} Payload_IR_Camera_Status_0x000004_t;

/**
 * @brief 可见光相机状态反馈 (MsgID: 0x000005)
 * @note 上报周期: 5Hz (200ms)
 * @note 对应文档 3.2.1.3
 */
typedef struct {
    uint8_t  zoom_status;         // 字节1: 变倍状态 (0x00:完成, 0x01:正在变倍)
    uint16_t focal_length;        // 字节2-3: 焦距, 单位: 0.01mm
    uint16_t hybrid_zoom_ratio;   // 字节4-5: 混合变倍倍率, 单位 0.1倍
    uint8_t  ev_value;            // 字节6: EV值上报 (0x00:Auto, 0x0A:+2, 0x10:+1, 0x16:0, 0x1C:-1, 0x23:-2)
    uint16_t iso_value;           // 字节7-8: ISO值上报, 单位 0.1db (文档单位存疑)
    uint16_t shutter_speed;       // 字节9-10: 电子快门值上报, 单位: 微秒
    uint8_t  ae_lock_status;      // 字节11: AE_LOCK状态 (0x01:开, 0x02:关)
    uint8_t  focus_status;        // 字节12: 对焦状态 (0x00:完成, 0x01:正在对焦)
    uint16_t precise_refocus_focal_length; // 字节13-14: 精准复拍焦距
    uint8_t  reserved;            // 字节15: 预留
} Payload_VL_Camera_Status_0x000005_t;

/**
 * @brief 相机升级与修复状态反馈 (MsgID: 0x000008)
 * @note 上报周期: 1Hz (1s)
 * @note 对应文档 3.2.1.4
 */
typedef struct {
    uint8_t status;   // 字节1: 状态 (0:成功, 1:失败, 2:正在升级, 3:正在修复)
    uint8_t progress; // 字节2: 当前进度 (0-100)
    uint8_t reserved; // 字节3: 预留
} Payload_Camera_Upgrade_Status_0x000008_t;

/**
 * @brief 播放状态反馈 (MsgID: 0x000009)
 * @note 上报周期: 1Hz (1s) (多媒体文件回放时)
 * @note 对应文档 3.2.1.5
 */
typedef struct {
    uint32_t playback_mode;      // 字节0-3: 播放状态 (2:播放中, 3:暂停, 7:停止)
    uint8_t  video_play_process; // 字节4: 播放进度 (0-100%)
    uint32_t video_length_ms;    // 字节5-8: 视频长度 (毫秒)
    uint32_t play_pos_ms;        // 字节9-12: 当前播放到的毫秒数
    // 文档中payload为255bytes，但只定义了前13字节，这里按已定义的。
} Payload_Playback_Status_0x000009_t;


// --- 红外相机设置消息 (0x000100-0x0001FF) ---

/**
 * @brief 红外相机所有设置参数读取指令 (MsgID: 0x000100) - 请求
 * @note 对应文档 3.2.2.1
 */
typedef struct {
    uint8_t read_all_cmd; // 字节1: 0x01:读取红外相机所有设置指令
    uint8_t reserved;     // 字节2: 预留
} Payload_IR_Read_All_Settings_Req_0x000100_t;

/**
 * @brief 红外相机所有设置参数读取指令 (MsgID: 0x000100) - ACK
 * @note 对应文档 3.2.2.1
 */
typedef struct {
    uint16_t response_code;             // 字节1-2: 反馈 (ACK:0表示成功)
    uint8_t  pseudo_color_setting;      // 字节3: 伪彩色设置 (1-20)
    uint8_t  temp_measure_ezoom_config; // 字节4: bit0:测温开关(0开,1关), bit1:测温类型(0点,1区域), bit2:机芯类型(0观瞄,1测温), bit3-7:电子放大
    uint8_t  ir_sharpen_param;          // 字节5: 红外锐化参数 (范围见支持说明)
    uint8_t  ir_gain_mode;              // 字节6: 红外增益模式 (0:高, 1:低, 2:自动)
    uint8_t  ir_brightness;             // 字节7: 红外亮度设置 (范围见支持说明)
    uint8_t  ir_contrast;               // 字节8: 红外对比度设置 (范围见支持说明)
    uint8_t  denoise_setting;           // 字节9: bit7:去噪开关(0关,1开), bit6-0:去噪等级
    uint8_t  enhance_setting;           // 字节10: bit0:增强开关(0关,1开), bit6-0:增强参数
    int16_t  high_temp_alert_thresh;    // 字节11-12: 高温预警温度设置 (开启:-1000~5000, 关闭:-2732), 单位0.1℃, int
    int16_t  low_temp_alert_thresh;     // 字节13-14: 低温预警温度设置 (开启:-1000~5000, 关闭:-2732), 单位0.1℃, int
    int16_t  temp_diff_alert_thresh;    // 字节15-16: 温度差预警设置 (开启:1~6000, 关闭:-2732), 单位0.1℃, int
    uint8_t  threshold_temp_alert_switch; // 字节17: 阈值温度预警开关 (0:关, 1:开)
    int16_t  temp_diff_baseline;        // 字节18-19: 温差基准值 (-300~500), 单位0.1℃, int
    int16_t  temp_fluctuation_value;    // 字节20-21: 温度浮动值 (0-800), 单位0.1℃, int
    uint16_t reserved;                  // 字节22-23: 预留
} Ack_IR_Read_All_Settings_0x000100_t;

/**
 * @brief 红外电子放大设置指令 (MsgID: 0x000105) - 请求
 * @note 对应文档 3.2.2.2
 */
typedef struct {
    uint8_t ezoom_setting; // 字节1: 0x01:无放大, 0x02-0x08:2-8倍电子放大
    uint8_t reserved;      // 字节2: 预留
} Payload_IR_EZoom_Set_Req_0x000105_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外伪彩设置指令 (MsgID: 0x000106) - 请求
 * @note 对应文档 3.2.2.3
 */
typedef struct {
    uint8_t pseudo_color_setting; // 字节1: 1-20 (对应不同伪彩)
    uint8_t reserved;             // 字节2: 预留
} Payload_IR_Pseudo_Color_Set_Req_0x000106_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外测温开关指令 (MsgID: 0x000108) - 请求
 * @note 对应文档 3.2.2.4
 */
typedef struct {
    uint8_t temp_measure_switch; // 字节1: 0x00:开, 0x01:关 (默认开)
    uint8_t reserved;            // 字节2: 预留
} Payload_IR_Temp_Measure_Switch_Req_0x000108_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外锐化设置指令 (MsgID: 0x00010A) - 请求
 * @note 对应文档 3.2.2.5
 */
typedef struct {
    uint8_t sharpen_setting; // 字节1: 锐化设置 (0-100)
    uint8_t reserved;        // 字节2: 预留
} Payload_IR_Sharpen_Set_Req_0x00010A_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外亮度设置指令 (MsgID: 0x00010B) - 请求
 * @note 对应文档 3.2.2.6
 */
typedef struct {
    uint8_t brightness_setting; // 字节1: 亮度参数 (0-100)
    uint8_t reserved;           // 字节2: 预留
} Payload_IR_Brightness_Set_Req_0x00010B_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外对比度设置指令 (MsgID: 0x00010C) - 请求
 * @note 对应文档 3.2.2.7
 */
typedef struct {
    uint8_t contrast_setting; // 字节1: 对比度参数 (0-100)
    uint8_t reserved;         // 字节2: 预留
} Payload_IR_Contrast_Set_Req_0x00010C_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外去噪设置指令 (MsgID: 0x00010D) - 请求
 * @note 对应文档 3.2.2.8
 */
typedef struct {
    uint8_t denoise_switch; // 字节1: 去噪开关 (0:关, 1:开)
    uint8_t denoise_level;  // 字节2: 去噪等级 (0-100)
    uint8_t reserved;       // 字节3: 预留
} Payload_IR_Denoise_Set_Req_0x00010D_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外图像增强设置指令 (MsgID: 0x00010E) - 请求
 * @note 对应文档 3.2.2.9
 */
typedef struct {
    uint8_t enhance_setting; // 字节1: 0:关, 1-10:等级
    uint8_t reserved;        // 字节2: 预留
} Payload_IR_Enhance_Set_Req_0x00010E_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外点测温设置指令 (MsgID: 0x00010F) - 请求
 * @note 对应文档 3.2.2.10
 */
typedef struct {
    uint16_t cursor_x; // 字节1-2: X轴光标点 (范围0-1920, 实际320-1079)
    uint16_t cursor_y; // 字节3-4: Y轴光标点 (范围0-1088, 实际32-1023)
    uint8_t  reserved; // 字节5: 预留
} Payload_IR_Spot_Temp_Set_Req_0x00010F_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外区域测温设置指令 (MsgID: 0x000110) - 请求
 * @note 对应文档 3.2.2.11
 */
typedef struct {
    uint16_t rect_width;  // 字节1-2: 区域框宽度
    uint16_t rect_height; // 字节3-4: 区域框高度
    uint16_t center_x;    // 字节5-6: 区域框中心X坐标
    uint16_t center_y;    // 字节7-8: 区域框中心Y坐标
    uint8_t  reserved;    // 字节9: 预留
} Payload_IR_Area_Temp_Set_Req_0x000110_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外相机增益模式设置指令 (MsgID: 0x000123) - 请求
 * @note 对应文档 3.2.2.12
 */
typedef struct {
    uint8_t gain_mode; // 字节1: 0x00:高增益, 0x01:低增益, 0x02:自动
    uint8_t reserved;  // 字节2: 预留
} Payload_IR_Gain_Mode_Set_Req_0x000123_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外相机温度预警设置指令 (MsgID: 0x000124) - 请求
 * @note 对应文档 3.2.2.13
 */
typedef struct {
    int16_t high_temp_alert_thresh; // 字节1-2: 高温预警 (开启:-1000~5000, 关闭:-2732), 单位0.1℃, int
    int16_t low_temp_alert_thresh;  // 字节3-4: 低温预警 (开启:-1000~5000, 关闭:-2732), 单位0.1℃, int
    int16_t temp_diff_alert_thresh; // 字节5-6: 温差预警 (开启:1~6000, 关闭:-2732), 单位0.1℃, int
    uint16_t reserved; // 字节7-8: 预留 (文档中写 Byte7，但长度为2)
} Payload_IR_Temp_Alert_Set_Req_0x000124_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外相机测温度信息叠加开关设置指令 (MsgID: 0x000125) - 请求
 * @note 对应文档 3.2.2.14
 */
typedef struct {
    uint8_t temp_overlay_switch; // 字节1: 0x00:关闭, 0x01:打开
    uint8_t reserved;            // 字节2: 预留
} Payload_IR_Temp_Overlay_Switch_Req_0x000125_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 红外相机阈值温差设置指令 (MsgID: 0x000126) - 请求
 * @note 对应文档 3.2.2.15
 */
typedef struct {
    uint8_t  switch_setting;     // 字节1: 开关 (0:关, 1:开)
    int16_t  temp_diff_baseline; // 字节2-3: 温差基准值 (-300~500), 单位0.1℃, int
    int16_t  temp_fluctuation;   // 字节4-5: 温度浮动值 (0-800), 单位0.1℃, int
    uint8_t  reserved;           // 字节6: 预留
} Payload_IR_Threshold_Temp_Diff_Set_Req_0x000126_t;
// ACK: Ack_Generic_Response_t


// --- 可见光相机 (0x000200-0x0002FF) ---

/**
 * @brief 可见光相机所有设置参数读取指令 (MsgID: 0x000200) - 请求
 * @note 对应文档 3.2.3.1
 */
typedef struct {
    uint8_t read_all_cmd; // 字节1: 0x01:读取可见光相机所有设置指令
    uint8_t reserved;     // 字节2: 预留
} Payload_VL_Read_All_Settings_Req_0x000200_t;

/**
 * @brief 可见光相机所有设置参数读取指令 (MsgID: 0x000200) - ACK
 * @note 对应文档 3.2.3.1
 */
typedef struct {
    uint16_t response_code;          // 字节1-2: 反馈 (ACK:0表示成功)
    uint8_t  photo_resolution;       // 字节3: 可见光拍照分辨率 (0x14:8000*6000, etc.)
    uint8_t  video_resolution;       // 字节4: 可见光录像分辨率 (0x08:1080p, etc.)
    uint8_t  video_bitrate;          // 字节5: 可见光录像码率 (H264/H265下不同)
    uint8_t  white_balance;          // 字节6: bit3-0:白平衡设置, bit7-4:预留
    uint16_t reserved1;              // 字节7-8: 预留
    uint8_t  ev_value;               // 字节9: EV值 (0x00:Auto, etc.)
    uint8_t  iso_setting;            // 字节10: ISO设置 (0x00:AUTO, 0x01:ISO100, etc.)
    uint8_t  shutter_speed;          // 字节11: 电子快门 (拍照/录像模式下不同)
    uint8_t  zoom_finetune_value;    // 字节12: 变焦微调值 (0-100)
    uint8_t  backlight_compensation; // 字节13: Bit7:开关(1开,2关), Bit6-0:值(0-100)
    uint8_t  strong_light_suppression; // 字节14: Bit7:开关(1开,2关), Bit6-0:值(0-100)
    uint8_t  ae_lock_feedback;       // 字节15: AE LOCK反馈 (0x1:开, 0x2:关)
    uint16_t osd_watermark_switch;   // 字节16-17: 可见光OSD水印开关 (bit0-14对应不同水印项, 1开,0关)
    uint8_t  anti_flicker_status;    // 字节18: 抗闪烁状态 (0x01:关, 0x02:50HZ, 0x03:60HZ, 0x04:自动)
    uint8_t  metering_mode_feedback; // 字节19: 可见光测光模式设置反馈 (0x01:中心权重, 0x02:区域, 0x03:平均)
} Ack_VL_Read_All_Settings_0x000200_t;

/**
 * @brief 可见光录像分辨率设置指令 (MsgID: 0x000201) - 请求
 * @note 对应文档 3.2.3.2
 */
typedef struct {
    uint8_t start_setting;      // 字节1: 0x00:开始设置
    uint8_t video_resolution; // 字节2: 0x08:1920*1080, 0x26:3840*2160, 0x36:4000*3000
} Payload_VL_Video_Res_Set_Req_0x000201_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光拍照分辨率设置指令 (MsgID: 0x000202) - 请求
 * @note 对应文档 3.2.3.3
 */
typedef struct {
    uint8_t start_setting;     // 字节1: 0x00:开始设置
    uint8_t photo_resolution; // 字节2: 0x14:8000*6000, 0x15:4000*3000, etc.
} Payload_VL_Photo_Res_Set_Req_0x000202_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光ISO设置指令 (MsgID: 0x000203) - 请求
 * @note 对应文档 3.2.3.4
 */
typedef struct {
    uint8_t start_setting; // 字节1: 0x00:开始设置
    uint8_t iso_setting;   // 字节2: 0x00:AUTO, 0x01:ISO100, etc.
} Payload_VL_ISO_Set_Req_0x000203_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光电子快门设置指令 (MsgID: 0x000204) - 请求
 * @note 对应文档 3.2.3.5
 */
typedef struct {
    uint8_t start_setting;   // 字节1: 0x00:开始设置
    uint8_t shutter_setting; // 字节2: 0x00:Auto, 0x01:1/4, etc.
} Payload_VL_Shutter_Set_Req_0x000204_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光EV设置指令 (MsgID: 0x000205) - 请求
 * @note 对应文档 3.2.3.6
 */
typedef struct {
    uint8_t start_setting; // 字节1: 0x00:开始设置
    uint8_t ev_setting;    // 字节2: 0x00:Auto, 0x0A:+2, etc.
} Payload_VL_EV_Set_Req_0x000205_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光白平衡设置指令 (MsgID: 0x000206) - 请求
 * @note 对应文档 3.2.3.7
 */
typedef struct {
    uint8_t  start_setting;   // 字节1: 0x00:开始设置
    uint8_t  wb_setting;      // 字节2: bit3-0:白平衡设置 (0001:Auto, etc.), bit7-4:预留
    uint16_t reserved;        // 字节3-4: 预留
} Payload_VL_WB_Set_Req_0x000206_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光抗闪烁设置指令 (MsgID: 0x000207) - 请求
 * @note 对应文档 3.2.3.8
 */
typedef struct {
    uint8_t start_setting;     // 字节1: 0x00:开始设置
    uint8_t anti_flicker_setting; // 字节2: 0x01:关, 0x02:50HZ, 0x03:60HZ, 0x04:自动
} Payload_VL_Anti_Flicker_Set_Req_0x000207_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光强光抑制设置指令 (MsgID: 0x000208) - 请求
 * @note 对应文档 3.2.3.9
 */
typedef struct {
    uint8_t start_setting;          // 字节1: 0x00:开始设置
    uint8_t suppression_switch;     // 字节2: 0x01:开启, 0x02:关闭
    uint8_t suppression_value;      // 字节3: 设定值 (0-100)
} Payload_VL_Strong_Light_Suppression_Set_Req_0x000208_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光背光补偿设置指令 (MsgID: 0x000209) - 请求
 * @note 对应文档 3.2.3.10
 */
typedef struct {
    uint8_t start_setting;        // 字节1: 0x00:开始设置
    uint8_t compensation_switch;  // 字节2: 0x01:开启, 0x02:关闭
    uint8_t compensation_value;   // 字节3: 设定值 (0-100)
} Payload_VL_Backlight_Comp_Set_Req_0x000209_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光AE LOCK设置指令 (MsgID: 0x00020A) - 请求
 * @note 对应文档 3.2.3.11
 */
typedef struct {
    uint8_t start_setting;  // 字节1: 0x00:开始设置
    uint8_t ae_lock_setting; // 字节2: 0x01:开, 0x02:关
} Payload_VL_AE_Lock_Set_Req_0x00020A_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 可见光相机测光模式指令 (MsgID: 0x00020B) - 请求
 * @note 对应文档 3.2.3.12
 */
typedef struct {
    uint8_t start_setting;   // 字节1: 0x00:开始设置
    uint8_t metering_mode; // 字节2: 1:中心权重, 2:区域测光(默认), 3:平均测光
} Payload_VL_Metering_Mode_Set_Req_0x00020B_t;
// ACK: Ack_Generic_Response_t

// --- 通用部分 (0x000300-0x0003FF) ---

/**
 * @brief 拍照、录像模式设置指令 (MsgID: 0x000300) - 请求
 * @note 对应文档 3.2.4.1
 */
typedef struct {
    uint8_t mode_setting; // 字节1: 模式切换 (0:拍照, 1:录像)
    uint8_t reserved;     // 字节2: 预留
} Payload_Photo_Video_Mode_Set_Req_0x000300_t;
// No ACK specified, assume Generic or none if not critical.

/**
 * @brief 拍照参数设置指令 (MsgID: 0x000301) - 请求
 * @note 对应文档 3.2.4.2
 */
typedef struct {
    uint8_t photo_function_setting; // 字节1: 0x00:正常单拍, 0x01:连拍3/5张, 0x02:延时拍照
    uint8_t timed_interval;         // 字节2: 延时间隔 (5/7/30/60)
    uint8_t burst_count;            // 字节3: 连拍张数 (3/5)
    uint8_t reserved;               // 字节4: 预留
} Payload_Photo_Param_Set_Req_0x000301_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 拍照指令 (MsgID: 0x000302) - 请求
 * @note 对应文档 3.2.4.3
 */
typedef struct {
    uint8_t photo_mode;        // 字节1: 拍照模式 (0x00:默认(红外+可见光), 0x01:红外, 0x02:可见光, 0x03:红外+可见光)
    uint8_t photo_command;     // 字节2: 相机拍照指令 (0x00:单拍/开始, 0x01:停止(仅连拍))
    char    folder_name[20];   // 字节3-22: 文件夹名 (NULL或""不使用)
    char    image_name[32];    // 字节23-54: 图片名 (NULL或""使用默认)
} Payload_Photo_Cmd_Req_0x000302_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 拍照指令 (MsgID: 0x000302) - 状态帧
 * @note 对应文档 3.2.4.3
 */
typedef struct {
    uint8_t  photo_mode_status; // 字节1: 0:默认, 1:红外, 2:可见光, 3:红外+可见光
    uint8_t  current_photo_feedback; // 字节2: 当前拍照反馈 (0:完成, 1:单拍中, 2:连拍中, etc.)
    uint16_t burst_count_feedback; // 字节3-4: 连拍张数
    uint8_t  reserved[3];          // 字节5-7: 预留
} StatusFrame_Photo_Cmd_0x000302_t;

/**
 * @brief 录像指令 (MsgID: 0x000303) - 请求
 * @note 对应文档 3.2.4.4
 */
typedef struct {
    uint8_t video_mode;        // 字节1: 录像模式 (0x00:默认(红外+可见光), 0x01:红外, 0x02:可见光, 0x03:红外+可见光, 0x04:视频流录屏)
    uint8_t video_command;     // 字节2: 0x01:开始录像, 0x02:停止录像
    char    folder_name[20];   // 字节3-22: 文件夹名
    char    video_name[32];    // 字节23-54: 视频名称
} Payload_Video_Cmd_Req_0x000303_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 录像指令 (MsgID: 0x000303) - 状态帧 (1Hz)
 * @note 对应文档 3.2.4.4
 */
typedef struct {
    uint8_t  current_video_mode_feedback; // 字节1: 当前录像模式反馈
    uint8_t  current_video_feedback;      // 字节2: 当前录像反馈 (0:停止, 1:正在录像, etc.)
    uint16_t recording_time;              // 字节3-4: 录像时间 (单位未指定, 假设秒)
    uint8_t  reserved[3];                 // 字节5-7: 预留
} StatusFrame_Video_Cmd_0x000303_t;

/**
 * @brief 指定混合变倍指令 (MsgID: 0x000304) - 请求
 * @note 对应文档 3.2.4.5
 */
typedef struct {
    uint8_t  start_setting;     // 字节1: 0:开始设置
    uint16_t hybrid_zoom_ratio; // 字节2-3: 指定混合变倍倍率*10 (1-160倍)
} Payload_Specify_Hybrid_Zoom_Req_0x000304_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 指定混合变倍指令 (MsgID: 0x000304) - 状态帧
 * @note 对应文档 3.2.4.5
 */
typedef struct {
    uint8_t zoom_status; // 字节1: 变倍状态 (0x01:变倍中, 0x00:变倍完成)
    uint8_t reserved;    // 字节2: 预留
} StatusFrame_Specify_Hybrid_Zoom_0x000304_t;

/**
 * @brief 连续混合变倍指令 (MsgID: 0x000306) - 请求
 * @note 对应文档 3.2.4.6
 */
typedef struct {
    uint8_t zoom_control; // 字节1: 0x00:连续放大, 0x01:连续缩小, 0x02:停止, 0x03:放大(单步), 0x04:缩小(单步)
    uint8_t reserved;     // 字节2: 预留
} Payload_Continuous_Hybrid_Zoom_Req_0x000306_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 指定相机精准复拍指令 (MsgID: 0x000307) - 请求
 * @note 对应文档 3.2.4.7
 */
typedef struct {
    uint8_t  photo_mode;          // 字节1: 拍照模式 (0x00:默认, 0x01:红外, 0x02:可见光, 0x03:红外+可见光)
    uint8_t  vl_photo_resolution; // 字节2: 可见光拍照分辨率 (0x14:8000*6000, etc.)
    uint16_t vl_zoom_ratio;       // 字节3-4: 可见光倍数, 单位0.1倍
    uint16_t vl_refocus_focal_length; // 字节5-6: 可见光精准复拍焦距
    char     folder_name[20];     // 字节7-26: 文件夹名称
    char     photo_name[32];      // 字节27-58: 照片名称
} Payload_Precise_Refocus_Photo_Req_0x000307_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 指定相机精准复拍指令 (MsgID: 0x000307) - 状态帧
 * @note 对应文档 3.2.4.7
 */
typedef struct {
    uint8_t photo_mode_status;        // 字节1: 拍照模式状态
    uint8_t current_photo_feedback;   // 字节2: 当前拍照反馈 (含12:正在精准复拍)
    uint8_t reserved[5];              // 字节3-7: 预留
} StatusFrame_Precise_Refocus_Photo_0x000307_t;

/**
 * @brief 视频输出码流设置指令 (MsgID: 0x000308) - 请求
 * @note 对应文档 3.2.4.8
 */
typedef struct {
    uint8_t bitrate_setting; // 字节1: 1:1M, 2:1.5M, 3:2M, 4:4M, 5:8M, 6:12M
    uint8_t reserved;        // 字节2: 预留
} Payload_Video_Bitrate_Set_Req_0x000308_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 视频输出分辨率设置指令 (MsgID: 0x00030A) - 请求
 * @note 对应文档 3.2.4.9
 */
typedef struct {
    uint8_t resolution_setting; // 字节1: 1:1080P30fps, 2:720P30fps(不支持)
    uint8_t reserved;           // 字节2: 预留
} Payload_Video_Output_Res_Set_Req_0x00030A_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 视频编码格式设置指令 (MsgID: 0x00030B) - 请求
 * @note 对应文档 3.2.4.10
 */
typedef struct {
    uint8_t encoding_format; // 字节1: 0:H264, 1:H265
    uint8_t reserved;        // 字节2: 预留
} Payload_Video_Encoding_Set_Req_0x00030B_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief TF卡升级指令 (MsgID: 0x00030C) - 请求
 * @note 对应文档 3.2.4.11
 */
typedef struct {
    uint8_t tf_upgrade_cmd; // 字节1: 1:开始TF卡升级, 0:无效
    uint8_t reserved;       // 字节2: 预留
} Payload_TF_Upgrade_Req_0x00030C_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief TF卡升级指令 (MsgID: 0x00030C) - 状态帧
 * @note 对应文档 3.2.4.11
 */
typedef struct {
    uint8_t upgrade_status; // 字节1: TF卡升级状态 (0x01:升级中, 0x02:失败, 0x00:成功)
    uint8_t reserved;       // 字节2: 预留
} StatusFrame_TF_Upgrade_0x00030C_t;

/**
 * @brief TF卡格式化指令 (MsgID: 0x00030D) - 请求
 * @note 对应文档 3.2.4.12
 */
typedef struct {
    uint8_t format_cmd; // 字节1: 0x01:格式化SD卡
    uint8_t reserved;   // 字节2: 预留
} Payload_TF_Format_Req_0x00030D_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief TF卡格式化指令 (MsgID: 0x00030D) - 状态帧 (5Hz)
 * @note 对应文档 3.2.4.12
 */
typedef struct {
    uint8_t  format_status; // 字节1: 0x00:完成, 0x01:格式化中, 0x02:失败, 0x03:SD卡不可用
    uint16_t reserved;      // 字节2-3: 预留
} StatusFrame_TF_Format_0x00030D_t;

/**
 * @brief 云台相机授时指令 (MsgID: 0x00030E) - 请求
 * @note 对应文档 3.2.4.13
 */
typedef struct {
    uint64_t timestamp_ms;     // 字节1-8: 时间戳 (1970至今毫秒数)
    int32_t  tz_minuteswest;   // 字节9-12: 时区 (格林威治以西分钟数)
    int32_t  tz_dsttime;       // 字节13-16: 夏令时校正类型
} Payload_Camera_Time_Sync_Req_0x00030E_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 恢复出厂设置指令 (MsgID: 0x00030F) - 请求
 * @note 对应文档 3.2.4.14
 */
typedef struct {
    uint8_t factory_reset_cmd; // 字节1: 0x01:恢复出厂设置
    uint8_t reserved;          // 字节2: 预留
} Payload_Factory_Reset_Req_0x00030F_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 相机向飞控请求GPS信息指令 (MsgID: 0x000310) - 请求 (相机发给飞控)
 * @note 对应文档 3.2.4.15
 */
typedef struct {
    uint8_t  lens_enable; // 字节1: 镜头使能 (0:未使能, 1:使能)
    uint8_t  hour;        // 字节2: 时 (0-23)
    uint8_t  minute;      // 字节3: 分 (0-59)
    uint8_t  second;      // 字节4: 秒 (0-59)
    uint16_t millisecond; // 字节5-6: 毫秒 (0-999)
} Payload_Camera_Req_GPS_From_FC_Req_0x000310_t;

/**
 * @brief 相机向飞控请求GPS信息指令 (MsgID: 0x000310) - ACK (飞控发给云台/相机)
 * @note 对应文档 3.2.4.15
 */
typedef struct {
    uint16_t response_code;    // 字节1-2: 响应码
    uint8_t  hour_echo;        // 字节3: 时 (同请求)
    uint8_t  minute_echo;      // 字节4: 分 (同请求)
    uint8_t  second_echo;      // 字节5: 秒 (同请求)
    uint16_t millisecond_echo; // 字节6-7: 毫秒 (同请求)
    int32_t  longitude;        // 字节8-11: 经度 * 10^7, int32
    int32_t  latitude;         // 字节12-15: 纬度 * 10^7, int32
    int16_t  relative_altitude; // 字节16-17: 相对高度 * 10 (m), int16
    int16_t  absolute_altitude; // 字节18-19: 海拔高度 * 10 (m), int16
    // 飞机姿态信息
    int16_t  plane_yaw_angle;    // 字节20-21: 飞机方位角度 * 100, int16
    int16_t  plane_roll_angle;   // 字节22-23: 飞机横滚角度 * 100, int16
    int16_t  plane_pitch_angle;  // 字节24-25: 飞机俯仰角度 * 100, int16
    // 云台姿态信息
    int16_t  gimbal_yaw_angle;   // 字节26-27: 云台方位角度 * 100, int16
    int16_t  gimbal_roll_angle;  // 字节28-29: 云台横滚角度 * 100, int16
    int16_t  gimbal_pitch_angle; // 字节30-31: 云台俯仰角度 * 100, int16
} Ack_Camera_Req_GPS_From_FC_0x000310_t;

/**
 * @brief 相机IP地址设置指令 (MsgID: 0x000311) - 请求
 * @note 对应文档 ********
 */
typedef struct {
    uint8_t ip_type;          // 字节1: 0:静态, 1:动态
    uint8_t ip_address[4];    // 字节2-5: IP地址 (例: {20, 1, 192, 145} -> ************)
    uint8_t subnet_mask[4];   // 字节6-9: 子网掩码
    uint8_t default_gateway[4]; // 字节10-13: 默认网关
} Payload_Camera_IP_Set_Req_0x000311_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 相机IP地址获取指令 (MsgID: 0x000312) - 请求
 * @note 对应文档 ********
 */
typedef struct {
    uint8_t query_ip_cmd; // 字节1: 1:查询IP地址
    uint8_t reserved;     // 字节2: 预留
} Payload_Camera_IP_Get_Req_0x000312_t;

/**
 * @brief 相机IP地址获取指令 (MsgID: 0x000312) - ACK
 * @note 对应文档 ********
 */
typedef struct {
    uint16_t response_code;    // 字节1-2: 响应码
    uint8_t  ip_address[4];    // 字节3-6: IP地址
    uint8_t  subnet_mask[4];   // 字节7-10: 子网掩码
    uint8_t  default_gateway[4]; // 字节11-14: 默认网关
} Ack_Camera_IP_Get_0x000312_t;

/**
 * @brief 调焦指令 (MsgID: 0x000313) - 请求
 * @note 对应文档 ********
 */
typedef struct {
    uint8_t  focus_setting;       // 字节1: 0x00:自动, 0x01:手动微调+, 0x02:手动微调-, 0x03:停止, 0x04:区域自动, 0x05:一键, 0x06:开变倍后自动, 0x07:关变倍后自动
    uint16_t area_focus_x1;       // 字节2-3: 区域自动对焦框左上X
    uint16_t area_focus_y1;       // 字节4-5: 区域自动对焦框左上Y
    uint16_t area_focus_x2;       // 字节6-7: 区域自动对焦框右下X
    uint16_t area_focus_y2;       // 字节8-9: 区域自动对焦框右下Y
    uint8_t  reserved;            // 字节10: 预留
} Payload_Focus_Cmd_Req_0x000313_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 相机OSD水印开关 (MsgID: 0x000314) - 请求
 * @note 对应文档 ********
 */
typedef struct {
    uint8_t watermark_switch; // 字节1: 水印开关 (0:关, 1:开) (总开关)
    uint8_t reserved1;        // 字节2: 预留
    uint8_t reserved2;        // 字节3: 预留
} Payload_OSD_Watermark_Switch_Req_0x000314_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 相机OSD水印自定义文案 (MsgID: 0x000315) - 请求
 * @note 对应文档 3.2.4.20
 */
typedef struct {
    char     custom_text[60]; // 字节1-60: 水印自定义文案
    uint16_t x_coord;         // 字节61-62: X坐标点 (0-1920)
    uint16_t y_coord;         // 字节63-64: Y坐标点 (0-1080)
    uint8_t  font_size;       // 字节65: 字体大小 (1-50)
    uint8_t  watermark_switch; // 字节66: 水印开关 (0:关, 1:开) (针对此条)
    uint8_t  watermark_id;    // 字节67: 水印序号 (1-10)
    uint8_t  reserved;        // 字节68: 预留
} Payload_OSD_Custom_Text_Req_0x000315_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 相机关机指令 (MsgID: 0x000316) - 请求
 * @note 对应文档 3.2.4.21
 */
typedef struct {
    uint8_t shutdown_cmd; // 字节1: 0x01:即将关机
    uint8_t reserved;     // 字节2: 预留
} Payload_Camera_Shutdown_Req_0x000316_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 获取相机版本号 (MsgID: 0x000317) - 请求
 * @note 对应文档 3.2.4.22
 */
typedef struct {
    uint8_t get_version_cmd; // 字节1: 0x01:获取相机版本号
    uint8_t reserved;        // 字节2: 预留
} Payload_Camera_Get_Version_Req_0x000317_t;

/**
 * @brief 获取相机版本号 (MsgID: 0x000317) - ACK
 * @note 对应文档 3.2.4.22
 */
typedef struct {
    uint16_t response_code; // 字节1-2: 响应码
    uint8_t  major_version; // 字节3: 主版本号
    uint8_t  minor_version; // 字节4: 次版本号
    uint8_t  patch_version; // 字节5: 小版本号
} Ack_Camera_Get_Version_0x000317_t;

/**
 * @brief 图像模式设置指令 (MsgID: 0x000318) - 请求
 * @note 对应文档 3.2.4.23
 */
typedef struct {
    uint8_t image_mode; // 字节1: 0x00:红外, 0x05/0x06:可见光, 0x07:分屏
    uint8_t reserved;   // 字节2: 预留
} Payload_Image_Mode_Set_Req_0x000318_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief AI智能识别目标数据结构 (用于状态帧)
 */
typedef struct {
    uint16_t top_left_x;  // 左上角X
    uint16_t top_left_y;  // 左上角Y
    uint16_t width;       // 宽
    uint16_t height;      // 高
    uint8_t  confidence;  // 目标置信度 (conf*100)
    uint8_t  class_id;    // 类别id
} AI_Target_Data_t;

/**
 * @brief 智能识别指令 (MsgID: 0x000319) - 请求
 * @note 对应文档 3.2.4.24
 */
typedef struct {
    uint8_t recognition_switch; // 字节1: 识别开关 (0x01:开, 0x02:关)
    uint8_t model_type;         // 字节2: 指定加载的模型类型 (默认0x00 for yolov)
    uint8_t target_classes[10]; // 字节3-12: 同时识别的目标类别 (最多10个)
} Payload_AI_Recognition_Req_0x000319_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 智能识别指令 (MsgID: 0x000319) - 状态帧 (40ms上报一次)
 * @note 对应文档 3.2.4.24. N为目标数量, 单包最多24个目标.
 * @note 此结构体仅表示一个可能的数据包格式，实际处理时需要根据目标数量动态处理。
 * 这里定义一个最大可能情况。
 */
#define MAX_AI_TARGETS_PER_PACKET 24 // 根据文档单包最多24个目标
typedef struct {
    AI_Target_Data_t targets[MAX_AI_TARGETS_PER_PACKET]; // 多/单目标数据
    uint8_t          packet_info;  // 高4位:总包数, 低4位:当前包ID (当目标数 > MAX_AI_TARGETS_PER_PACKET时使用)
    uint8_t          detect_status; // 检测状态 (0x00:成功, 0x02:未检测到)
    // 注意：实际payload中 targets的数量由具体识别结果决定，packet_info用于分包
} StatusFrame_AI_Recognition_0x000319_t;


/**
 * @brief 飞控请求目标GPS信息指令 (MsgID: 0x000320) - 请求 (飞控发给相机)
 * @note 对应文档 3.2.4.25
 */
typedef struct {
    uint8_t  mount_request;       // 字节1: 请求挂载 (Bit0-7对应挂载1-8, 1请求,0未请求)
    uint8_t  gps_status;          // 字节2: GPS状态 (0:未连接, ..., 6:固定RTK, etc.)
    uint64_t utc_timestamp_ms;    // 字节3-10: UTC时间戳, 毫秒, 低字节在前
    int32_t  longitude_deg_e7;    // 字节11-14: 经度角度*10^7, int32, 低字节在前
    int32_t  latitude_deg_e7;     // 字节15-18: 纬度角度*10^7, int32, 低字节在前
    int32_t  relative_alt_mm;     // 字节19-22: 相对高度*1000 (m -> mm), int32, 低字节在前
    int32_t  absolute_alt_mm;     // 字节23-26: 海拔高度*1000 (m -> mm), int32, 低字节在前
    int16_t  plane_yaw_deg_e2;    // 字节27-28: 飞机方位角度*100, int16, 低字节在前
    int16_t  plane_roll_deg_e2;   // 字节29-30: 飞机横滚角度*100, int16, 低字节在前
    int16_t  plane_pitch_deg_e2;  // 字节31-32: 飞机俯仰角度*100, int16, 低字节在前
    uint16_t airspeed_mps_e2;     // 字节33-34: 空速*100 (m/s), uint16, 低字节在前
    uint16_t groundspeed_mps_e2;  // 字节35-36: 地速*100 (m/s), uint16, 低字节在前
    uint16_t heading_deg_e2;      // 字节37-38: 航向*100 (0-360, 0北), uint16, 低字节在前
    uint16_t throttle_percent_e2; // 字节39-40: 油门*100 (0-100%), uint16, 低字节在前
    int16_t  climb_rate_mps_e2;   // 字节41-42: 爬升速率*100 (m/s), int16, 低字节在前
} Payload_FC_Req_Target_GPS_Req_0x000320_t;

/**
 * @brief 飞控请求目标GPS信息指令 (MsgID: 0x000320) - ACK (相机回复给飞控)
 * @note 对应文档 3.2.4.25
 */
typedef struct {
    uint16_t response_code;      // 字节1-2: ACK响应码
    uint8_t  mount_enable;       // 字节3: 挂载使能 (固定0x1f)
    uint64_t utc_timestamp_ms;   // 字节4-11: UTC时间戳, 毫秒, 低字节在前
    int32_t  longitude_deg_e7;   // 字节12-15: 经度角度*10^7, int32, 低字节在前
    int32_t  latitude_deg_e7;    // 字节16-19: 纬度角度*10^7, int32, 低字节在前
    int32_t  relative_alt_mm;    // 字节20-23: 相对高度*1000 (m -> mm), int32, 低字节在前
    int32_t  absolute_alt_mm;    // 字节24-27: 海拔高度*1000 (m -> mm), int32, 低字节在前
} Ack_FC_Req_Target_GPS_0x000320_t;

/**
 * @brief 框选目标追踪指令 (MsgID: 0x000324) - 请求
 * @note 对应文档 3.2.4.26
 */
typedef struct {
    uint8_t  tracking_switch; // 字节1: 0x01:开启框选, 0x02:关闭框选
    uint16_t target_box_x;    // 字节2-3: 目标框左上点X (0-1920)
    uint16_t target_box_y;    // 字节4-5: 目标框左上点Y (0-1088)
    uint16_t target_box_w;    // 字节6-7: 目标框宽度
    uint16_t target_box_h;    // 字节8-9: 目标框高度
    uint8_t  reserved;        // 字节10: 预留
} Payload_Box_Select_Track_Req_0x000324_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 框选目标追踪指令 (MsgID: 0x020324) - 状态帧 (40ms上报一次)
 * @note 对应文档 3.2.4.26
 */
typedef struct {
    AI_Target_Data_t target_data;   // 单目标数据 (10字节)
    uint8_t          packet_info;   // 高4位:总包数, 低4位:当前包ID (此处单目标，可能固定为0x01或0x11)
    uint8_t          detect_status; // 检测状态 (0x00:成功, 0x02:未检测到)
} StatusFrame_Box_Select_Track_0x000324_t;


// --- 激光载荷协议 (Sysid: 0x04, MsgID prefix 0x0004xx) ---

/**
 * @brief 激光测距设置指令 (MsgID: 0x000400) - 请求
 * @note 对应文档 3.2.5.1
 */
typedef struct {
    uint8_t ranging_setting; // 字节1: 0:不开启, 1:开启单次测距
    uint8_t reserved;        // 字节2: 预留
} Payload_Laser_Ranging_Set_Req_0x000400_t;

/**
 * @brief 激光测距设置指令 (MsgID: 0x000400) - ACK
 * @note 对应文档 3.2.5.1
 */
typedef struct {
    uint16_t response_code;   // 字节1-2: 响应码
    uint16_t distance_dm;     // 字节3-4: 激光测距反馈, uint16, 单位0.1米 (无值反馈0)
} Ack_Laser_Ranging_Set_0x000400_t;

/**
 * @brief 激光周期测距设置指令 (MsgID: 0x000406) - 请求
 * @note 对应文档 3.2.5.2
 */
typedef struct {
    uint8_t periodic_ranging_set; // 字节1: 0:不开启, 1:开启1s周期测距
    uint8_t reserved;             // 字节2: 预留
} Payload_Laser_Periodic_Ranging_Set_Req_0x000406_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief 激光周期测距设置指令 (MsgID: 0x000406) - 状态帧
 * @note 对应文档 3.2.5.2
 */
typedef struct {
    // uint16_t response_code; // 文档状态帧描述为 Byte1-2: 0x00响应码，这通常是ACK的一部分
                               // 更可能是直接上报距离
    uint16_t distance_dm;     // 字节3-4 (或 1-2 如果无response_code): 激光测距距离, uint16, 单位0.1米
} StatusFrame_Laser_Periodic_Ranging_0x000406_t;


// --- SBUS通道协议 (Sysid: 0x04, MsgID prefix 0x0005xx) ---

/**
 * @brief SBUS通道值范围设置指令 (MsgID: 0x000500) - 请求
 * @note 对应文档 3.2.6.1
 */
typedef struct {
    uint16_t max_value; // 字节1-2: 最大值 (0-2047)
    uint16_t min_value; // 字节3-4: 最小值 (0-2047, 小于最大值)
    uint8_t  reserved;  // 字节5: 预留
} Payload_SBUS_Value_Range_Set_Req_0x000500_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief SBUS通道配置指令 (MsgID: 0x000501) - 请求
 * @note 对应文档 3.2.6.2
 */
typedef struct {
    uint8_t stream_channel;  // 字节1: 推流设置通道 (0-15 -> CH1-16)
    uint8_t zoom_channel;    // 字节2: 变倍设置通道 (0-15)
    uint8_t photo_channel;   // 字节3: 拍照设置通道 (0-15)
    uint8_t video_channel;   // 字节4: 录像设置通道 (0-15) (文档中Byte3重复，应为Byte4)
    uint8_t gimbal_pitch_channel; // 字节5: 云台俯仰通道 (0-15) (文档中Byte3重复，应为Byte5)
    uint8_t gimbal_yaw_channel;   // 字节6: 云台航向通道 (0-15) (文档中Byte3重复，应为Byte6)
    uint8_t gimbal_recenter_channel; // 字节7: 云台回中通道 (0-15) (文档中Byte3重复，应为Byte7)
    uint8_t reserved;        // 字节8: 预留 (文档中Byte3重复，应为Byte8)
} Payload_SBUS_Channel_Config_Req_0x000501_t;
// ACK: Ack_Generic_Response_t

/**
 * @brief SBUS配置获取指令 (MsgID: 0x000502) - 请求
 * @note 对应文档 3.2.6.3
 */
typedef struct {
    uint8_t get_sbus_config_cmd; // 字节1: 0x01:请求获取sbus配置
    uint8_t reserved;            // 字节2: 预留
} Payload_SBUS_Config_Get_Req_0x000502_t;

/**
 * @brief SBUS配置获取指令 (MsgID: 0x000502) - 返回值 ( يعتبر ACK)
 * @note 对应文档 3.2.6.3
 */
typedef struct {
    uint16_t response_code;    // 字节1-2: 响应码
    uint16_t sbus_max_value;   // 字节3-4: SBUS设置最大值
    uint16_t sbus_min_value;   // 字节5-6: SBUS设置最小值
    uint8_t  stream_channel;   // 字节7: 推流设置通道
    uint8_t  zoom_channel;     // 字节8: 变倍设置通道
    uint8_t  photo_channel;    // 字节9: 拍照设置通道
    uint8_t  video_channel;    // 字节10: 录像设置通道
    uint8_t  gimbal_pitch_channel; // 字节11: 云台俯仰通道
    uint8_t  gimbal_yaw_channel;   // 字节12: 云台航向通道
    uint8_t  gimbal_recenter_channel; // 字节13: 云台回中通道
    uint8_t  reserved;         // 字节14: 预留
} Return_SBUS_Config_Get_0x000502_t;


//#pragma pack(pop) // 恢复默认对齐













//---------------------------custom---------------------------

enum trackState
{
    TRACKER_TRACKING = 1,
    TRACKER_FINDING = 2,
    TRACKER_LOST = 3,
    TRACKER_NOT_UPDATED = 4
};


/// @brief heq heartbeat data
typedef struct heq_heartbeat_data
{
    //msg head
    unsigned short head;
    //length
    unsigned char len;
    //frame type
    unsigned char type;
    //fpv mode ,0 Altitude Hold mode, 1 Position Hold Mode, 2 Attack Mode
    unsigned char mode;
    //battery voltage 0-5.0v, The number is magnified by a factor of 10
    unsigned char voltage;

    //The number is magnified by a factor of 100
    //飞行高度（0~500）
    unsigned short height;

    //云台pitch角度 ±90
    short neu;

    //飞机pitch角度 ±90
    short ned;

    //flight speed
    //飞行速度(0~40),如果此值为-1 那OSD上就显示NA
    unsigned short speed;

    //compass calibration status
    //指南针校准状态:         uint16_t
    //bit 0~3:校准状态 0 不显示指南针校准 1正在校准 2 校准成功 3 校准失败
    //bit 4~5:上方校准状态：1 Up未校准（显示红色） 2 Up正在校准（黄色） 3 Up校准完成（绿色）
    //bit 6~7:1 Down未校准（显示红色） 2 Down正在校准（黄色） 3 Down校准完成（绿色）
    //bit 8~9:1 Front未校准（显示红色） 2 Front正在校准（黄色） 3 Front校准完成（绿色）
    //bit 10~11:1 Back未校准（显示红色） 2 Back正在校准（黄色） 3 Back校准完成（绿色）
    //bit 12~13:1 Left未校准（显示红色） 2 Left正在校准（黄色） 3 Left校准完成（绿色）
    //bit 14~15:1 Right未校准（显示红色） 2 Right正在校准（黄色） 3 Right校准完成（绿色）
    unsigned short compass_calibration_status;
    
}heq_heartbeat_data_s;

// 指南针校准状态结构体
typedef struct {
    unsigned short calibrationStatus : 4;  // bit 0~3: 校准状态
    unsigned short upStatus          : 2;  // bit 4~5: 上方校准状态
    unsigned short downStatus        : 2;  // bit 6~7: 下方校准状态
    unsigned short frontStatus       : 2;  // bit 8~9: 前方校准状态
    unsigned short backStatus        : 2;  // bit 10~11: 后方校准状态
    unsigned short leftStatus        : 2;  // bit 12~13: 左方校准状态
    unsigned short rightStatus       : 2;  // bit 14~15: 右方校准状态
} CompassCalibrationStatus;



/// @brief heq dual light
typedef struct heq_dual_light_data
{
    //msg head
    unsigned short head;
    //length
    unsigned char len;
    //frame type
    unsigned char type;

    unsigned short dx;
    unsigned short dy;
    unsigned short switch_zoom_factor;
    
}heq_dual_light_data_s;





typedef enum ZOOM_CMD_TYPE
{
    E_ZOOM_BUTT = 0,
    E_ZOOM_TO_DEFAULT = 0x01,
    E_ZOOM_ADD_1X = 0x02,
    E_ZOOM_SUB_1X = 0x03,
    E_ZOOM_ADD_10X = 0x04,
    E_ZOOM_SUB_10X = 0x05,
    E_ZOOM_ADD_100X = 0x06,
    E_ZOOM_SUB_100X = 0x07,
    E_ZOOM_TO_SPECIFIC = 0x08,

}ZOOM_CMD_TYPE_E;


typedef struct zoom_msg
{

    ZOOM_CMD_TYPE_E action_type;
    //when action_type is E_ZOOM_TO_SPECIFIC, this field is valid
    //0x34 => 52 =>5.2
    int factor;

}zoom_msg_s;


typedef enum DUALLIGHT_DXDY_TYPE
{
    E_DUALLIGHT_ADD_LONG_LENS_X = 0x01,
    E_DUALLIGHT_SUB_LONG_LENS_X = 0x02,
    E_DUALLIGHT_ADD_LONG_LENS_Y = 0x03,
    E_DUALLIGHT_SUB_LONG_LENS_Y = 0x04,

}DUALLIGHT_DXDY_TYPE_E;

typedef struct heq_dual_light_msg
{
    DUALLIGHT_DXDY_TYPE_E action_type;
}heq_dual_light_msg_s;




#pragma pack(pop) 
//---------------------------end---------------------------






// void camera_photo_ack(tracking_camera_photo_struct *req, uint8_t result);

// void camera_record_ack(tracking_camera_record_struct *req, uint8_t result);

// void camera_visible_zoom_ack(tracking_camera_visible_zoom_struct *req, uint8_t result);
// void camera_thermal_zoom_ack(tracking_camera_thermal_zoom_struct *req, uint8_t result);
// void camera_video_type_ack(tracking_camera_video_type_struct *req, uint8_t result);

// void start_track_ack(tracking_camera_start_track_struct *req, uint8_t result);

// void camera_stop_track(tracking_camera_stop_track_struct *req, uint8_t result);

// void camera_track_result(uint8_t state, uint16_t x, uint16_t y, uint16_t w, uint16_t h);



void start_fc();

/**
 * @brief 封装一个通用的ACK响应消息 (支持自定义载荷结构体)
 * @param dest_sys_id       目标系统ID
 * @param dest_comp_id      目标组件ID
 * @param src_sys_id        源系统ID (本机发送方)
 * @param src_comp_id       源组件ID (本机发送方)
 * @param seq               本条消息的序列号
 * @param ack_msg_id        要发送的ACK消息的完整MSG ID
 * @param ack_payload_data  指向ACK载荷数据的指针 (可以是任何结构体)
 * @param ack_payload_len   ACK载荷数据的长度 (字节)
 * @param out_buffer        用于存放封装好的消息帧的缓冲区指针
 * @param out_buffer_max_len out_buffer 的最大长度
 * @return int              成功则返回封装后消息帧的实际长度，失败则返回0或负数错误码
 */
int package_custom_ack(
    uint8_t dest_sys_id,
    uint8_t dest_comp_id,
    uint8_t src_sys_id,
    uint8_t src_comp_id,
    uint8_t seq,
    uint32_t ack_msg_id,
    const void *ack_payload_data,
    uint8_t ack_payload_len,
    uint8_t *out_buffer,
    int out_buffer_max_len);

/**
 * @brief 封装一个通用的ACK响应消息 (原有函数，保持兼容性)
 * @param dest_sys_id       目标系统ID
 * @param dest_comp_id      目标组件ID
 * @param src_sys_id        源系统ID (本机发送方)
 * @param src_comp_id       源组件ID (本机发送方)
 * @param seq               本条消息的序列号
 * @param ack_msg_id        要发送的ACK消息的完整MSG ID
 * @param response_code     响应码 (例如 0x0000 表示成功)
 * @param out_buffer        用于存放封装好的消息帧的缓冲区指针
 * @param out_buffer_max_len out_buffer 的最大长度
 * @return int              成功则返回封装后消息帧的实际长度，失败则返回0或负数错误码
 */
int package_generic_ack(
    uint8_t dest_sys_id,
    uint8_t dest_comp_id,
    uint8_t src_sys_id,
    uint8_t src_comp_id,
    uint8_t seq,
    uint32_t ack_msg_id,
    uint16_t response_code,
    uint8_t *out_buffer,
    int out_buffer_max_len);

/**
 * @brief 上报云台姿态信息的云台姿态信息 (MsgID: 0x000002)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_gimbal_attitude_message_report( uint16_t joint_yaw_angle,        // 字节1-2: 云台偏航角度(关节角), 无符号整型, 单位: 度*100
    uint16_t joint_roll_angle,       // 字节3-4: 云台横滚角度(关节角), 无符号整型, 单位: 度*100
    uint16_t joint_pitch_angle,      // 字节5-6: 云台俯仰角度(关节角), 无符号整型, 单位: 度*100
    uint16_t attitude_yaw_angle,     // 字节7-8: 云台偏航角度(姿态角), 无符号整型, 单位: 度*100
    uint16_t attitude_roll_angle,    // 字节9-10: 云台横滚角度(姿态角), 无符号整型, 单位: 度*100
    uint16_t attitude_pitch_angle,   // 字节11-12: 云台俯仰角度(姿态角), 无符号整型, 单位: 度*100
    int16_t  yaw_angular_velocity,   // 字节13-14: 云台偏航角速度, int16, 单位: (度/秒)*100
    int16_t  pitch_angular_velocity, // 字节15-16: 云台俯仰角速度, int16, 单位: (度/秒)*100
    int16_t  roll_angular_velocity  // 字节17-18: 云台横滚角速度, int16, 单位: (度/秒)*100)
    );


/**
 * @brief 处理AI追踪状态帧 (MSG_ID_STATE_AI_TRACKING 0x020324)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_state_ai_tracking(  uint16_t top_left_x,  // 左上角X
                                uint16_t top_left_y,  // 左上角Y
                                uint16_t width,       // 宽
                                uint16_t height,      // 高
                                uint8_t  confidence,  // 目标置信度 (conf*100)
                                uint8_t  class_id    // 类别id
    );


/**
 * @brief 处理AI识别状态帧 (MSG_ID_STATE_AI_DETECTION 0x020319)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_state_ai_detection(StatusFrame_AI_Recognition_0x000319_t *payload);



/**
 * @brief 处理相机系统状态反馈 (MSG_ID_CAM_SYS_STATUS 0x000003)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_cam_sys_status_report(Payload_Camera_System_Status_0x000003_t *payload);


/**
 * @brief 处理红外相机状态反馈 (MSG_ID_IR_STATUS 0x000004)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_ir_status_report(Payload_IR_Camera_Status_0x000004_t *payload);

/**
 * @brief 处理可见光相机状态反馈 (MSG_ID_VL_CAM_STATUS 0x000005)
 * @param data 接收到的完整协议帧数据指针
 */
void handle_visible_status_report( Payload_VL_Camera_Status_0x000005_t * payload );

/**
 * @brief 向飞控请求GPS信息 (MSG_ID_GPS_REQUEST 0x000310)
 * @return 0:成功, -1:失败
 */
int send_gps_request_to_fc(void);


#ifdef __cplusplus
}
#endif


#endif
