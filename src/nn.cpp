#include <stdint.h>
#include <stdlib.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <signal.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <errno.h>
#include <dlfcn.h>
#include <pthread.h>
#include <linux/videodev2.h>
#include "sample_common.h"
#include "sample_common_infra.h"
#include "configparser.h"
#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"
#include "nn.h"
#include "denoise.h"
#include <opencv2/highgui/highgui_c.h>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include "image_file_util.h"

#ifdef SEND_DETECT_RESULT_VIA_SERIAL
//#include "../tracker/Serial.hpp"
extern Serial* g_ptrSerial;
#include <sys/prctl.h>
#endif

using namespace std;
using namespace cv;

#ifdef NN_ENABLED
/*******************************************extern*******************************************/
extern vs_int8_t                g_bus_id[VII_MAX_ROUTE_NUM];
extern sample_sensor_type_e     g_sensor_type[VII_MAX_ROUTE_NUM];

/*******************************************global*******************************************/
#ifdef VS_ORION
static vs_uint32_t              g_disp_output_type = 0;
#endif
static vs_bool_t                g_rotation_flag = VS_FALSE;
// singal handle to stop
static vs_uint32_t              g_buffer_dimension = 1;

extern vs_size_s s_vpp_sr_size;

extern volatile sig_atomic_t    g_stop_flag;
extern vs_size_s s_vo_size;
extern vs_size_s s_vpp_size;
extern int denoiseenable;
extern int srenable;
extern int detectenable;
extern int showdebuglogo;

/*****************************************functions*****************************************/
vs_void_t nn_signal_handle(vs_int32_t s_no)
{
    (vs_void_t)s_no;
    g_stop_flag = 1;
}

vs_void_t nn_register_signal_handler(void (*sig_handler)(int))
{
    struct sigaction sa;
    memset(&sa, 0, sizeof(struct sigaction));
    sa.sa_handler = sig_handler;
    sa.sa_flags = 0;

    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
}

vs_int32_t phy_virt_malloc(const vs_char_t *mmz_name, const vs_char_t *mmb_name,
                           vs_uint64_t *phys_addr, vs_void_t **virt_addr, vs_uint32_t len)
{
    vs_int32_t ret = VS_SUCCESS;
    ret = vs_mal_mmz_alloc(mmz_name, mmb_name, len, phys_addr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_mmz_alloc ret[0x%x]\n", ret);
        return ret;
    }
    *virt_addr = vs_mal_sys_mmap(*phys_addr, len);
    if (*virt_addr == VS_NULL) {
        vs_sample_trace("vs_mal_sys_mmap fail to mmap!\n");
        return VS_FAILED;
    }
    memset(*virt_addr, 0, len);
    return ret;
}

vs_int32_t phy_virt_free(vs_uint64_t phys_addr, vs_void_t *virt_addr,vs_uint32_t len)
{
    vs_int32_t ret = VS_SUCCESS;
    ret = vs_mal_sys_unmap(virt_addr, len);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_sys_unmap ret[0x%x]\n", ret);
    }
    ret = vs_mal_mmz_free(phys_addr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_mmz_free ret[0x%x]\n", ret);
    }
    return ret;
}

//sensor->vii->isp->vpp|->vo
//                     |->nn
vs_void_t nn_default_producer_info_get(sample_nn_producer_info_s *p_producer_info)
{
    memset(p_producer_info, 0, sizeof(sample_nn_producer_info_s));
    
    p_producer_info->vb_cnt = 18;
    p_producer_info->vb_pool_cnt = 1;
    p_producer_info->sensor_type = 0;
    // zk  rgb
    p_producer_info->sensor_id = 0;
    p_producer_info->vii_pipeid = 1;
    p_producer_info->vii_chnid = 0;
    p_producer_info->vpp_grpid = 1;
    p_producer_info->vpp_chnid_vo_ext = 5;
    p_producer_info->vpp_chnid_vo = 0;
    p_producer_info->vpp_chnid_nn = 1;
    memset(p_producer_info->vpp_chn_enable, 0, sizeof(p_producer_info->vpp_chn_enable));
    //memset(p_producer_info->vpp_extchn_enable, 0 ,sizeof(p_producer_info->vpp_extchn_enable));
    //p_producer_info->vpp_chn_enable[p_producer_info->vpp_chnid_nn] = VS_TRUE;
    p_producer_info->vpp_chn_enable[p_producer_info->vpp_chnid_vo] = VS_TRUE;
    //p_producer_info->vpp_extchn_enable[p_producer_info->vpp_chnid_vo_ext] = VS_TRUE;
    p_producer_info->vo_layerid = 0;
    p_producer_info->vo_chnid = 0;
    p_producer_info->dsp_id = 0;

    memset(&p_producer_info->vii_cfg, 0, sizeof(p_producer_info->vii_cfg));
    memset(&p_producer_info->vo_config, 0, sizeof(p_producer_info->vo_config));
}

vs_void_t nn_set_vpp_grp_attr(vs_size_s *img_size, vs_vpp_grp_attr_s *grp_attr)
{
    grp_attr->max_width = img_size->width;
    grp_attr->max_height = img_size->height;
    grp_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    grp_attr->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    grp_attr->framerate.src_framerate = -1;
    grp_attr->framerate.dst_framerate = -1;
}

vs_void_t nn_set_vpp_chn_attr(vs_size_s *img_size,
                                            vs_int32_t src_frame_rate,
                                            vs_int32_t dst_frame_rate,
                                            vs_vpp_chn_attr_s *chn_attr)
{
    chn_attr->chn_mode = E_VPP_CHN_MODE_USER;
    chn_attr->width = img_size->width;
    chn_attr->height = img_size->height;
    chn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
    chn_attr->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    chn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    chn_attr->compress_mode = E_COMPRESS_MODE_NONE;
    chn_attr->framerate.src_framerate = src_frame_rate;
    chn_attr->framerate.dst_framerate = dst_frame_rate;
    chn_attr->mirror_enable = VS_FALSE;
    chn_attr->flip_enable = VS_FALSE;
    chn_attr->depth = 3;
    chn_attr->aspect_ratio.mode = E_ASPECT_RATIO_MODE_NONE;
}



vs_void_t nn_set_vo_cfg(vs_size_s *img_size, sample_vo_cfg_s *vo_config)
{
    vo_config->vo_devid = 0;
    vo_config->vo_layerid = 0;
    vo_config->bg_color = 0;
    vo_config->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    vo_config->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    vo_config->vo_mode = E_VO_MODE_1MUX;
    vo_config->img_width = img_size->width;
    vo_config->img_height = img_size->height;
    vo_config->zorder = 0;
    vo_config->enable = VS_TRUE;

#ifdef VS_ORION
    switch (g_disp_output_type) {
    case 0:
        vo_config->vo_intf_type = E_VO_INTERFACE_TYPE_MIPI;
        vo_config->vo_output = E_VO_OUTPUT_TYPE_USER;
        vo_config->img_width = img_size->height;
        vo_config->img_height = img_size->width;
        g_rotation_flag = VS_TRUE;
        break;
    case 1:
        vo_config->vo_intf_type = E_VO_INTERFACE_TYPE_BT1120;
        vo_config->vo_output = E_VO_OUTPUT_TYPE_1080P60;
        break;
    case 2:
        vo_config->vo_intf_type = E_VO_INTERFACE_TYPE_BT1120;
        vo_config->vo_output = E_VO_OUTPUT_TYPE_1080I60;
        break;
    default:
        vo_config->vo_intf_type = E_VO_INTERFACE_TYPE_BT1120;
        vo_config->vo_output = E_VO_OUTPUT_TYPE_1080P60;
        break;
    }
#else
    vo_config->vo_intf_type = E_VO_INTERFACE_TYPE_HDMI;
    if (img_size->width > 3840) {
        vo_config->vo_output = E_VO_OUTPUT_TYPE_3840x2160_60;
    } else if (((img_size->width == 3840) && (img_size->height == 2160))
        || ((img_size->width == 2560) && (img_size->height == 1440))
        || ((img_size->width == 2688) && (img_size->height == 1520))) {
        vo_config->vo_output = E_VO_OUTPUT_TYPE_3840x2160_30;
    } else {
        vo_config->vo_output = E_VO_OUTPUT_TYPE_1080P60;
    }
#endif
}





vs_int32_t nn_gpe_frame_osd(const vs_video_frame_info_s *p_frame, const char* osd_bmp_path, int start_x, int start_y, int w = 128, int h = 128)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_int32_t job_handle = 0;
    ret = vs_mal_gpe_job_start(&job_handle);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_job_start ret[0x%x], job_handle: %d\n", ret, job_handle);
        return ret;
    }

    vs_task_attr_s task_attr = {0};
    vs_task_attr_s *p_task_attr = &task_attr;

    p_task_attr->src_frame = *p_frame;
    p_task_attr->dst_frame = *p_frame;

    vs_gpe_osd_attr_s *p_osd_attr = (vs_gpe_osd_attr_s*) malloc(sizeof(vs_gpe_osd_attr_s));
    p_osd_attr->enable = VS_TRUE;
    p_osd_attr->rect.x = start_x;
    p_osd_attr->rect.y = start_y;
    p_osd_attr->rect.width = w;
    p_osd_attr->rect.height = h;
    p_osd_attr->global_alpha = 0x0;
    //p_osd_attr->bg_color = 0x80FF0000;
    p_osd_attr->bg_color = 0x00;
    p_osd_attr->format = E_PIXEL_FORMAT_ARGB8888;
    p_osd_attr->stride = p_osd_attr->rect.width * 4;
    vs_uint32_t osd_size = p_osd_attr->stride * p_osd_attr->rect.height;
    vs_uint64_t osd_phys_addr;
    vs_void_t *p_osd_virt_addr;
    vs_char_t mmb_name[] = "osd_mmb";
    ret = phy_virt_malloc(VS_NULL, mmb_name, &osd_phys_addr, &p_osd_virt_addr, osd_size);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("osd file phy_virt_malloc fail, ret[0x%x]\n", ret);
        goto CLEAR;
    }
    load_bmp(osd_bmp_path, p_osd_virt_addr);
    p_osd_attr->phys_addr = osd_phys_addr;

    ret = vs_mal_gpe_osd_task_add(job_handle, p_task_attr, p_osd_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_osd_task_add ret[0x%x], job_handle: %d\n", ret, job_handle);
        vs_mal_gpe_job_cancel(job_handle);
        goto CLEAR;
    }
    ret = vs_mal_gpe_job_finish(job_handle);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_job_finish ret[0x%x], job_handle: %d\n", ret, job_handle);
        vs_mal_gpe_job_cancel(job_handle);
        goto CLEAR;
    }

CLEAR:
    p_task_attr = VS_NULL;
    phy_virt_free(osd_phys_addr, p_osd_virt_addr, osd_size);
    free(p_osd_attr);
    p_osd_attr = VS_NULL;
exit0:
    return ret;
}

vs_int32_t nn_gpe_frame_boxes_draw(const vs_video_frame_info_s *p_frame,
                                                    const vs_float_t scale_ratio_w,
                                                    const vs_float_t scale_ratio_h,
                                                    sample_nn_post_result_s *p_post_ret)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_int32_t handle = 0;
    vs_task_attr_s task_attr = {0};
    vs_gpe_cover_attr_s *p_covers_attr = VS_NULL;
    vs_uint32_t i = 0;
    vs_point_s p0 = {0};
    vs_int32_t box_w = 0, box_h = 0;
    vs_int32_t cover_index = 0;
    sample_nn_detection_result_s *p_result_objs = &p_post_ret->detection;

    if (p_frame == VS_NULL) {
        vs_sample_trace("input frame is NULL!\n");
        return VS_FAILED;
    }

    p_covers_attr =(vs_gpe_cover_attr_s *)malloc(p_result_objs->obj_num * sizeof(vs_gpe_cover_attr_s));
    memset(p_covers_attr, 0, p_result_objs->obj_num * sizeof(vs_gpe_cover_attr_s));

    // vs_sample_trace("nn_gpe_frame_boxes_draw detection obj num is:%d\n",p_result_objs->obj_num);
    for (i = 0; i < p_result_objs->obj_num; i++) {
        if (g_rotation_flag) {
            box_w = p_result_objs->objs[i].h * scale_ratio_h;
            box_h = p_result_objs->objs[i].w * scale_ratio_w;
            p0.x = p_frame->frame.width - (p_result_objs->objs[i].y + p_result_objs->objs[i].h) * scale_ratio_h;
            p0.y = p_result_objs->objs[i].x * scale_ratio_w;
        }
        else {
            box_w = p_result_objs->objs[i].w * scale_ratio_w;
            box_h = p_result_objs->objs[i].h * scale_ratio_h;
            p0.x = p_result_objs->objs[i].x * scale_ratio_w;
            p0.y = p_result_objs->objs[i].y * scale_ratio_h;
        }

        // printf("x:%d, y:%d, w:%d, h:%d\n", p0.x, p0.y, box_w, box_h);
        if (p0.x < 0) {
            p0.x = 0;
        }
        if (p0.y < 0) {
            p0.y = 0;
        }
        if (p0.y + box_h >= p_frame->frame.height) {
            box_h = p_frame->frame.height - p0.y - 1;
        }
        if (p0.x + box_w >= p_frame->frame.width) {
            box_w = p_frame->frame.width - p0.x - 1;
        }
        // printf("x:%d, y:%d, w:%d, h:%d\n", p0.x, p0.y, box_w, box_h);



#ifdef GPE_RECT_FORCE_DRAW
        box_h = max(box_h, GPE_COVER_MIN_HEIGHT);
        box_w = max(box_w, GPE_COVER_MIN_WIDTH);
#endif

        if(box_w >= GPE_COVER_MIN_WIDTH && box_h >= GPE_COVER_MIN_HEIGHT) {
            p_covers_attr[cover_index].type = E_GPE_COVER_TYPE_RECTANGLE;
            p_covers_attr[cover_index].color = p_result_objs->objs[i].color;
            p_covers_attr[cover_index].width = NN_BOX_LINE_WIDTH;
            p_covers_attr[cover_index].solid = VS_FALSE;
            p_covers_attr[cover_index].rect.x = p0.x;
            p_covers_attr[cover_index].rect.y = p0.y;
            p_covers_attr[cover_index].rect.width = box_w;
            p_covers_attr[cover_index].rect.height = box_h;
            cover_index ++;
        }
        else
        {
            printf("warining: gpe_draw_cover_box, box_w:%d or box_h:%d less than %d\n", box_w, box_h, GPE_COVER_MIN_WIDTH);

            goto exit1;
        }
    }

    task_attr.src_frame = *p_frame;
    task_attr.dst_frame = *p_frame;

    if (cover_index > 0) {
        ret = vs_mal_gpe_job_start(&handle);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("vs_mal_gpe_job_start failed with err 0x%x\n", ret);
            goto exit1;
        }

        ret = vs_mal_gpe_multicover_task_add(handle, &task_attr, p_covers_attr, cover_index);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("vs_mal_gpe_multicover_task_add failed with err 0x%x\n", ret);
            for (i = 0; i < cover_index; i++) {
                vs_sample_trace("x[%d] y[%d] w[%u] h[%u] frame.width[%u] frame.height[%u]\n",
                                p_covers_attr[i].rect.x, p_covers_attr[i].rect.y,
                                p_covers_attr[i].rect.height, p_covers_attr[i].rect.width,
                                p_frame->frame.width, p_frame->frame.height);
            }
            ret = vs_mal_gpe_job_cancel(handle);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_job_cancel failed with err 0x%x\n", ret);
            }
        }
        else {
            ret = vs_mal_gpe_job_finish(handle);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_job_finish failed with err 0x%x\n", ret);
                for (i = 0; i < cover_index; i++) {
                    vs_sample_trace("box index: %4d, start(%4d,%4d), box width: %4d, box height: %4d\n",
                        i, p_covers_attr[i].rect.x, p_covers_attr[i].rect.y,
                        p_covers_attr[i].rect.width, p_covers_attr[i].rect.height);
                }
            }
        }
    }

exit1:
    if (p_covers_attr) {
        free(p_covers_attr);
        p_covers_attr = VS_NULL;
    }
    return ret;
}


vs_int32_t nn_gpe_frame_boxes_draw(const vs_video_frame_info_s *p_frame,
                                                    const vs_float_t scale_ratio_w,
                                                    const vs_float_t scale_ratio_h,
                                                    sample_nn_post_result_s *p_post_ret)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_int32_t handle = 0;
    vs_task_attr_s task_attr = {0};
    vs_gpe_cover_attr_s *p_covers_attr = VS_NULL;
    vs_uint32_t i = 0;
    vs_point_s p0 = {0};
    vs_int32_t box_w = 0, box_h = 0;
    vs_int32_t cover_index = 0;
    sample_nn_detection_result_s *p_result_objs = &p_post_ret->detection;

    if (p_frame == VS_NULL) {
        vs_sample_trace("input frame is NULL!\n");
        return VS_FAILED;
    }

    p_covers_attr =(vs_gpe_cover_attr_s *)malloc(p_result_objs->obj_num * sizeof(vs_gpe_cover_attr_s));
    memset(p_covers_attr, 0, p_result_objs->obj_num * sizeof(vs_gpe_cover_attr_s));

    // vs_sample_trace("nn_gpe_frame_boxes_draw detection obj num is:%d\n",p_result_objs->obj_num);
    for (i = 0; i < p_result_objs->obj_num; i++) {
        if (g_rotation_flag) {
            box_w = p_result_objs->objs[i].h * scale_ratio_h;
            box_h = p_result_objs->objs[i].w * scale_ratio_w;
            p0.x = p_frame->frame.width - (p_result_objs->objs[i].y + p_result_objs->objs[i].h) * scale_ratio_h;
            p0.y = p_result_objs->objs[i].x * scale_ratio_w;
        }
        else {
            box_w = p_result_objs->objs[i].w * scale_ratio_w;
            box_h = p_result_objs->objs[i].h * scale_ratio_h;
            p0.x = p_result_objs->objs[i].x * scale_ratio_w;
            p0.y = p_result_objs->objs[i].y * scale_ratio_h;
        }

        // printf("x:%d, y:%d, w:%d, h:%d\n", p0.x, p0.y, box_w, box_h);
        if (p0.x < 0) {
            p0.x = 0;
        }
        if (p0.y < 0) {
            p0.y = 0;
        }
        if (p0.y + box_h >= p_frame->frame.height) {
            box_h = p_frame->frame.height - p0.y - 1;
        }
        if (p0.x + box_w >= p_frame->frame.width) {
            box_w = p_frame->frame.width - p0.x - 1;
        }
        // printf("x:%d, y:%d, w:%d, h:%d\n", p0.x, p0.y, box_w, box_h);



#ifdef GPE_RECT_FORCE_DRAW
        box_h = max(box_h, GPE_COVER_MIN_HEIGHT);
        box_w = max(box_w, GPE_COVER_MIN_WIDTH);
#endif

        if(box_w >= GPE_COVER_MIN_WIDTH && box_h >= GPE_COVER_MIN_HEIGHT) {
            p_covers_attr[cover_index].type = E_GPE_COVER_TYPE_RECTANGLE;
            p_covers_attr[cover_index].color = p_result_objs->objs[i].color;
            p_covers_attr[cover_index].width = NN_BOX_LINE_WIDTH;
            p_covers_attr[cover_index].solid = VS_FALSE;
            p_covers_attr[cover_index].rect.x = p0.x;
            p_covers_attr[cover_index].rect.y = p0.y;
            p_covers_attr[cover_index].rect.width = box_w;
            p_covers_attr[cover_index].rect.height = box_h;
            cover_index ++;
        }
        else
        {
            printf("warining: gpe_draw_cover_box, box_w:%d or box_h:%d less than %d\n", box_w, box_h, GPE_COVER_MIN_WIDTH);

            goto exit1;
        }
    }

    task_attr.src_frame = *p_frame;
    task_attr.dst_frame = *p_frame;

    if (cover_index > 0) {
        ret = vs_mal_gpe_job_start(&handle);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("vs_mal_gpe_job_start failed with err 0x%x\n", ret);
            goto exit1; 
        }

        ret = vs_mal_gpe_multicover_task_add(handle, &task_attr, p_covers_attr, cover_index);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("vs_mal_gpe_multicover_task_add failed with err 0x%x\n", ret);
            for (i = 0; i < cover_index; i++) {
                vs_sample_trace("x[%d] y[%d] w[%u] h[%u] frame.width[%u] frame.height[%u]\n",
                                p_covers_attr[i].rect.x, p_covers_attr[i].rect.y,
                                p_covers_attr[i].rect.height, p_covers_attr[i].rect.width,
                                p_frame->frame.width, p_frame->frame.height);
            }
            ret = vs_mal_gpe_job_cancel(handle);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_job_cancel failed with err 0x%x\n", ret);
            }
        }
        else {
            ret = vs_mal_gpe_job_finish(handle);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_job_finish failed with err 0x%x\n", ret);
                for (i = 0; i < cover_index; i++) {
                    vs_sample_trace("box index: %4d, start(%4d,%4d), box width: %4d, box height: %4d\n",
                        i, p_covers_attr[i].rect.x, p_covers_attr[i].rect.y,
                        p_covers_attr[i].rect.width, p_covers_attr[i].rect.height);
                }
            }
        }
    }

exit1:
    if (p_covers_attr) {
        free(p_covers_attr);
        p_covers_attr = VS_NULL;
    }
    return ret;
}


vs_int32_t nn_gpe_tracking_boxes_draw(const vs_video_frame_info_s *p_frame,
                                                    const vs_float_t scale_ratio_w,
                                                    const vs_float_t scale_ratio_h,
                                                    sample_nn_post_result_s *p_post_ret)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_int32_t handle = 0;

    if (p_frame == VS_NULL) 
    {
        vs_sample_trace("input frame is NULL!\n");
        return VS_FAILED;
    }

    vs_task_attr_s* p_task_attr;
    p_task_attr = (vs_task_attr_s*) malloc(sizeof(vs_task_attr_s));
    memset(p_task_attr, 0, sizeof(vs_task_attr_s));

    sample_nn_detection_result_s *p_result_objs = &p_post_ret->detection;
    vs_gpe_line_attr_s *p_lines_attr = (vs_gpe_line_attr_s*)malloc(p_result_objs->obj_num  * 8 * sizeof(vs_gpe_line_attr_s));
    memset(p_lines_attr, 0, p_result_objs->obj_num * 8 * sizeof(vs_gpe_line_attr_s)); 

    vs_uint32_t i = 0;
    vs_uint32_t line_index = 0;
    vs_point_s p0 = {0};
    vs_int32_t box_w = 0, box_h = 0;

    vs_point_s p1 = {0};
    vs_point_s p_lines[8][2] = {0};

    // airvisen_trace("p_result_objs->obj_num: %d\n", p_result_objs->obj_num);

    for (i = 0; i < p_result_objs->obj_num; i++) 
    {
        // printf("come to %s, line:%d\n", __FUNCTION__, __LINE__);
        if (g_rotation_flag) {
            box_w = p_result_objs->objs[i].h * scale_ratio_h;
            box_h = p_result_objs->objs[i].w * scale_ratio_w;
            p0.x = p_frame->frame.width - (p_result_objs->objs[i].y + p_result_objs->objs[i].h) * scale_ratio_h;
            p0.y = p_result_objs->objs[i].x * scale_ratio_w;
        }
        else {
            box_w = p_result_objs->objs[i].w * scale_ratio_w;
            box_h = p_result_objs->objs[i].h * scale_ratio_h;
            p0.x = p_result_objs->objs[i].x * scale_ratio_w;
            p0.y = p_result_objs->objs[i].y * scale_ratio_h;
        }

        if (p0.x < 0) {
            p0.x = 0;
        }
        if (p0.y < 0) {
            p0.y = 0;
        }

        p1.x = p0.x + box_w;
        p1.y = p0.y + box_h;

        if (p1.y >= p_frame->frame.height) {
            p1.y = p_frame->frame.height - 1;
        }
        if (p1.x >= p_frame->frame.width) {
            p1.x = p_frame->frame.width - 1;
        }

        /*           ----        ----
         *     1-->  |              |  <--6
         *                    
         *     2-->  |              |  <--5 
         *           ----        ----
         */
        /** 
         * #line 1  (x0, y0) --> (x0, y0 + (y1 - y0) / 4)
         * #line 2  (x0, y1) --> (x0, y1 - (y1 - y0) / 4)
         * #line 3  (x0, y1) --> (x0 + (x1 - x0) / 4, y1)
         * #line 4  (x1, y1) --> (x1 - (x1 - x0) / 4, y1)
         * #line 5  (x1, y1) --> (x1, y1 - (y1 - y0) / 4)
         * #line 6  (x1, y0) --> (x1, y0 + (y1 - y0) / 4)
         * #line 7  (x1, y0) --> (x1 - (x1 - x0) / 4, y0)
         * #line 8  (x0, y0) --> (x0 + (x1 - x0) / 4, y0)
         *
         */
        int width_offset = (p1.x - p0.x) / 4;
        int height_offset = (p1.y - p0.y) / 4;
        p_lines[0][1].x = p0.x;
        p_lines[1][1].x = p0.x;
        p_lines[2][1].x = p0.x + width_offset + 1;
        p_lines[3][1].x = p1.x - width_offset;
        p_lines[4][1].x = p1.x;
        p_lines[5][1].x = p1.x;
        p_lines[6][1].x = p1.x - width_offset;
        p_lines[7][1].x = p0.x + width_offset + 1;
        p_lines[0][1].y =  p0.y + height_offset + 1;
        p_lines[1][1].y =  p1.y - height_offset;
        p_lines[2][1].y =  p1.y;
        p_lines[3][1].y =  p1.y;
        p_lines[4][1].y =  p1.y - height_offset;
        p_lines[5][1].y =  p0.y + height_offset + 1;
        p_lines[6][1].y =  p0.y;
        p_lines[7][1].y =  p0.y;

        p_lines[0][0].x = p0.x;
        p_lines[1][0].x = p0.x;
        p_lines[2][0].x = p0.x;
        p_lines[3][0].x = p1.x + 1;
        p_lines[4][0].x = p1.x;
        p_lines[5][0].x = p1.x;
        p_lines[6][0].x = p1.x + 1;
        p_lines[7][0].x = p0.x;
        p_lines[0][0].y = p0.y;
        p_lines[1][0].y = p1.y + 1;
        p_lines[2][0].y = p1.y;
        p_lines[3][0].y = p1.y;
        p_lines[4][0].y = p1.y + 1;
        p_lines[5][0].y = p0.y;
        p_lines[6][0].y = p0.y;
        p_lines[7][0].y = p0.y;

        for (int j = 0; j < 8; j++)
        {
            p_lines_attr[line_index].color = p_result_objs->objs[i].color;
            p_lines_attr[line_index].start.x = p_lines[j][0].x;
            p_lines_attr[line_index].start.y = p_lines[j][0].y;
            p_lines_attr[line_index].end.x = p_lines[j][1].x;
            p_lines_attr[line_index].end.y = p_lines[j][1].y;
            p_lines_attr[line_index].width = 3;

            line_index ++;
        }
    }

    p_task_attr->src_frame = *p_frame;
    p_task_attr->dst_frame = *p_frame;

    if (line_index > 0) {
        ret = vs_mal_gpe_job_start(&handle);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("vs_mal_gpe_job_start failed with err 0x%x\n", ret);
            goto exit1;
        }
        

        //ret = vs_mal_gpe_line_task_add(handle, p_task_attr, p_lines_attr);
        ret = vs_mal_gpe_multiline_task_add(handle, p_task_attr, p_lines_attr, std::min(line_index, (vs_uint32_t)(100)));

        if (ret != VS_SUCCESS) {
            vs_sample_trace("vs_mal_gpe_line_task_add failed with err 0x%x\n", ret);
            ret = vs_mal_gpe_job_cancel(handle);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_job_cancel failed with err 0x%x\n", ret);
            }
        }
        else {
            ret = vs_mal_gpe_job_finish(handle);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_job_finish failed with err 0x%x\n", ret);
            }
        }
    }

exit1:
    free(p_task_attr);
    p_task_attr = VS_NULL;
    free(p_lines_attr);
    p_lines_attr = VS_NULL;
    return ret;
}

vs_int32_t nn_vpp_rgn_destroy(vs_int32_t rgn_handle, vs_int32_t vpp_grp, vs_int32_t vpp_chnid)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_chn_s chn;// = {0};

    chn.modid = E_MOD_ID_VPP;
    chn.devid = vpp_grp;
    chn.chnid = vpp_chnid;

    ret = vs_mal_rgn_chn_unbind(rgn_handle, &chn);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_rgn_chn_unbind failed with ret 0x%x\n", ret);
    }
    ret = vs_mal_rgn_destroy(rgn_handle);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_rgn_destroy failed with ret 0x%x\n", ret);
    }

    return ret;
}

vs_int32_t nn_vpp_rgn_create(vs_int32_t rgn_handle, vs_size_s rgn_size, vs_int32_t vpp_grp, vs_int32_t vpp_chnid)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_rgn_attr_s rgn_attr;// = {0};
    vs_chn_s chn;// = {0};
    vs_rgn_disp_info_s disp_info;// = {0};

    rgn_attr.type = E_RGN_TYPE_OVERLAYEX;
    rgn_attr.attr.overlayex.size = rgn_size;
    rgn_attr.attr.overlayex.format = E_PIXEL_FORMAT_ARGB8888;
    rgn_attr.attr.overlayex.bgcolor = 0;
    rgn_attr.attr.overlayex.surface_num = 2;
    ret = vs_mal_rgn_create(rgn_handle, &rgn_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_rgn_create failed, ret 0x%x\n", ret);
        goto rgn_destroy;
    }

    chn.modid = E_MOD_ID_VPP;
    chn.devid = vpp_grp;
    chn.chnid = vpp_chnid;

    disp_info.show = VS_TRUE;
    disp_info.type = E_RGN_TYPE_OVERLAYEX;
    disp_info.disp_attr.overlayex_disp.point.x = 0;
    disp_info.disp_attr.overlayex_disp.point.y = 0;
    disp_info.disp_attr.overlayex_disp.zorder = 0;
    disp_info.disp_attr.overlayex_disp.coord = E_COORDINATE_MODE_ABSOLUTE;
    ret = vs_mal_rgn_chn_bind(rgn_handle, &chn, &disp_info);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_rgn_chn_bind failed, ret 0x%x\n", ret);
        goto rgn_destroy;
    }
    return VS_SUCCESS;

rgn_destroy:
    nn_vpp_rgn_destroy(rgn_handle, vpp_grp, vpp_chnid);

    return ret;
}


vs_int32_t nn_producer_pipeline_stop(sample_nn_producer_info_s *p_producer_info)
{
    vs_int32_t ret = VS_SUCCESS;

    ret |= sample_common_vo_stop(&p_producer_info->vo_config);
    //ret |= sample_common_vpp_stop(p_producer_info->vpp_grpid, p_producer_info->vpp_chn_enable);
    //ret |= sample_common_vii_unbind_vpp(p_producer_info->vii_pipeid, p_producer_info->vii_chnid, p_producer_info->vpp_grpid);
    //sample_common_vii_stop(&p_producer_info->vii_cfg);
    //sample_common_dsp_exit(p_producer_info->dsp_id);
    sample_common_sys_exit();
    return ret;
}


static vs_int32_t nn_gpe_frame_fusion_draw(const vs_video_frame_info_s *p_frame,cv::Mat srcImage,vs_uint32_t x, vs_uint32_t y, vs_uint32_t w,vs_uint32_t h)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_int32_t handle = 0;
    vs_task_attr_s task_attr = {0};

    
    if (p_frame == VS_NULL) {
        vs_sample_trace("input frame is NULL!\n");
        return VS_FAILED;
    }
    
    task_attr.src_frame = *p_frame;
    task_attr.dst_frame = *p_frame;

    vs_gpe_osd_attr_s *p_osd_attr = (vs_gpe_osd_attr_s*) malloc(sizeof(vs_gpe_osd_attr_s));
    p_osd_attr->enable = VS_TRUE;
    p_osd_attr->rect.x = x;
    p_osd_attr->rect.y = y;
    p_osd_attr->rect.width = w;
    p_osd_attr->rect.height = h;
    p_osd_attr->global_alpha = 0;
    p_osd_attr->bg_color = 0x80FF0000;
    p_osd_attr->format = E_PIXEL_FORMAT_ARGB8888;
    p_osd_attr->stride = p_osd_attr->rect.width * 4;
    vs_uint32_t osd_size = p_osd_attr->stride * p_osd_attr->rect.height;
    vs_uint64_t osd_phys_addr;
    vs_void_t *p_osd_virt_addr;
    vs_char_t mmb_name[] = "osd_mmb";
    ret = phy_virt_malloc(VS_NULL, mmb_name, &osd_phys_addr, &p_osd_virt_addr, osd_size);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("osd file phy_virt_malloc fail, ret[0x%x]\n", ret);
        goto CLEAR;
    }
  
    memcpy(p_osd_virt_addr,srcImage.ptr<uint8_t>(0),osd_size);
    
    p_osd_attr->phys_addr = osd_phys_addr;
    
    // 启动gpe job
    ret = vs_mal_gpe_job_start(&handle);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_job_start ret[0x%x], job_handle: %d\n", ret, handle);
        goto exit1;
    }

    ret = vs_mal_gpe_osd_task_add(handle, &task_attr, p_osd_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_osd_task_add ret[0x%x], job_handle: %d\n", ret, handle);
        vs_mal_gpe_job_cancel(handle);
        goto CLEAR;
    }
    ret = vs_mal_gpe_job_finish(handle);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("vs_mal_gpe_job_finish ret[0x%x], job_handle: %d\n", ret, handle);
        vs_mal_gpe_job_cancel(handle);
        goto CLEAR;
    }

    exit1:
    if (p_osd_attr) {
        free(p_osd_attr);
        p_osd_attr = VS_NULL;
    }
    CLEAR:
    phy_virt_free(osd_phys_addr, p_osd_virt_addr, osd_size);
    free(p_osd_attr);
    p_osd_attr = VS_NULL;
    return ret;
}


// 图层叠加的实现方法
static vs_int32_t nn_gpe_frame_muti_single_draw_v2(const vs_video_frame_info_s *p_frame,cv::Mat srcImage,sample_nn_post_result_s *p_post_ret)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_int32_t handle = 0;
    vs_task_attr_s task_attr = {0};
    
    vs_int32_t mask_index = 0;
    vs_uint32_t i = 0;
    vs_point_s p0 = {0};
    vs_int32_t box_w = 0, box_h = 0,right=0,bottom=0;
    if (p_frame == VS_NULL) {
        vs_sample_trace("input frame is NULL!\n");
        return VS_FAILED;
    }
    // 从npu获取的目标检测结果
    sample_nn_detection_result_s *p_result_objs = &p_post_ret->detection;
    vs_int32_t mask_num = p_result_objs->obj_num;
    vs_uint32_t osd_size = srcImage.cols*srcImage.rows*4;
    vs_uint64_t osd_phys_addr= VS_NULL;
    vs_void_t *p_osd_virt_addr;
    vs_char_t mmb_name[] = "osd_mmb";
    cv::Mat kasima;
    std::vector<cv::Mat> channels;

    vs_void_t *src_virt_addr;
    task_attr.src_frame = *p_frame;
    task_attr.dst_frame = *p_frame;
    // 获取mask属性空间
    vs_gpe_osd_attr_s *p_osd_attr = (vs_gpe_osd_attr_s*) malloc(sizeof(vs_gpe_osd_attr_s));
    memset(p_osd_attr, 0, sizeof(vs_gpe_osd_attr_s));

    ret = phy_virt_malloc(VS_NULL, mmb_name, &osd_phys_addr, &p_osd_virt_addr, osd_size);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("phy_virt_malloc failed\n");
        goto CLEAR;
    }

    memcpy(p_osd_virt_addr,srcImage.ptr<uint8_t>(0),osd_size);
           
    
    p_osd_attr->stride = srcImage.cols * 4;

    for(i = 0; i < p_result_objs->obj_num; i++) {
        if (g_rotation_flag) {
            box_w = (vs_uint32_t)(p_result_objs->objs[i].h * 3.3056 + 1);
            box_h = (vs_uint32_t)(p_result_objs->objs[i].w * 2.3243 + 1);
            p0.x = (vs_uint32_t)(p_frame->frame.width - (p_result_objs->objs[i].y + p_result_objs->objs[i].h) * 3.3056 + 1);
            p0.y = (vs_uint32_t)(p_result_objs->objs[i].x * 2.3243 + 1);
        }
        else {
            box_w = (vs_uint32_t)(p_result_objs->objs[i].w * 2.3243 + 1);
            box_h = (vs_uint32_t)(p_result_objs->objs[i].h * 3.3056 + 1);
            p0.x = (vs_uint32_t)(p_result_objs->objs[i].x * 2.3243 + 1);
            p0.y = (vs_uint32_t)(p_result_objs->objs[i].y * 3.3056 + 1);
        }
      
        right = p0.x+box_w;
        if(right>1192){
            box_w = 1192-p0.x;
            right = 1192;
        }

        bottom = p0.y+box_h;
        if(bottom>952){
            box_h = 952-p0.y;
            bottom = 952;
        }
    
        if(box_w > GPE_COVER_MIN_WIDTH && box_h > GPE_COVER_MIN_HEIGHT) {
            // cv::Mat patchImage = cv::Mat(box_h,box_w,CV_8UC4);
            // patchImage = srcImage(cv::Range(p0.y,bottom),cv::Range(p0.x,right)).clone();

            p_osd_attr->enable = VS_TRUE;
            p_osd_attr->rect.x = p0.x + 338;
            p_osd_attr->rect.y = p0.y + 68;
            p_osd_attr->rect.width = box_w;
            p_osd_attr->rect.height = box_h;
            p_osd_attr->global_alpha = 0;
            p_osd_attr->bg_color = 0x80FF0000;
            p_osd_attr->format = E_PIXEL_FORMAT_ARGB8888;
            p_osd_attr->phys_addr = osd_phys_addr+p0.x*4+p0.y*p_osd_attr->stride;
            // osd_size = p_osd_attr->stride * p_osd_attr->rect.height;
            
            
            
            
            ret = vs_mal_gpe_job_start(&handle);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_job_start ret[0x%x], job_handle: %d\n", ret, handle);
                goto exit1;
            }

            ret = vs_mal_gpe_osd_task_add(handle, &task_attr, p_osd_attr);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_osd_task_add ret[0x%x], job_handle: %d\n", ret, handle);
                vs_mal_gpe_job_cancel(handle);
                goto CLEAR;
            }
            ret = vs_mal_gpe_job_finish(handle);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("vs_mal_gpe_job_finish ret[0x%x], job_handle: %d\n", ret, handle);
                vs_mal_gpe_job_cancel(handle);
                goto CLEAR;
            }
           
        }
    }
    
 
    exit1:
   
    if (p_osd_attr) {
        free(p_osd_attr);
        p_osd_attr = VS_NULL;
    }
    CLEAR:
    
    if(osd_phys_addr){
        phy_virt_free(osd_phys_addr, p_osd_virt_addr, osd_size);
        free(p_osd_attr);
        p_osd_attr = VS_NULL;
    }

    return ret;
}

static sample_nn_post_result_s nn_post_results;
#ifdef SEND_DETECT_RESULT_VIA_SERIAL

//return ns from system running
static uint64_t get_sys_uptime_in_ns (void)
{
    struct timespec tp;
    clock_gettime(CLOCK_MONOTONIC, &tp);
    return (tp.tv_sec * 1000000000ULL + tp.tv_nsec);
}

extern int g_uppercommtype;
extern char g_strSerialName[256];

bool g_bCommSendExit = false;

static cv::Rect g_ShowRect;
static clock_t pre_frame_clock_t = 0, current_frame_clock_t;
void* nn_comm_send_proc(void *args)
{
    char proc_name[32] = "nn_comm_send_proc";
    prctl(PR_SET_NAME, proc_name, 0, 0, 0);

    int ret = -1;
	int client_sock = -1;

    cv::Rect showRect;

    static unsigned long g_SendCount = 0;

    while(!g_bCommSendExit)
    {

        static unsigned char s_ctrlBuf[1024];
		memset(s_ctrlBuf, '\0', sizeof(s_ctrlBuf));
		
		int nRecvLen = 0;
		
		if ((g_uppercommtype == 0 || g_uppercommtype == 3) && access(g_strSerialName, F_OK) != -1)
		{
			nRecvLen = g_ptrSerial->Recv(s_ctrlBuf, 1024);	
		}

//< send nn result out from serial/net
#if 1
        static double total_t = 0; 
        current_frame_clock_t = clock();
        total_t = (double)(current_frame_clock_t - pre_frame_clock_t) / CLOCKS_PER_SEC * 1000;

        if (total_t < 20.0)
        {
            int sleep_us = int((20.0 - total_t) * 1000);
            printf("will sleep %d us\n", sleep_us);
            usleep(sleep_us);
        }
        pre_frame_clock_t = clock();

#endif
#if 0
        sample_nn_detection_result_s *p_result_objs = &nn_post_results.detection;
        cv::Rect tempRect;
        for (int i = 0; i < p_result_objs->obj_num; i++) 
        {
            tempRect.width = p_result_objs->objs[i].w ;
            tempRect.height = p_result_objs->objs[i].h ;
            tempRect.x = p_result_objs->objs[i].x ;
            tempRect.y = p_result_objs->objs[i].y ;

            unsigned char out_buffer[16];
            out_buffer[0]   = 0x91; //消息头1
            out_buffer[1]   = 0x0D; //帧长
            out_buffer[2]   = 0x3f; // tracking enable 
            out_buffer[3]   = 1;


            out_buffer[4] = tempRect.x & 0xFF;  // x
            out_buffer[5] = (tempRect.x >> 8) & 0xFF;  // x
            out_buffer[6] = tempRect.y & 0xFF;  // y
            out_buffer[7] = (tempRect.y >> 8) & 0xFF;  // y

            out_buffer[8] = tempRect.width & 0xFF;  // w
            out_buffer[9] = (tempRect.width >> 8) & 0xFF;  // w
            out_buffer[10] = tempRect.height & 0xFF;  // h
            out_buffer[11] = (tempRect.height >> 8) & 0xFF;  // h
         
            out_buffer[12] = 0x01;  // crc8                            
#else
        sample_nn_detection_result_s *p_result_objs = &nn_post_results.detection;
        
        int max_object_num = 10;
        unsigned char out_buffer[256];

        int send_num = p_result_objs->obj_num < max_object_num ? p_result_objs->obj_num : max_object_num;
        int len = 4 + send_num * 10;
        out_buffer[0]    = 0xAA; //消息头1
        out_buffer[1]    = len; //帧长
        out_buffer[2]    = send_num; //target num
        for(int i = 0; i < send_num; i++)
        {
            float scale_ratio_w = 640 / get_nn_chn_width();
            float scale_ratio_h = 512 / get_nn_chn_height();
            int x = p_result_objs->objs[i].x * scale_ratio_w;
            int y = p_result_objs->objs[i].y * scale_ratio_h;
            int w = p_result_objs->objs[i].w * scale_ratio_w;
            int h = p_result_objs->objs[i].h * scale_ratio_h;
            out_buffer[3 + i * 10]      = p_result_objs->objs[i].cid; //class id
            out_buffer[4 + i * 10]      = (int)(p_result_objs->objs[i].cprob * 255); //confidence
            out_buffer[5 + i * 10]      = x & 0xFF;   // x
            out_buffer[6 + i * 10]      = (x >> 8) & 0xFF;  // x
            out_buffer[7 + i * 10]      = y & 0xFF;  // y
            out_buffer[8 + i * 10]      = (y >> 8) & 0xFF;  // y
            out_buffer[9 + i * 10]     = w & 0xFF;  // w
            out_buffer[10 + i * 10]     = (w >> 8) & 0xFF;  // w
            out_buffer[11 + i * 10]     = h & 0xFF;  // h
            out_buffer[12 + i * 10]     = (h >> 8) & 0xFF;  // h
        }

        out_buffer[len - 1] = 0x01;  // crc8
#endif
        if ((g_uppercommtype == 0 || g_uppercommtype == 3) && access(g_strSerialName, F_OK) != -1)
        {
            g_ptrSerial->Send(out_buffer, len);
        }
        
    }//< while loop
}
#endif

vs_void_t* nn_producer_vo_proc(vs_void_t *args)
{
    static int g_index = 0;
    vs_int32_t ret = 0;
    vs_video_frame_info_s vpp_frame = {0};
    //sample_nn_post_result_s nn_post_results;
    sample_nn_producer_info_s* p_producer_info = (sample_nn_producer_info_s*)args;
    p_producer_info->vpp_grpid = VII_VPP_GRP_ID;
    p_producer_info->vpp_chnid_vo = IR_VPP_CHN_ID;
    p_producer_info->vo_chnid = IR_VO_CHN_ID;
    airvisen_trace("nn_producer_vo_proc start.\n");
    int timeout = VPP_ACQUIRE_TIMEOUT;
#ifdef DUAL_LENSES 
    vs_uint64_t   src_phys_addr,dst_phys_addr;  
    vs_void_t *src_virt_addr,*dst_virt_addr;
    uint32_t src_frm_size = 1080 *1920;         // 红外图像大小
    uint32_t dst_frm_size = 1080 *1920;         // 可见光图像大小
    vs_video_frame_info_s dst_frame = {0};
    cv::Mat srcImage;
    cv::Mat dstImage,beta,alpha;

    // 实现融合时需要打开下列代码
    // vs_int32_t x0,y0,box_w,box_h;               
    // cv::Size size = cv::Size(1192,952);         // 根据红外图像和可见光图像的实际大小比例计算的红外图像大小
    timeout = 50;
#endif

    //创建vb_pool
    vs_vb_pool_cfg_s dst_pool_cfg = {0};
    vs_uint32_t dst_frm_size = 0;
    vs_pixel_format_e dst_format ;
    vs_size_s dst_size;

    dst_format = E_PIXEL_FORMAT_YUV_420SP;
    dst_size.width = s_vpp_sr_size.width;
    dst_size.height = s_vpp_sr_size.height;

    dst_frm_size = sample_common_buffer_size_get(&dst_size,
                dst_format, E_COMPRESS_MODE_NONE, 1);

#if defined(RTSP_ENABLED)
    dst_pool_cfg.blk_cnt = 30;
#else
    dst_pool_cfg.blk_cnt = 6;
#endif
    dst_pool_cfg.blk_size = dst_frm_size;
    dst_pool_cfg.remap_mode = VB_REMAP_MODE_NONE;
    VB_POOL vb_pool = vs_mal_vb_pool_create(&dst_pool_cfg);

    if (VS_INVALID_POOLID == vb_pool) {
            vs_sample_trace("failed to create pool for algo\n");
    }

    //获取缓存块

    VB_BLK dst_frm_vb;
    vs_uint64_t phys_addr;
    vs_void_t *virt_addr = NULL;
    vs_video_frame_info_s dst_frame = {0};
    vs_video_frame_info_s *p_frame = VS_NULL;

    vs_uint32_t y_size = 0, uv_size = 0, dst_frm_size_sr = 0;

    y_size = s_vpp_sr_size.width*s_vpp_sr_size.height;
    uv_size = y_size / 2;

    dst_frm_size_sr = y_size + uv_size;

#ifdef DUAL_LENSES 
    dst_frm_vb = vs_mal_vb_block_get(vb_pool, dst_frm_size_sr, NULL);
    if (VS_INVALID_VB_HANDLE == dst_frm_vb) {
                vs_sample_trace("failed to get vb block for algo\n");       
    }

    phys_addr = vs_mal_vb_handle2physaddr(dst_frm_vb);
    virt_addr = vs_mal_sys_mmap(phys_addr, dst_frm_size_sr);

    dst_frame.modid = E_MOD_ID_VII;
    dst_frame.poolid = vb_pool;
    dst_frame.frame.width = dst_size.width;
    dst_frame.frame.height = dst_size.height;
    dst_frame.frame.pixel_format = dst_format;
    dst_frame.frame.video_format = E_VIDEO_FORMAT_LINEAR;
    dst_frame.frame.compress_mode = E_COMPRESS_MODE_NONE;
    dst_frame.frame.dynamic_range = E_DYNAMIC_RANGE_SDR8;
    //dst_frame.frame.color_gamut = src_frame.frame.color_gamut;
    dst_frame.frame.stride[0] = dst_size.width;
    dst_frame.frame.stride[1] = dst_size.width;
    dst_frame.frame.stride[2] = dst_size.width;
    dst_frame.frame.phys_addr[0] = phys_addr;
    dst_frame.frame.phys_addr[1] = phys_addr+ y_size;
    dst_frame.frame.phys_addr[2] = 0;
    dst_frame.frame.virt_addr[0] = (vs_uint64_t)virt_addr;
    dst_frame.frame.virt_addr[1] = (vs_uint64_t)(virt_addr + y_size);
    dst_frame.frame.virt_addr[2] = 0;

    memset((vs_void_t*)dst_frame.frame.virt_addr[1], 128, uv_size);
#endif

    do{
        // get frame
        ret = vs_mal_vpp_chn_frame_acquire(p_producer_info->vpp_grpid, p_producer_info->vpp_chnid_vo, &vpp_frame, VPP_ACQUIRE_TIMEOUT);

        #ifdef DUAL_LENSES 
            // 获取可见光图像   “2”是可见光图像的grpid，“10”是可见光的拓展通道id
            ret &= vs_mal_vpp_chn_frame_acquire(2, 10,&dst_frame, timeout);
        #endif 
        if (ret != VS_SUCCESS) {
            //vs_sample_trace("vpp_chn_frame_acquire failed with err 0x%x, group_id %d, chn_id %d\n", ret, p_producer_info->vpp_grpid, p_producer_info->vpp_chnid_vo);
            usleep(150);
            continue;
        }


        g_index ++;
        airvisen_trace("##NN_PRODUCER_VO_PROC: g_index:%d\n", g_index);
#ifdef LIYI_AI_DENOISE_NEW_PIPELINE
        if (denoiseenable == 1)
        {
            vs_uint64_t  src_phys_addr;
            vs_void_t *src_virt_addr = NULL;
            vs_uint64_t  dst_phys_addr;
            vs_void_t *dst_virt_addr = NULL;

            src_phys_addr = vpp_frame.frame.phys_addr[0];
            src_virt_addr = vs_mal_sys_mmap(src_phys_addr, s_vpp_size.height*s_vpp_size.width);

            WALLTIMETRACKING
            denoise((vs_uint8_t *)src_virt_addr, src_phys_addr);
            vs_mal_sys_unmap(src_virt_addr,s_vpp_size.height*s_vpp_size.width);
            WALLTIMESTAT("4. denoise preprocess postprocess total timecost: ")
        }
        if (srenable == 1)
        {
            vs_uint64_t  src_phys_addr;
            vs_void_t *src_virt_addr = NULL;
            vs_uint64_t  dst_phys_addr;
            vs_void_t *dst_virt_addr = NULL;

#ifdef LIYI_AI_SR
            dst_frm_vb = vs_mal_vb_block_get(vb_pool, dst_frm_size_sr, NULL);
            if (VS_INVALID_VB_HANDLE == dst_frm_vb) 
            {
                vs_sample_trace("failed to get vb block for algo\n");       
            }

            phys_addr = vs_mal_vb_handle2physaddr(dst_frm_vb);
            //virt_addr = vs_mal_sys_mmap(phys_addr, dst_frm_size_sr);
            virt_addr = vs_mal_sys_mmap_cached(phys_addr, dst_frm_size_sr);

            dst_frame.modid = E_MOD_ID_VII;
            dst_frame.poolid = vb_pool;
            dst_frame.frame.width = dst_size.width;
            dst_frame.frame.height = dst_size.height;
            dst_frame.frame.pixel_format = dst_format;
            dst_frame.frame.video_format = E_VIDEO_FORMAT_LINEAR;
            dst_frame.frame.compress_mode = E_COMPRESS_MODE_NONE;
            dst_frame.frame.dynamic_range = E_DYNAMIC_RANGE_SDR8;
            dst_frame.frame.stride[0] = dst_size.width;
            dst_frame.frame.stride[1] = dst_size.width;
            dst_frame.frame.stride[2] = dst_size.width;
            dst_frame.frame.phys_addr[0] = phys_addr;
            dst_frame.frame.phys_addr[1] = phys_addr+ y_size;
            dst_frame.frame.phys_addr[2] = 0;
            dst_frame.frame.virt_addr[0] = (vs_uint64_t)virt_addr;
            dst_frame.frame.virt_addr[1] = (vs_uint64_t)(virt_addr + y_size);
            dst_frame.frame.virt_addr[2] = 0;

            memset((vs_void_t*)dst_frame.frame.virt_addr[1], 128, uv_size);
            src_phys_addr = vpp_frame.frame.phys_addr[0];
            //src_virt_addr = vs_mal_sys_mmap(src_phys_addr, s_vpp_size.height*s_vpp_size.width);
            src_virt_addr = vs_mal_sys_mmap_cached(src_phys_addr, s_vpp_size.height*s_vpp_size.width);
            dst_phys_addr = dst_frame.frame.phys_addr[0];
            dst_virt_addr = virt_addr;

            WALLTIMETRACKING
            nn_sr((vs_uint8_t *)src_virt_addr, src_phys_addr,(vs_uint8_t *)dst_virt_addr);
            WALLTIMESTAT("4. SR preprocess postprocess total timecost: ")
            vs_mal_sys_unmap(src_virt_addr,s_vpp_size.height*s_vpp_size.width);
#endif          
        }
#endif
        if (detectenable == 1)
        {
            //get result
            ret = sample_common_nn_result_get(&p_producer_info->nn_info, &nn_post_results);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("sample_common_nn_result_get failed with ret 0x%x\n", ret);
                goto exit1;
            }

            //draw postproc res
            //vs_sample_trace("nn2vow is %d ,nn2vo_h is %d \n",p_producer_info->scale_ratio_nn2vo_w,p_producer_info->scale_ratio_nn2vo_h);

            p_producer_info->scale_ratio_nn2vo_w = (vs_float_t)s_vpp_size.width / get_nn_chn_width();
            p_producer_info->scale_ratio_nn2vo_h = (vs_float_t)s_vpp_size.height / get_nn_chn_height();
            if (p_producer_info->nn_info.nb_info.model_type == E_NN_MODE_DETECTION) 
            {
#ifdef DUAL_LENSES
#ifdef ASPECT_RATIO_16_9
                p_producer_info->scale_ratio_nn2vo_w = 3.75f;
                p_producer_info->scale_ratio_nn2vo_h = 3.75f;
#endif
                ret = nn_gpe_frame_boxes_draw(&dst_frame, p_producer_info->scale_ratio_nn2vo_w, p_producer_info->scale_ratio_nn2vo_h, &nn_post_results);
#else
                if(srenable == 1)
                {
                    p_producer_info->scale_ratio_nn2vo_w = (vs_float_t)s_vpp_sr_size.width / get_nn_chn_width();
                    p_producer_info->scale_ratio_nn2vo_h = (vs_float_t)s_vpp_sr_size.height / get_nn_chn_height();
                    ret = nn_gpe_frame_boxes_draw(&dst_frame, p_producer_info->scale_ratio_nn2vo_w, p_producer_info->scale_ratio_nn2vo_h, &nn_post_results);
                }
                else 
                {
                    ret = nn_gpe_frame_boxes_draw(&vpp_frame, p_producer_info->scale_ratio_nn2vo_w, p_producer_info->scale_ratio_nn2vo_h, &nn_post_results);
                }

#endif
                if (ret != VS_SUCCESS) 
                {
                    vs_sample_trace("sample_nn_gpe_draw_boxes_toframe failed with err 0x%x\n", ret);
                    goto exit1;
                }
            }

            
#ifdef DUAL_LENSES 
            
            // 绑定红外图像的物理地址和虚拟地址（采用cache可以提升速度）
            src_phys_addr = vpp_frame.frame.phys_addr[0];
            src_virt_addr = vs_mal_sys_mmap_cached(src_phys_addr, src_frm_size);

            // 绑定可见光图像的物理地址和虚拟地址
            dst_phys_addr = dst_frame.frame.phys_addr[0];
            dst_virt_addr = vs_mal_sys_mmap_cached(dst_phys_addr, dst_frm_size);


            if(src_virt_addr!=NULL){ 
                // 通过opencv将红外图像缩放成可见光图像的相应大小
                srcImage = cv::Mat(512, 640, CV_8UC1, src_virt_addr);
                //cv::resize(srcImage,srcImage,size);
            }
            

            // if(dst_virt_addr!=NULL){ 
            //     dstImage = cv::Mat(512, 640, CV_8UC1, dst_virt_addr);
            // }

            // 将单通道图像转化为RGBA图像（gpe组件要求RGBA图像）
            beta = cv::Mat(512, 640, CV_8UC4);
            cv::cvtColor(srcImage,beta,cv::COLOR_GRAY2RGBA);

            // 调用集成了gpe的融合方法
            // nn_gpe_frame_muti_single_draw_v2(&dst_frame,beta,&nn_post_results);

            // 目前只传入整张图片贴在右上角
            nn_gpe_frame_fusion_draw(&dst_frame,beta,0,0,640,512);


            // 将可见光图像和红外图像的物理地址和虚拟地址分别解绑
            vs_mal_sys_unmap(src_virt_addr, src_frm_size);
            vs_mal_sys_unmap(dst_virt_addr, src_frm_size);
#endif 

        }

       
        if (showdebuglogo == 1)
        {
            if (srenable == 1)
            {
                p_frame =  &dst_frame;

                if (s_vpp_sr_size.width == s_vpp_size.width * 4)
                {
                    //ret = nn_gpe_frame_osd(&dst_frame, "resources/logo/4xlogo.bmp", s_vpp_sr_size.width - 128, 100);
                    ret = nn_gpe_frame_osd(&dst_frame, "resources/logo/2xlogo.bmp", 0, s_vpp_sr_size.height - 128);
                }

                if (s_vpp_sr_size.width == s_vpp_size.width * 2)
                {
                    //ret = nn_gpe_frame_osd(&dst_frame, "resources/logo/2xlogo.bmp", s_vpp_sr_size.width - 128, 100);
                    ret = nn_gpe_frame_osd(&dst_frame, "resources/logo/2xlogo.bmp", 0, s_vpp_sr_size.height - 128);
                }
            }
            else
            {
                p_frame = &vpp_frame;
            }
                
           if (s_vo_size.width >= 2560 )
           {
#if ((!defined VII_FROM_UVC_256 ) && (! defined VII_FROM_UVC_384) )
                ret = nn_gpe_frame_osd(p_frame, "resources/logo/4Kgolden.bmp", 100, 100, 448, 384);
                //ret = nn_gpe_frame_osd(p_frame, "resources/logo/4Kgolden.bmp", 100, 100, 128, 128);
                if (ret != VS_SUCCESS) 
                {
                    vs_sample_trace("sample_nn_gpe_draw_boxes_toframe failed with err 0x%x\n", ret);
                    goto exit1;
                }
#endif
            }

        }
        //send frame to display
        
#ifdef RTSP_ENABLED
#ifdef DUAL_LENSES 
        //ret = vs_mal_vo_chn_frame_send(0,0, &dst_frame, timeout);
        printf("RTSP for duallenses currently not supported yet!\n");
#else
        if(srenable == 1)
        {
            ret = vs_mal_venc_frame_send(0, &dst_frame, 0);
        }
        else 
        {
            ret = vs_mal_venc_frame_send(0, &vpp_frame, 0);
        }
#endif
#else
#ifdef DUAL_LENSES 
        ret = vs_mal_vo_chn_frame_send(0,0, &dst_frame, timeout);
#else
        if(srenable == 1)
        {
            ret = vs_mal_vo_chn_frame_send(p_producer_info->vo_layerid,p_producer_info->vo_chnid, &dst_frame, VO_ACQUIRE_TIMEOUT);
        }
        else 
        {
            ret = vs_mal_vo_chn_frame_send(p_producer_info->vo_layerid,p_producer_info->vo_chnid, &vpp_frame, VO_ACQUIRE_TIMEOUT);
        }
        
#endif //< DUAL_LENSES
#endif //< RTSP_ENABLED

       if (ret != VS_SUCCESS) {
            //vs_sample_trace("vs_mal_vo_chn_frame_send failed with ret 0x%x\n", ret);
            goto exit1;
        }
      
        //release frame
exit1:
        ret = vs_mal_vpp_chn_frame_release(p_producer_info->vpp_grpid, p_producer_info->vpp_chnid_vo, &vpp_frame);

#ifdef LIYI_AI_SR
        if (srenable == 1)
        {
            vs_mal_sys_unmap(virt_addr, dst_frm_size);
            vs_mal_vb_block_release(dst_frm_vb);
        }
#endif

#ifdef DUAL_LENSES 
        ret = vs_mal_vpp_chn_frame_release(2, 10, &dst_frame);
#endif
        if (ret != VS_SUCCESS) {
            vs_sample_trace("vs_mal_vpp_chn_frame_release failed with ret 0x%x\n", ret);
        }
    } while (g_stop_flag != VS_TRUE);
#ifdef LIYI_AI_DENOISE_NEW_PIPELINE    
    
    ret = vs_mal_vb_pool_destory(vb_pool);
    if (ret != VS_SUCCESS)
    {
        printf("destory vb pool of vo_proc failed!\n");
    }
    cleanup_nn_lite_nrnet();
#endif

    airvisen_trace("nn_producer_vo_proc stop.\n");
    return VS_NULL;
}

vs_void_t nn_post_result_show(sample_nn_post_result_s *p_nn_post_result)
{
    vs_uint32_t i = 0;

    if (p_nn_post_result->model_type == E_NN_MODE_DETECTION) {
        for (i = 0; i < p_nn_post_result->detection.obj_num; i++) {
            airvisen_trace("index[%d] object id:%3d, prob:%1.2f, start(%3d,%3d), width[%3d],height[%3d]\n",
            			i,
            			p_nn_post_result->detection.objs[i].cid,
            			p_nn_post_result->detection.objs[i].cprob,
            			p_nn_post_result->detection.objs[i].x,
            			p_nn_post_result->detection.objs[i].y,
            			p_nn_post_result->detection.objs[i].w,
            			p_nn_post_result->detection.objs[i].h);
        }
    } else if (p_nn_post_result->model_type == E_NN_MODE_CLASSIFFICATION) {
        airvisen_trace("object id:%3d, prob:%1.2f \n",
        			p_nn_post_result->classification.cid,
        			p_nn_post_result->classification.max_prob);
    } else {
        vs_sample_trace("model_type[%d] not support \n", p_nn_post_result->model_type);
    }
}

#endif
