/*
 * @file    sample_infra.c
 * @brief   sample infra implementation
 * @details
 * <AUTHOR> Software Group
 * @date    2022-05-25
 * @version v1.00
 * @Copyright (c) 2022 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#include <stdint.h>
#include <stdlib.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <signal.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <errno.h>
#include <dlfcn.h>
#include <pthread.h>
#include <linux/videodev2.h>
#include "vs_mal_nn.h"
#include "denoise.h"
#include <opencv2/highgui/highgui_c.h>
#include <opencv2/highgui/highgui.hpp>

using namespace std;
using namespace cv;

#include "sample_common.h"
#include "sample_common_infra.h"
#include "airvisenpipeline.h"
#include "configparser.h"
#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"

#ifdef RTSP_ENABLED
#define SAMPLE_VENC_INPUT_FORMAT       E_PIXEL_FORMAT_YVU_420SP
#endif

#ifdef VII_FROM_UVC
#include "uvc_camera.h"
extern "C" 
{
    int uvc_host_infra_case_start(vs_size_s s_uvc_frame_size, vs_uvc_camera_info_s *p_camera);
    int uvc_host_infra_case_stop(vs_uvc_camera_info_s *p_camera, int statusCode);
};
#endif
#include "nuc.h"
#include "dde.h"

extern char *g_strConfigIni;
static sample_infra_algo_handle_s algo_handle ;
static sample_vo_cfg_s vo_cfg = {VS_FALSE};
static sample_vii_cfg_s vii_cfg ;
static vs_bool_t vpp_chn_enable[VPP_MAX_PHYCHN_NUM] = {VS_FALSE};
static vs_vpp_chn_attr_s vpp_chn_attr[VPP_MAX_PHYCHN_NUM] ;
static vs_bool_t vpp_extchn_enable[VPP_MAX_EXTCHN_NUM] = {VS_FALSE};
static vs_vpp_extchn_attr_s vpp_extchn_attr[VPP_MAX_EXTCHN_NUM] = {0};
static volatile sig_atomic_t s_stop_flag = 0;
extern vs_int8_t g_bus_id[VII_MAX_ROUTE_NUM];
extern sample_sensor_type_e g_sensor_type[VII_MAX_ROUTE_NUM];

#if defined(RTSP_ENABLED)
static vs_uint32_t s_comm_vb_cnt = 38;
#else
static vs_uint32_t s_comm_vb_cnt = 18;
#endif
static vs_vii_vpp_mode_e s_vii_vpp_mode = E_VII_OFFLINE_VPP_ONLINE;

static sample_infra_algo_type_e s_algo_type = SAMPLE_INFRA_ALGO_CPU;
extern vs_size_s s_vo_size;
extern vs_uint32_t s_vo_fps;

extern vs_size_s s_vpp_size;

static volatile sig_atomic_t g_stop_flag = 0;
static vs_bool_t s_vo_dsp = VS_FALSE;

static vs_int32_t s_recfg_drop_cnt = 0;
static sample_infra_ooc_handle_s ooc_handle = {0};
extern void * g_dstptr;
extern char ddealgotype[];
extern int npuenable;


extern vs_size_s s_vpp_sr_size;
#ifdef VII_FROM_UVC
static vs_uvc_camera_info_s camera_info;
static int g_uvc_host_retcode = -1;
#endif
static char oocpath[256] = {0};

// Note 25<= INFRA_FPS <= 36
#define INFRA_FPS 30
#define T_REG (25*875/INFRA_FPS)

#ifndef DUAL_LENSES
#define LINE_COUNTER (3000)
#else
#define LINE_COUNTER (3000 * 93750 / 65625) //< (3000 * 93750000 / 65625000)
#endif

#define NUCR_START_BIT (86-32*2+8)
#define NUCR_MASK_BITS ~(0x00000003 << NUCR_START_BIT)

#define OSCR_START_BIT (85-32*2+8)
#define OSCR_MASK_BITS ~(0x00000001 << OSCR_START_BIT)

#define FOS_START_BIT (79-32*2+8)
#define FOS_MASK_BITS ~(0x0000003F << FOS_START_BIT)

#define AD_GAIN_START_BIT (66-32*2+8)
#define AD_GAIN_MASK_BITS ~(0x0000001F << AD_GAIN_START_BIT)

#define INT_CAP_START_BIT (37-32+8)
#define INT_CAP_MASK_BITS ~(0x0000007 << INT_CAP_START_BIT)

#define INTEGRATION_START_BIT (3)
#define INTEGRATION_MASK_BITS ~(0x000007F << INTEGRATION_START_BIT)

#define VII_INFRA_RAW_WIDTH 672
#if VII_INFRA_RAW_WIDTH > 656
#define OOC_WIDTH VII_INFRA_RAW_WIDTH
#else
#define OOC_WIDTH 656
#endif

#define OOC_HEIGHT 522

#define POCC_START_ROW 1
#define POCC_START_COL 6

#define COCC_START_ROW 0
#define COCC_START_COL 6
#define COCC_ROW_NUMS 1

#define ROCC_START_ROW 1
#define ROCC_START_COL 0
#define ROCC_COL_NUMS 1

#if 1
// 00000000098e80210000081f0007eeff
static sample_infra_sensor_cfg_s s_airvisen_ql64017_cfg = {
    .name = "QL64017",

    .width = VII_INFRA_RAW_WIDTH,
    .height = 520,
    .pixel_format = E_PIXEL_FORMAT_BAYER_16BPP,
    .fps = INFRA_FPS,//max 36 now

    .ooc_width = OOC_WIDTH,
    .ooc_height = OOC_HEIGHT,

    .raw_output_width = 640,
    .raw_output_height = 512,
    .raw_output_format = E_PIXEL_FORMAT_BAYER_16BPP,

    .algo_output_width = 640,
    .algo_output_height = 512,
    .algo_output_format = E_PIXEL_FORMAT_YUV_420SP,

    .type = E_VII_INFRARED_SENSOR_TYPE_0,  // airvisen
#ifdef VS_CARINA
    .reg_array = { ////
        // 0 - 3
        (T_REG/*875*/<<16)|(3000 /*3000*/<<0),//0x036b0bb8, // pixel_window_cfg0
        (2<<16)  |((0) + (30+(11+(12+2)/2)*7) + 7 + (VII_INFRA_RAW_WIDTH*7/2)),//0x00000932, // pixel_window_cfg1
        (4<<16)  |(523<<0),//0x0004020b, // pixel_window_cfg2
        (3<<16)  |(592<<0),//0x00030250, // pixel_window_cfg3
        // 4 - 9
        0x00000008,
        0x0000007c,
        (0xf<<16)|(VII_INFRA_RAW_WIDTH/8 <<0),//0x000f0052,
        (VII_INFRA_RAW_WIDTH<<16)|(0x7e <<0),//0x0280007e,
        0x00003008,
        0x00000030,
        //10
        (0x0 <<16)|((30+(11+(12+2)/2)*7) <<0),//0x0000006b,
        0x00001030,
        //12
        (VII_INFRA_RAW_WIDTH<<16)|(0x7e <<0),//0x0280007e,
        // // 0 - 3
        // 0x036b0bb8, // pixel_window_cfg0
        // 0x00000932, // pixel_window_cfg1
        // 0x0004020b, // pixel_window_cfg2
        // 0x00030250, // pixel_window_cfg3
        // // 4 - 12
        // 0x00000008,
        // 0x0000007c,
        // 0x000f0052,
        // 0x0280007e,
        // 0x00003008,
        // 0x00000030,
        // 0x0000006b,
        // 0x00001030,
        // 0x0280007e,
        // 13 - 25
        0x0001390a, // ari_cfg_data beg
        0x373613ba,
        0xf2853b57,
        0xffffa50c,
        0x0c035d82,
        0xa1701ff0,
        0x326e364c,
        0x9b6416c5,
        //0xb36db61b,
        //0x6c410080,
        //0x08045133,
        //0x3ec3a6bd,
        //0x001c4000, // ari_cfg_data end

        0x00,
        // 0x00000001,
        0x00000009,
        // 0x85003D00,
        0x8e801d00,
        // 0x8e802100,
        // 0x00002F00,
        0x00081f00,
        // 0x07DEFF,
        0x07eeff,
    },
    .reg_num = 26,
#endif
};
#else
static sample_infra_sensor_cfg_s s_airvisen_ql64017_cfg = {
    .name = "QL64017",

    .width = VII_INFRA_RAW_WIDTH,
    .height = 520,
    .pixel_format = E_PIXEL_FORMAT_BAYER_16BPP,
    .fps = INFRA_FPS,//max 36 now

    .ooc_width = OOC_WIDTH,
    .ooc_height = OOC_HEIGHT,

    .algo_output_width = 640,
    .algo_output_height = 512,
    .algo_output_format = E_PIXEL_FORMAT_YUV_420SP,

    .type = E_VII_INFRARED_SENSOR_TYPE_0,  // airvisen
#ifdef VS_CARINA
    .reg_array = { ////
        // 0 - 3
        (T_REG/*875*/<<16)|(LINE_COUNTER /*LINE_COUNTER*/<<0),//0x036b0bb8, // pixel_window_cfg0
        (2<<16)  |((0) + (30+(11+(12+2)/2)*7) + 7 + (VII_INFRA_RAW_WIDTH*7/2)),// pixel_window_cfg1
        (4<<16)  |(523<<0),//0x0004020b, // pixel_window_cfg2
        (3<<16)  |(592<<0),//0x00030250, // pixel_window_cfg3
        // 4 - 9
        0x00000008,
        0x0000007c,
        (0xf<<16)|(VII_INFRA_RAW_WIDTH/8 <<0),//0x000f0052,
        (VII_INFRA_RAW_WIDTH<<16)|(0x7e <<0),//0x0280007e,
        0x00003008,
        0x00000030,
        //10
        (0x0 <<16)|((30+(11+(12+2)/2)*7) <<0),//0x0000006b,
        0x00001030,
        //12
        (VII_INFRA_RAW_WIDTH<<16)|(0x7e <<0),//0x0280007e,
        // 13 - 25
        0x0001390a, // ari_cfg_data beg
        0x373613ba,
        0xf2853b57,
        0xffffa50c,
        0x0c035d82,
        0xa1701ff0,
        0x326e364c,
        0x9b6416c5, // ari_cfg_data end
        0x00,
        0x00000009,
        0x8e801d00,
        0x00081f00,
        0x07eeff,
    },
#endif
    .reg_num = 26,
};

#endif


static sample_infra_sensor_cfg_s *s_infra_sensor_cfg[] = {
    &s_airvisen_ql64017_cfg,

};


static vs_int32_t sample_infra_sensor_cfg_num_get()
{
    return sizeof(s_infra_sensor_cfg) / sizeof(s_infra_sensor_cfg[0]);
}

static const char * sample_infra_sensor_cfg_name_get(
        sample_infra_sensor_type_e type)
{
    return s_infra_sensor_cfg[type]->name;
}

static sample_infra_sensor_cfg_s * sample_infra_sensor_cfg_get(
        sample_infra_sensor_type_e type)
{
    return s_infra_sensor_cfg[type];
}


vs_int32_t infra_set_fos(uint32_t fos)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;
    uint32_t rel_fos;
    uint32_t fos_reg;

    sample_infra_sensor_cfg_s *p_sensor_cfg;

    rel_fos = fos << FOS_START_BIT;
    p_sensor_cfg = sample_infra_sensor_cfg_get(0);

    fos_reg = p_sensor_cfg->reg_array[23] & (FOS_MASK_BITS);
    fos_reg |= rel_fos;
    p_sensor_cfg->reg_array[23] = fos_reg;

    airvisen_trace("fos_reg:0x%x, fos:0x%x\n", fos_reg, fos);

    return ret;
}

vs_int32_t infra_set_integration(uint32_t integration)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;
    uint32_t rel_integration, integration_reg;

    sample_infra_sensor_cfg_s *p_sensor_cfg;

    rel_integration = integration << INTEGRATION_START_BIT;
    p_sensor_cfg = sample_infra_sensor_cfg_get(0);

    integration_reg = p_sensor_cfg->reg_array[25] & (INTEGRATION_MASK_BITS);
    integration_reg |= rel_integration;
    p_sensor_cfg->reg_array[25] = integration_reg;
    airvisen_trace("set integration, rel_val:0x%x, reg:0x%x\n", integration, integration_reg);

    return ret;
}

uint32_t infra_get_fos(void)
{
    uint32_t fos_reg;

    sample_infra_sensor_cfg_s *p_sensor_cfg;

    p_sensor_cfg = sample_infra_sensor_cfg_get(0);

    fos_reg = p_sensor_cfg->reg_array[23];
    fos_reg = (fos_reg >> FOS_START_BIT) & 0x3F;

    airvisen_trace("fos:0x%x\n", fos_reg);

    return fos_reg;
}

uint32_t infra_get_integration(void)
{
    uint32_t integration_reg;

    sample_infra_sensor_cfg_s *p_sensor_cfg;

    p_sensor_cfg = sample_infra_sensor_cfg_get(0);

    integration_reg = p_sensor_cfg->reg_array[25];
    integration_reg = (integration_reg >> INTEGRATION_START_BIT) & 0x7F;

    airvisen_trace("integration:0x%x\n", integration_reg);

    return integration_reg;
}


/**************************** util func ***************************************/

static vs_int32_t sample_infra_frame_stride_get(vs_pixel_format_e pixel_format,
        vs_uint32_t width, vs_compress_mode_e cmp_mode)
{
    vs_uint32_t bit_width = 0;
    vs_uint32_t stride = 0;
    vs_uint32_t line_align = 0;

    if ((pixel_format == E_PIXEL_FORMAT_BAYER_8BPP) ||
        (pixel_format == E_PIXEL_FORMAT_YVU_422SP)  ||
        (pixel_format == E_PIXEL_FORMAT_YUV_422SP)  ||
        (pixel_format == E_PIXEL_FORMAT_YUV_420SP)  ||
        (pixel_format == E_PIXEL_FORMAT_YVU_420SP)) {
        bit_width = 8;
    } else {
        bit_width = 16;
    }

    // compress 128 align
    // non-compress 16 align
    if (cmp_mode == E_COMPRESS_MODE_NONE) {
        line_align = 64;
        //line_align = 256;
    } else {
        line_align = 256;
    }

    stride = align_up((width * bit_width / 8), line_align);

    return stride;
}


    
    


/**************************** algo func ***************************************/

static vs_int32_t sample_infra_dsp_proc(vs_video_frame_info_s *p_src_frm,
        vs_video_frame_info_s *p_dst_frm)
{
#if !defined(VS_815) && !defined(VS_816)
    vs_int32_t ret;
    vs_dsp_message_s msg = {0};
    vs_dsp_id_e dsp_id = E_DSP_ID_0;
    vs_dsp_pri_e pri = (vs_dsp_pri_e)0;
    sample_infra_dsp_msg_s msg_body = {0};
    vs_nn_invoke_attr_s invoke_attr = { 0 };
    vs_uint32_t handle = 0;
    vs_bool_t finish = VS_FALSE;

    msg.cmd = 1026;
    msg.msgid = 0;
    msg.body = (vs_uint64_t)&msg_body;
    msg_body.src = *p_src_frm;
    msg_body.dst = *p_dst_frm;
    msg.len = sizeof(msg_body);

    ret = vs_mal_dsp_rpc(&handle, &msg, dsp_id, pri);
    if (ret != 0) {
        airvisen_trace("failed to vs_mal_dsp_rpc, ret = 0x%x handle: 0x%x\n",
                ret, handle);
    }
    ret = vs_mal_dsp_query(dsp_id, handle, &finish, VS_TRUE);
    if (ret != 0) {
        airvisen_trace("failed to vs_mal_dsp_query, ret = 0x%x finish: 0x%x\n",
                ret, finish);
    }

    *p_dst_frm = msg_body.dst;
#endif
    return VS_SUCCESS;
}

#if 1
static vs_int32_t sample_infra_cpu_proc(vs_video_frame_info_s *p_src_frm,
        vs_video_frame_info_s *p_dst_frm, infra_algo_params_s* algo_params)
{
    vs_uint32_t stride = 0, y_size = 0, uv_size = 0;
    vs_pixel_format_e dst_format = p_dst_frm->frame.pixel_format;
    
    sample_infra_sensor_cfg_s *p_sensor_cfg;
    p_sensor_cfg = sample_infra_sensor_cfg_get(0);

    vs_size_s dst_size = {0};

    dst_format = p_sensor_cfg->algo_output_format;
    dst_size.width = p_sensor_cfg->algo_output_width;
    dst_size.height = p_sensor_cfg->algo_output_height;


    //起始5行无效
    uint32_t src_stride = p_src_frm->frame.stride[0];
    uint32_t dst_width = dst_size.width;
 
    uint32_t src_frm_size = src_stride * p_src_frm->frame.height;
    
    vs_uint64_t  src_phys_addr;
    vs_void_t *src_virt_addr = NULL;

    src_phys_addr = p_src_frm->frame.phys_addr[0];
#ifdef INFRA_VII_SRC_FRM_USE_CACHED_MEM
    src_virt_addr = vs_mal_sys_mmap_cached(src_phys_addr, src_frm_size);
#else
    src_virt_addr = vs_mal_sys_mmap(src_phys_addr, src_frm_size);
#endif

    stride = sample_infra_frame_stride_get(dst_format,
                p_dst_frm->frame.width, E_COMPRESS_MODE_NONE);
    y_size = stride * p_dst_frm->frame.height;
    if (dst_format == E_PIXEL_FORMAT_YVU_422SP ||
            dst_format == E_PIXEL_FORMAT_YUV_422SP) {
        uv_size = y_size;
    } else if (dst_format == E_PIXEL_FORMAT_YVU_420SP ||
            dst_format == E_PIXEL_FORMAT_YUV_420SP) {
        uv_size = y_size / 2;
    } else {
        uv_size = 0;
    }
    
    airvisen_trace("src_stride:%d, dst_stride:%d\n", src_stride, stride);
#ifdef INFRA_VII_SRC_FRM_USE_CACHED_MEM
    /* invalidate cached mmz, cache mmz must be invalidate before read */
     int ret = vs_mal_sys_cache_invalidate(src_phys_addr, src_virt_addr, src_frm_size);
     if (ret != VS_SUCCESS) {
         airvisen_trace("vs_mal_sys_cache_invalidate failed with err 0x%x\n", ret);
         return VS_FAILED;
     }
#endif
    infra_isp_procedure(src_virt_addr, src_stride, (vs_void_t*)p_dst_frm->frame.virt_addr[0], stride); 
    memset((vs_void_t*)p_dst_frm->frame.virt_addr[1], 128, uv_size);
    
    vs_mal_sys_unmap(src_virt_addr, src_frm_size);

    return VS_SUCCESS;
}
#endif

static vs_void_t* sample_infra_algo_proc_thread(vs_void_t *arg)
{
    pthread_setname_np(pthread_self(), "sampleinfra");
    vs_int32_t ret = VS_FAILED;
    sample_infra_algo_handle_s *p_handle;
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    vs_int32_t srcpipe = VII_SRC_PIPE, dstpipe = VII_DST_PIPE;
    vs_uint32_t frame_num = 0;
    vs_vii_pipe_dump_frame_attr_s dump_attr = {VS_FALSE}, backup_dump_attr = {VS_FALSE};
    vs_video_frame_info_s src_frame = {0}, dst_frame = {0};
    const vs_video_frame_info_s *dst_frm_arr[2] = {0};

    VB_BLK dst_frm_vb;
    vs_uint64_t phys_addr;
    vs_void_t *virt_addr = NULL;
    vs_uint32_t src_stride;
    vs_uint32_t stride = 0, y_size = 0, uv_size = 0, dst_frm_size = 0;
    vs_pixel_format_e dst_format;
    vs_size_s dst_size = {0};


    p_handle = (sample_infra_algo_handle_s*)arg;
    infra_algo_params_s algo_params = p_handle->algo_params;
    p_sensor_cfg = sample_infra_sensor_cfg_get(p_handle->sensor_type);

    dst_format = p_sensor_cfg->algo_output_format;
    dst_size.width = p_sensor_cfg->algo_output_width;
    dst_size.height = p_sensor_cfg->algo_output_height;

#ifndef VII_VIRT_FROM_RAW_SEQUENCE
    src_stride = sample_infra_frame_stride_get(p_sensor_cfg->pixel_format, p_sensor_cfg->width, E_COMPRESS_MODE_NONE);
#else
    src_stride = sample_infra_frame_stride_get(p_sensor_cfg->raw_output_format, p_sensor_cfg->raw_output_width, E_COMPRESS_MODE_NONE);
#endif

    // prepare dst frame attr
    stride = sample_infra_frame_stride_get(dst_format,
                dst_size.width, E_COMPRESS_MODE_NONE);
    airvisen_trace("############## src_stride:%d, stride:%d\n", src_stride, stride);
    y_size = stride * dst_size.height;
    if (dst_format == E_PIXEL_FORMAT_YVU_422SP ||
            dst_format == E_PIXEL_FORMAT_YUV_422SP) {
        uv_size = y_size;
    } else if (dst_format == E_PIXEL_FORMAT_YVU_420SP ||
            dst_format == E_PIXEL_FORMAT_YUV_420SP) {
        uv_size = y_size / 2;
    } else {
        uv_size = 0;
    }
    dst_frm_size = y_size + uv_size;

    // set depth for dump
    ret = vs_mal_vii_pipe_dump_frame_attr_get(srcpipe, &backup_dump_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to get pipe %d dump frame attr\n", srcpipe);
        return VS_NULL;
    }
    dump_attr.enable = VS_TRUE;
    dump_attr.depth = 3;

    ret = vs_mal_vii_pipe_dump_frame_attr_set(srcpipe, &dump_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to set pipe %d dump frame attr\n", srcpipe);
    //    continue;
    }


    while (p_handle->is_start) {
        usleep(50);
#ifndef VII_VIRT_FROM_RAW_SEQUENCE
        ret = vs_mal_vii_pipe_frame_acquire(srcpipe, &src_frame, &frame_num, 50);
        if(ret){
            airvisen_trace("failed to vs_mal_vii_pipe_frame_acquire, ret=0x%x\n", ret);
            continue;
        }
#endif
        dst_frm_vb = vs_mal_vb_block_get(p_handle->vb_pool, dst_frm_size, NULL);
        if (VS_INVALID_VB_HANDLE == dst_frm_vb) {
            airvisen_trace("failed to get vb block for algo\n");
            vs_mal_vii_pipe_frame_release(srcpipe, &src_frame, frame_num);
            continue;
        }
        phys_addr = vs_mal_vb_handle2physaddr(dst_frm_vb);
        virt_addr = vs_mal_sys_mmap(phys_addr, dst_frm_size);

        src_frame.frame.width =  VII_INFRA_RAW_WIDTH;
        src_frame.frame.height = p_sensor_cfg->height;
        src_frame.frame.stride[0] = src_stride;
        src_frame.frame.stride[1] = src_stride;
        src_frame.frame.stride[2] = src_stride;

        dst_frame.modid = E_MOD_ID_VII;
        dst_frame.poolid = p_handle->vb_pool;
        dst_frame.frame.width = dst_size.width;
        dst_frame.frame.height = dst_size.height;
        dst_frame.frame.pixel_format = dst_format;
        dst_frame.frame.video_format = E_VIDEO_FORMAT_LINEAR;
        dst_frame.frame.compress_mode = E_COMPRESS_MODE_NONE;
        dst_frame.frame.dynamic_range = E_DYNAMIC_RANGE_SDR8;
        dst_frame.frame.color_gamut = src_frame.frame.color_gamut;
        dst_frame.frame.stride[0] = dst_size.width;
        dst_frame.frame.stride[1] = dst_size.width;
        dst_frame.frame.stride[2] = dst_size.width;
        dst_frame.frame.phys_addr[0] = phys_addr;
        dst_frame.frame.phys_addr[1] = phys_addr+ y_size;
        dst_frame.frame.phys_addr[2] = 0;
        dst_frame.frame.virt_addr[0] = (vs_uint64_t)virt_addr;
        dst_frame.frame.virt_addr[1] = (vs_uint64_t)(virt_addr + y_size);
        dst_frame.frame.virt_addr[2] = 0;

        // Fill Message
        if (s_algo_type == SAMPLE_INFRA_ALGO_DSP) {
            sample_infra_dsp_proc(&src_frame, &dst_frame);
        } else if (s_algo_type == SAMPLE_INFRA_ALGO_CPU) {
#ifndef VII_VIRT_FROM_RAW_SEQUENCE
            WALLTIMETRACKING
            sample_infra_cpu_proc(&src_frame, &dst_frame, &algo_params);
            WALLTIMESTAT("infra_cpu_proc:")
#else
            int ret = infra_isp_procedure(NULL, src_stride, (vs_void_t*)dst_frame.frame.virt_addr[0], stride); 
            if (ret == -1)
            {
                //< end of raw sequence
                break;
            }
            memset((vs_void_t*)dst_frame.frame.virt_addr[1], 128, uv_size);
#endif
        }

        dst_frame.frame.stride[0] = stride;
        dst_frame.frame.stride[1] = stride;
        dst_frame.frame.stride[2] = stride;
       
        dst_frm_arr[0] = &dst_frame;

        airvisen_trace("pixel format: %d\n", dst_frame.frame.pixel_format);
        airvisen_trace("infra thread id: %u, pid:%u, tid:%u\n", (unsigned int)(p_handle->algo_thread), (unsigned int)(getpid()), (unsigned int)(pthread_self()));
        if (s_recfg_drop_cnt == 0) {
            ret = vs_mal_vii_pipe_frame_send(dstpipe, dst_frm_arr, 1, 0);
            if (ret != VS_SUCCESS) {
                airvisen_trace("failed to send frame to pipe %d, ret = 0x%x\n", dstpipe, ret);
            }
        } else {
            s_recfg_drop_cnt--;
        }

#ifndef VII_VIRT_FROM_RAW_SEQUENCE
        vs_mal_vii_pipe_frame_release(srcpipe, &src_frame, frame_num);
#endif
        vs_mal_sys_unmap(virt_addr, dst_frm_size);
        vs_mal_vb_block_release(dst_frm_vb);
    }

    return VS_NULL;
}

static vs_int32_t sample_infra_algo_proc_start(sample_infra_algo_handle_s *p_handle)
{
    vs_int32_t ret;
    sample_infra_sensor_cfg_s *p_sensor_cfg;
    vs_uint32_t dst_frm_size = 0;
    vs_vb_pool_cfg_s dst_pool_cfg = {0};
    vs_pixel_format_e dst_format ;
    vs_size_s dst_size;
    sample_infra_nn_handle_s nn_proc;
    sample_infra_algo_handle_s *nn_handle;
    

    pthread_attr_t pthread_attr = {0} ,pthread_attr_nn = {0} ;
    struct sched_param param = {0} , param_nn = {0};

    p_sensor_cfg = sample_infra_sensor_cfg_get(p_handle->sensor_type);
    dst_format = p_sensor_cfg->algo_output_format;
    dst_size.width = p_sensor_cfg->algo_output_width;
    dst_size.height = p_sensor_cfg->algo_output_height;

    // dst vb pool create
    dst_frm_size = sample_common_buffer_size_get(&dst_size,
            dst_format, E_COMPRESS_MODE_NONE, 1);
    dst_pool_cfg.blk_cnt = 3;
    dst_pool_cfg.blk_size = dst_frm_size;
    dst_pool_cfg.remap_mode = VB_REMAP_MODE_NONE;
    p_handle->vb_pool = vs_mal_vb_pool_create(&dst_pool_cfg);
    if (VS_INVALID_POOLID == p_handle->vb_pool) {
        airvisen_trace("failed to create pool for algo\n");
        goto exit0;
    }

    // pthread create
    pthread_attr_init(&pthread_attr);
    param.sched_priority = 60;
    pthread_attr_setschedpolicy(&pthread_attr, SCHED_FIFO);
    pthread_attr_setschedparam(&pthread_attr, &param);
    pthread_attr_setinheritsched(&pthread_attr, PTHREAD_EXPLICIT_SCHED);

   
    if (s_algo_type == SAMPLE_INFRA_ALGO_DSP && !s_vo_dsp) {
        ret = sample_common_dsp_init(DSP_ID, (vs_char_t*)"/lib/firmware/vs_dsp0.bin");
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to init dsp\n");
            goto exit1;
        }
    }
    p_handle->is_start = VS_TRUE;

    pthread_create(&p_handle->algo_thread, &pthread_attr,
            sample_infra_algo_proc_thread, p_handle);
 

    return VS_SUCCESS;

exit1:
    vs_mal_vb_pool_destory(p_handle->vb_pool);
exit0:
    return VS_FAILED;
}

static vs_void_t sample_infra_algo_proc_stop(sample_infra_algo_handle_s *p_handle)
{
    p_handle->is_start = VS_FALSE;

    pthread_join(p_handle->algo_thread, VS_NULL);
    p_handle->algo_thread = 0;

    if (s_algo_type == SAMPLE_INFRA_ALGO_DSP && !s_vo_dsp) {
        sample_common_dsp_exit(DSP_ID);
    }
}

static vs_void_t sample_infra_algo_pool_destroy(sample_infra_algo_handle_s *p_handle)
{
    if (p_handle->vb_pool != VS_INVALID_POOLID) {
        vs_mal_vb_pool_destory(p_handle->vb_pool);
    }
}

/**************************** vii func ***************************************/

static vs_void_t sample_infra_vii_config_get(sample_infra_sensor_type_e type,
        sample_vii_cfg_s *p_vii_cfg)
{
    sample_vii_route_cfg_s *p_route_cfg = &p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID];
    sample_infra_sensor_cfg_s *p_sensor_cfg = sample_infra_sensor_cfg_get(type);
    sample_vii_pipe_cfg_s *p_pipe_cfg;

    //memset(p_vii_cfg, 0, sizeof(sample_vii_cfg_s));
    p_vii_cfg->vii_vpp_mode = s_vii_vpp_mode;

    //p_vii_cfg->route_num = 1; //< @Aaron, don't open it when using DUAL_LENS

    p_route_cfg->lane_mode = E_LANE_MODE_4_2_2;
    p_route_cfg->mipi_rx_attr.mode = E_MODE_INFRARED;
    p_route_cfg->dev_id = VII_INFRA_DEV;
    p_route_cfg->dev_attr.intf_type = E_VII_INTF_INFRARED;
    p_route_cfg->dev_attr.pixel_format = p_sensor_cfg->pixel_format;
    p_route_cfg->dev_attr.infrared_attr.type = p_sensor_cfg->type;
    p_route_cfg->dev_attr.infrared_attr.fps = p_sensor_cfg->fps;
    p_route_cfg->dev_attr.infrared_attr.reg_num = p_sensor_cfg->reg_num;
    memcpy(p_route_cfg->dev_attr.infrared_attr.reg_array,
            p_sensor_cfg->reg_array,
            VII_MAX_INFRARED_REG_NUM * sizeof(vs_uint32_t));

    p_route_cfg->vc_attr.vc[0] = E_VII_VC_ID_0;

    p_route_cfg->pipe_num = 2;
    p_route_cfg->pipe_id[0] = VII_SRC_PIPE;
    p_route_cfg->pipe_id[1] = VII_DST_PIPE;

    p_pipe_cfg = &p_route_cfg->pipe_cfg[0];
    p_pipe_cfg->pipe_id = p_route_cfg->pipe_id[0];
    p_pipe_cfg->pipe_attr.bypass_mode = E_VII_PIPE_BYPASS_FE;
    p_pipe_cfg->pipe_attr.image_size.width = p_sensor_cfg->width;
    p_pipe_cfg->pipe_attr.image_size.height = p_sensor_cfg->height;
    p_pipe_cfg->pipe_attr.real_fps = p_sensor_cfg->fps;
    p_pipe_cfg->pipe_attr.pixel_format = p_sensor_cfg->pixel_format;
    p_pipe_cfg->pipe_attr.wdr_mode = E_WDR_MODE_NONE;
    p_pipe_cfg->pipe_attr.compress_mode = E_COMPRESS_MODE_NONE;
    p_pipe_cfg->pipe_attr.framerate.src_framerate = -1;
    p_pipe_cfg->pipe_attr.framerate.dst_framerate = -1;

    p_pipe_cfg = &p_route_cfg->pipe_cfg[1];
    p_route_cfg->pipe_cfg[1] = p_route_cfg->pipe_cfg[0];
    p_pipe_cfg->pipe_id = p_route_cfg->pipe_id[1];
    p_pipe_cfg->pipe_attr.bypass_mode = E_VII_PIPE_BYPASS_FE;
    p_pipe_cfg->pipe_attr.image_size.width = p_sensor_cfg->algo_output_width;
    p_pipe_cfg->pipe_attr.image_size.height = p_sensor_cfg->algo_output_height;
}



 vs_int32_t sample_infra_vii_start(sample_vii_cfg_s *p_vii_cfg)
{
    vs_int32_t ret;
    vs_isp_nr3d_attr_s nr3d_attr;
    vs_isp_presharpen_attr_s presharpen_attr;
    vs_isp_postsharpen_attr_s postsharpen_attr;

    
    ret = sample_common_vii_start(p_vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start common vii\n");
        goto exit0;
    }


    vs_mal_vii_pipe_src_set(p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[0],
            E_VII_PIPE_SOURCE_USER);
    vs_mal_vii_pipe_src_set(p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1],
            E_VII_PIPE_SOURCE_USER);

    
    // enable nr3d
    ret = vs_mal_isp_nr3d_attr_get(
            p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1], &nr3d_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to get isp nr3d attr of pipe %d\n",
                p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1]);
        goto exit1;
    }
    nr3d_attr.enable = VS_FALSE;
    ret = vs_mal_isp_nr3d_attr_set(
            p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1], &nr3d_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to set isp nr3d attr of pipe %d\n",
                p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1]);
        goto exit1;
    }

    // enable presharpen
    ret = vs_mal_isp_presharpen_attr_get(
            p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1], &presharpen_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to get isp presharpen attr of pipe %d\n",
                p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1]);
        goto exit1;
    }
    presharpen_attr.enable = VS_FALSE;
    ret = vs_mal_isp_presharpen_attr_set(
            p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1], &presharpen_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to set isp presharpen attr of pipe %d\n",
                p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1]);
        goto exit1;
    }

    // enable postsharpen
    ret = vs_mal_isp_postsharpen_attr_get(
            p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1], &postsharpen_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to get isp postsharpen attr of pipe %d\n",
                p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1]);
        goto exit1;
    }
    postsharpen_attr.enable = VS_FALSE;
    ret = vs_mal_isp_postsharpen_attr_set(
            p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1], &postsharpen_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to set isp postsharpen attr of pipe %d\n",
                p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].pipe_id[1]);
        goto exit1;
    }

    
    return VS_SUCCESS;

exit1:
    sample_common_vii_stop(p_vii_cfg);

exit0:
    return VS_FAILED;
}

static vs_void_t sample_infra_vii_stop(sample_vii_cfg_s *p_vii_cfg)
{
    sample_common_vii_stop(p_vii_cfg);
}


/***************************** ooc func ***************************************/
vs_int32_t infra_update_ooc(sample_infra_ooc_handle_s *p_handle, uint8_t * ooc_src)
{
    vs_int32_t i, j, ret = VS_SUCCESS;
    sample_infra_sensor_cfg_s *p_sensor_cfg = sample_infra_sensor_cfg_get(0);

    //sample_infra_ooc_handle_s *p_handle = &ooc_handle;

    airvisen_trace("2. virt_addr:%p\n", p_handle->virt_addr);
    vs_uint8_t* ooc_virt_addr = (vs_uint8_t*)p_handle->virt_addr;

    vs_int32_t ooc_height = p_sensor_cfg->ooc_height;
    vs_int32_t ooc_width = p_sensor_cfg->ooc_width;

    airvisen_trace("3. ooc_virt_addr:%p\n", ooc_virt_addr);

    if(ooc_virt_addr != NULL){
        memcpy((vs_uint8_t*)ooc_virt_addr, (vs_uint8_t*)ooc_src, ooc_width*ooc_height);
        airvisen_trace("memcpy %d bytes!\n", ooc_width * ooc_height);
    }
    else{
        airvisen_trace("ooc mem no init, update ooc failed!\r\n")
        ret = VS_FAILED;
    }


    airvisen_trace("3.****************************************\n"); 
    return ret;
}

static vs_void_t sample_infra_ooc2mem(vs_void_t *ooc_virt_addr,
        vs_uint32_t ooc_width, vs_uint32_t ooc_height)
{
    vs_int32_t i, j;

    for (i = 0; i < ooc_height; i++) {
        for (j = 0; j < ooc_width; j++) {
            /* for debug
            if (i == 0 || i == 519 || j == 0 || j == 639) {
                ((vs_uint8_t*)ooc_virt_addr)[i * 656 + j] = 0;
            } else {
                ((vs_uint8_t*)ooc_virt_addr)[i * 656 + j] = 63;
            }
            */
            ((vs_uint8_t*)ooc_virt_addr)[i * ooc_width + j] = 32;
        }
    }
}

static vs_int32_t sample_infra_ooc_init(sample_infra_sensor_type_e sensor_type,
        sample_infra_ooc_handle_s *p_handle)
{
    //const char *ooc_file = "12345678.ooc";
    const char *ooc_file = oocpath;
    vs_vb_pool_cfg_s pool_cfg = {0};
    sample_infra_sensor_cfg_s *p_sensor_cfg =
        sample_infra_sensor_cfg_get(sensor_type);

    p_handle->buf_size = p_sensor_cfg->ooc_width * p_sensor_cfg->ooc_height;

    pool_cfg.blk_cnt = 1;
    pool_cfg.blk_size = p_handle->buf_size;
    pool_cfg.remap_mode = VB_REMAP_MODE_NONE;

    p_handle->vb_pool = vs_mal_vb_pool_create(&pool_cfg);
    if (VS_INVALID_POOLID == p_handle->vb_pool) {
        airvisen_trace("failed to create ooc vb pool\n");
        goto exit1;
    }
    p_handle->vb_blk = vs_mal_vb_block_get(p_handle->vb_pool,
            p_handle->buf_size, NULL);
    if (VS_INVALID_VB_HANDLE == p_handle->vb_blk) {
        airvisen_trace("failed to get ooc vb block\n");
        goto exit2;
    }
    p_handle->phys_addr = vs_mal_vb_handle2physaddr(p_handle->vb_blk);
    p_handle->virt_addr = vs_mal_sys_mmap(p_handle->phys_addr, p_handle->buf_size);

    //const char *ooc_file = "12345678.ooc";
    if(access(ooc_file, R_OK)!=-1) {
        uint8_t *p_buffer = (uint8_t*)malloc(p_handle->buf_size);
        airvisen_trace("ooc file %s found! load it. buf  size:%d\r\n", ooc_file, p_handle->buf_size);
        airvisen_trace("\n\n\nooc file %s found! load it.\r\n", ooc_file);
        FILE *ooc_ptr = fopen(ooc_file,"rb");
        fread(p_buffer, p_handle->buf_size, 1, ooc_ptr);
        fclose(ooc_ptr);
        infra_update_ooc(p_handle, p_buffer);
        free(p_buffer);
    }
    else {
        airvisen_trace("ooc file no found! load default value.\r\n");
        airvisen_trace("\n\n\nooc file no found! load default value.\r\n\n\n");
        sample_infra_ooc2mem(p_handle->virt_addr,
                p_sensor_cfg->ooc_width, p_sensor_cfg->ooc_height);
    }
    return VS_SUCCESS;

    vs_mal_sys_unmap(p_handle->virt_addr, p_handle->buf_size);
    vs_mal_vb_block_release(p_handle->vb_blk);
exit2:
    vs_mal_vb_pool_destory(p_handle->vb_pool);
exit1:
    return VS_FAILED;
}

static vs_void_t sample_infra_ooc_exit(sample_infra_ooc_handle_s *p_handle)
{
    vs_mal_sys_unmap(p_handle->virt_addr, p_handle->buf_size);
    vs_mal_vb_block_release(p_handle->vb_blk);
    vs_mal_vb_pool_destory(p_handle->vb_pool);
}


/**************************** vpp func ***************************************/

static vs_void_t sample_infra_vpp_grp_attr_get(
        sample_infra_sensor_type_e sensor_type, vs_vpp_grp_attr_s *grp_attr)
{
    sample_infra_sensor_cfg_s *p_sensor_cfg =
        sample_infra_sensor_cfg_get(sensor_type);

    // grp_attr->max_width = p_sensor_cfg->algo_output_width;
    // grp_attr->max_height = p_sensor_cfg->algo_output_height;
    grp_attr->max_width = s_vpp_size.width;
    grp_attr->max_height = s_vpp_size.height;
    grp_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    grp_attr->pixel_format = p_sensor_cfg->algo_output_format;
    grp_attr->framerate.dst_framerate = -1;
    grp_attr->framerate.src_framerate = -1;
}


static vs_void_t sample_infra_vpp_chn_attr_get(
        sample_infra_sensor_type_e sensor_type, vs_vpp_chn_attr_s *chn_attr)
{
    sample_infra_sensor_cfg_s *p_sensor_cfg =
        sample_infra_sensor_cfg_get(sensor_type);

    chn_attr->chn_mode = E_VPP_CHN_MODE_USER;
    // chn_attr->width = p_sensor_cfg->algo_output_width;
    // chn_attr->height = p_sensor_cfg->algo_output_height;
    chn_attr->width = s_vpp_size.width;
    chn_attr->height = s_vpp_size.height;
    chn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
    chn_attr->pixel_format = p_sensor_cfg->algo_output_format;
    chn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    chn_attr->compress_mode = E_COMPRESS_MODE_NONE;

    chn_attr->framerate.src_framerate = -1;
    chn_attr->framerate.dst_framerate = -1;
#ifndef VII_FROM_UVC
    chn_attr->mirror_enable = VS_TRUE;
#else
    chn_attr->mirror_enable = VS_FALSE;
#endif

    chn_attr->flip_enable = VS_FALSE;
    chn_attr->depth = 3;
    chn_attr->aspect_ratio.mode = E_ASPECT_RATIO_MODE_NONE;
}

static vs_void_t sample_infra_vpp_chn_nn_attr_get(
        sample_infra_sensor_type_e sensor_type, vs_vpp_chn_attr_s *chn_attr)
{
    sample_infra_sensor_cfg_s *p_sensor_cfg =
        sample_infra_sensor_cfg_get(sensor_type);

    chn_attr->chn_mode = E_VPP_CHN_MODE_USER;
    // chn_attr->width = p_sensor_cfg->algo_output_width;
    // chn_attr->height = p_sensor_cfg->algo_output_height;
    chn_attr->width = get_nn_chn_width();// 512;
    chn_attr->height = get_nn_chn_height(); //288;
    chn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
    chn_attr->pixel_format = p_sensor_cfg->algo_output_format;
    chn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    chn_attr->compress_mode = E_COMPRESS_MODE_NONE;

    chn_attr->framerate.src_framerate = -1;
    chn_attr->framerate.dst_framerate = -1;

    #if defined (DUAL_LENSES) || defined (VII_FROM_UVC)
        chn_attr->mirror_enable = VS_FALSE;
    #else 
        chn_attr->mirror_enable = VS_TRUE;
    #endif
    chn_attr->flip_enable = VS_FALSE;
    chn_attr->depth = 3;
    chn_attr->aspect_ratio.mode = E_ASPECT_RATIO_MODE_NONE;
}

static vs_void_t sample_infra_vpp_chn_sr_attr_get(
        sample_infra_sensor_type_e sensor_type, vs_vpp_chn_attr_s *chn_attr)
{
    sample_infra_sensor_cfg_s *p_sensor_cfg =
        sample_infra_sensor_cfg_get(sensor_type);

    chn_attr->chn_mode = E_VPP_CHN_MODE_USER;
    // chn_attr->width = p_sensor_cfg->algo_output_width;
    // chn_attr->height = p_sensor_cfg->algo_output_height;
    // chn_attr->width = get_nn_chn_width();// 512;
    // chn_attr->height = get_nn_chn_height(); //288;
    chn_attr->width = 512 ;// 512;
    chn_attr->height = 288; //288;
    chn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
    chn_attr->pixel_format = p_sensor_cfg->algo_output_format;
    chn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    chn_attr->compress_mode = E_COMPRESS_MODE_NONE;

    chn_attr->framerate.src_framerate = -1;
    chn_attr->framerate.dst_framerate = -1;
    #ifdef DUAL_LENSES
        chn_attr->mirror_enable = VS_FALSE;
    #else 
        chn_attr->mirror_enable = VS_TRUE;
    #endif
    chn_attr->flip_enable = VS_FALSE;
    chn_attr->depth = 3;
    chn_attr->aspect_ratio.mode = E_ASPECT_RATIO_MODE_NONE;
}



static vs_void_t sample_infra_vpp_extchn_attr_get(vs_int32_t bind_chnid,
        sample_infra_sensor_type_e sensor_type,
        vs_vpp_extchn_attr_s *p_extchn_attr)
{
    sample_infra_sensor_cfg_s *p_sensor_cfg =
        sample_infra_sensor_cfg_get(sensor_type);

    p_extchn_attr->bind_chnid = bind_chnid;
    p_extchn_attr->width = s_vpp_size.width;
    p_extchn_attr->height = s_vpp_size.height;
    p_extchn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
    p_extchn_attr->pixel_format = p_sensor_cfg->algo_output_format;
    p_extchn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    p_extchn_attr->compress_mode = E_COMPRESS_MODE_NONE;
    p_extchn_attr->framerate.src_framerate = -1;
    p_extchn_attr->framerate.dst_framerate = -1;
    p_extchn_attr->depth = 6;
}

/****************************** vo func ***************************************/

static vs_void_t sample_infra_vo_cfg_get(sample_infra_sensor_type_e sensor_type, sample_vo_cfg_s *vo_cfg)
{
    sample_infra_sensor_cfg_s *p_sensor_cfg =
        sample_infra_sensor_cfg_get(sensor_type);

    vo_cfg->vo_devid = 0;
    vo_cfg->vo_layerid = 0;

    vo_cfg->vo_intf_type = E_VO_INTERFACE_TYPE_HDMI;

    if (s_vo_size.width > 2560)
    {
        printf("\n\n ------  4K mode!!!!  ------\n");


        switch (s_vo_fps)
        {
            case 25:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_3840x2160_25; 
                break;
            case 30:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_3840x2160_30; 
                break;
            case 50:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_3840x2160_50; 
                break;
            case 60:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_3840x2160_60; 
                break;
            default:
                printf("error for vo type setting, please check!\n");
                break;
        }
    }
    else if ((s_vo_size.width > 1920) || (s_vo_size.height > 1080))
    {
        printf("\n\n ------  2K mode!!!!  ------\n");


        switch (s_vo_fps)
        {
            case 30:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_2560x1440_30; 
                break;
            case 60:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_2560x1440_60; 
                break;
            default:
                printf("error for vo type setting, please check!\n");
                break;
        }
    }
    else
    {
        switch(s_vo_fps)
        {
            case 25:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_1080P25;
                break;
            case 30:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_1080P30;
                break;
            case 50:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_1080P50;
                break;
            case 60:
                vo_cfg->vo_output = E_VO_OUTPUT_TYPE_1080P60;
                break;
            default:
                printf("error for vo type setting, please check!\n");
                break;
        }
    }

    vo_cfg->bg_color = 0;
    vo_cfg->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    vo_cfg->pixel_format = p_sensor_cfg->algo_output_format;
    vo_cfg->vo_mode = E_VO_MODE_1MUX;

    vo_cfg->img_width = s_vo_size.width;
    vo_cfg->img_height = s_vo_size.height;

    vo_cfg->enable = VS_TRUE;
    vo_cfg->zorder = 0;

#ifdef VS_ORION
    vo_cfg->vo_intf_type = E_VO_INTERFACE_TYPE_MIPI;
    vo_cfg->vo_output = E_VO_OUTPUT_TYPE_USER;
#endif
}


/****************************** case ******************************************/
vs_int32_t infra_set_nucr(uint32_t nucr)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;
    uint32_t rel_nucr;
    uint32_t nucr_reg;

    sample_infra_sensor_cfg_s *p_sensor_cfg;

    rel_nucr = nucr << NUCR_START_BIT;
    p_sensor_cfg = sample_infra_sensor_cfg_get(0);

    nucr_reg = p_sensor_cfg->reg_array[23] & (NUCR_MASK_BITS);
    nucr_reg |= rel_nucr;
    p_sensor_cfg->reg_array[23] = nucr_reg;

    airvisen_trace("nucr_reg:0x%x, nucr:0x%x\n", nucr_reg, nucr);

    return ret;
}
vs_int32_t sample_infra_init(sample_vii_cfg_s *p_vii_cfg)
{
    sample_infra_sensor_type_e sensor_type = 0;
    vs_int32_t ret = VS_SUCCESS, i = 0;
    vs_uint32_t frame_num = 1;
    vs_pixel_format_e format;
    // sample_infra_ooc_handle_s ooc_handle = {0};
    static int ooc_load_once=0;

    // vii start
    infra_set_nucr(0x3);
    sample_infra_vii_config_get(sensor_type, p_vii_cfg);
    // if(ooc_load_once == 0)
    {
        ooc_load_once = 1;

        ret = sample_infra_ooc_init(sensor_type, &ooc_handle);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to init ooc\n");
            goto exit1;
        }
    }
    p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_addr = ooc_handle.phys_addr;
    p_vii_cfg->route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_size = ooc_handle.buf_size;

    return VS_SUCCESS;
exit2:
    sample_infra_ooc_exit(&ooc_handle);
exit1:
exit0:
    return ret;

}

vs_int32_t infra_proc_destory()
{


#ifdef TRACKER_ENABLED
#ifndef VII_FROM_UVC_RGB
    return 0;
#endif
#endif

    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = IR_VPP_CHN_ID, vpp_extchnid = IR_VPP_EXTCHN_ID, vpp_chnid_nn = IR_VPP_NN_CHN_ID;
    vs_int32_t vo_devid = IR_VO_DEV_ID, vo_chnid = IR_VO_CHN_ID;
    vs_int32_t vii_dst_pipe = VII_DST_PIPE, vii_chnid = IR_VII_CHN_ID;
#ifdef RTSP_ENABLED 
    vs_int32_t venc_devid = 0;
    vs_int32_t venc_chnid[1] = {0};
    //vs_payload_type_e encode_type[1] = {E_PT_TYPE_H265};
    //vs_venc_profile_e profile[1] = {E_VENC_PROFILE_H265_MAIN};
    //sample_brc_mode_e brc_mode = E_VENC_BRC_CBR;
    //vs_venc_gop_mode_e gop_mode = E_VENC_GOP_MODE_NORMP;
    //vs_venc_gop_attr_s gop_attr;
#endif
    // PRINTLINE
    vs_int32_t ret = VS_SUCCESS;

#ifdef VII_FROM_UVC
    // PRINTLINE
    if (g_uvc_host_retcode == VS_SUCCESS)
    {
        //g_uvc_host_retcode = E_INFRA_UVC_HOST_STATUS_STOP | E_INFRA_UVC_HOST_STATUS_DEINIT | E_INFRA_UVC_HOST_STATUS_CLOSE;
        g_uvc_host_retcode = 0xFF;

    }
    uvc_host_infra_case_stop(&camera_info, g_uvc_host_retcode);
    // PRINTLINE
#else
    sample_infra_algo_proc_stop(&algo_handle);
    sleep(1);//< sleep in case of is during writing raw to disk

    QL_NUC_CLEAN_UP();//< Aaron, clean up the global memptr malloced inside NUC module

    if (strcmp(ddealgotype, "tianchaoalgo") == 0)
    {
        airvisenalgo2_cleanup();
    }
    else
    {
        DDE_CLEAN_UP();//< Aaron, clean up the global memptr malloced inside DDE module
    }

    free(g_dstptr);
    g_dstptr = NULL;
#endif


    // PRINTLINE
#ifdef RTSP_ENABLED
    vs_sample_trace("exit_venc_acquire_stream_stop \n");

    vs_sample_trace("exit_vpp_unbind_venc0\n");
    // PRINTLINE
    sample_common_vpp_unbind_venc(vpp_grpid, vpp_chnid, venc_devid, venc_chnid[0]);
exit_venc0_stop:
    vs_sample_trace("exit_venc0_stop\n");
    // PRINTLINE
    sample_common_venc_stop(venc_chnid[0]);
    // PRINTLINE
#else
    sample_common_vo_stop(&vo_cfg);
    if (vo_cfg.vo_intf_type == E_VO_INTERFACE_TYPE_MIPI) {
        sample_common_dsp_exit(DSP_ID);
    }
    #ifdef DUAL_LENSES 
        vs_mal_vpp_chn_disable(2, 10);
    #else 
        sample_common_vpp_unbind_vo(vpp_grpid, vpp_extchnid + VPP_MAX_PHYCHN_NUM, vo_devid, vo_chnid);
    #endif
#endif //< RTSP_ENABLED
    // PRINTLINE

#ifdef DUAL_LENSES 
    vs_mal_vpp_chn_disable(vpp_grpid, vpp_extchnid + VPP_MAX_PHYCHN_NUM);
    sample_common_vpp_stop(vpp_grpid, vpp_chn_enable);
    sample_common_vii_unbind_vpp(2, 2, 2);
#endif
#ifdef TRACKER_ENABLED
    sample_common_vii_unbind_vpp(2, 2, 2);
#endif
    
    // PRINTLINE
#ifndef VII_FROM_UVC
    sample_common_vii_unbind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
    sample_infra_vii_stop(&vii_cfg);
    sample_infra_algo_pool_destroy(&algo_handle);
    sample_infra_ooc_exit(&ooc_handle);
#endif
    // PRINTLINE
    sample_common_sys_exit();
    // PRINTLINE

    return ret;
}

static vs_int32_t sample_infra_case(sample_infra_sensor_type_e sensor_type, double dde_threshold = 5.0f)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    algo_handle.vb_pool = VS_INVALID_POOLID;

    vs_vpp_grp_attr_s vpp_grp_attr = {0};

    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = IR_VPP_CHN_ID, vpp_extchnid = IR_VPP_EXTCHN_ID, vpp_chnid_nn = IR_VPP_NN_CHN_ID, vpp_chnid_sr = IR_VPP_SR_CHN_ID  ;
    vs_int32_t vo_devid = IR_VO_DEV_ID, vo_chnid = IR_VO_CHN_ID;
    vs_int32_t vii_dst_pipe = VII_DST_PIPE, vii_chnid = IR_VII_CHN_ID;
    vs_uint32_t g_sensor_framerate = 30;
    sample_vii_cfg_s *p_vii_cfg = &vii_cfg;

    //< infra registers set , integration, fos etc
    uint32_t fos, integration;

    fos = GetIniKeyInt("ql64017","fos", g_strConfigIni);
    integration = GetIniKeyInt("ql64017","integration", g_strConfigIni);
    char *ptroocpath = GetIniKeyString("ql64017","oocpath", g_strConfigIni);

    memcpy(oocpath, ptroocpath, 255);
    airvisen_trace("fos:%d, integration:%d, oocpath:%s\n", fos, integration, oocpath);

    infra_set_fos(fos);
    infra_set_integration(integration);

    p_sensor_cfg = sample_infra_sensor_cfg_get(sensor_type);
    format = p_sensor_cfg->pixel_format;
    if (p_sensor_cfg->width > p_sensor_cfg->algo_output_width) {
        img_size.width = p_sensor_cfg->width;
        img_size.height = p_sensor_cfg->height;
    } else {
        img_size.width = p_sensor_cfg->algo_output_width;
        img_size.height = p_sensor_cfg->algo_output_height;
    }
    if (img_size.width < s_vo_size.width) {
        img_size = s_vo_size;
    }

    // sys init
    vb_cfg.pool_cnt = 1;
    for (i = 0; i < vb_cfg.pool_cnt; i++) {
        vb_cfg.ast_commpool[i].blk_size =
            sample_common_buffer_size_get(&img_size, format,
                    E_COMPRESS_MODE_NONE, frame_num);
        vb_cfg.ast_commpool[i].blk_cnt = s_comm_vb_cnt;
        vb_cfg.ast_commpool[i].remap_mode = VB_REMAP_MODE_NONE;
    }

    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        return ret;
    }

    // vii start
    memset(&vii_cfg, 0, sizeof(sample_vii_cfg_s));
    if (npuenable == 1)
    {
        vii_cfg.vii_vpp_mode = E_VII_OFFLINE_VPP_OFFLINE;
    }
    else
    {
        vii_cfg.vii_vpp_mode = E_VII_OFFLINE_VPP_ONLINE;

    }
    vii_cfg.route_num = 1;
    //s_vii_vpp_mode = vii_cfg.vii_vpp_mode;

    ret = sample_infra_init(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to init ooc\n");
        return ret;
    }

    ret = sample_infra_vii_start(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vii\n");
        return ret;
    }

    // vpp start
    ret = sample_common_vii_bind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vii vpp\n");
        return ret;
    }

    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);
   
    vpp_chn_enable[vpp_chnid] = VS_TRUE;

    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
    if (npuenable==1)
    {
        vpp_chn_enable[vpp_chnid_nn] = VS_TRUE;
        sample_infra_vpp_chn_nn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid_nn]);

//        vpp_extchn_enable[vpp_extchnid] = VS_TRUE;
//
//        sample_infra_vpp_extchn_attr_get(vpp_chnid, sensor_type,
//                &vpp_extchn_attr[vpp_extchnid]);
    }
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        return ret;
    }

//    if (npuenable==1)
//    {
//        ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
//                vpp_extchn_enable, vpp_extchn_attr);
//        if (ret != VS_SUCCESS) {
//            airvisen_trace("failed to set vpp extchn attr\n");
//            return ret;
//        }
//    }

    //airvisen_trace("vii_cfg.vii_vpp_mode:%d\n", vii_cfg.vii_vpp_mode);
    //if (vii_cfg.vii_vpp_mode == E_VII_OFFLINE_VPP_ONLINE)
    if (npuenable==0)
    {
        // vo start
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                vpp_chnid, vo_devid, vo_chnid);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    }

    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);
    if (vo_cfg.vo_intf_type == E_VO_INTERFACE_TYPE_MIPI) {
        ret = sample_common_dsp_init(DSP_ID, (vs_char_t*)"/lib/firmware/vs_dsp0.bin");
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to init dsp\n");
            return ret;
        }
        s_vo_dsp = VS_TRUE;
    }

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        return ret;
    }

    // algo start
    algo_handle.sensor_type = sensor_type;

    algo_handle.algo_params.dde_threshold = dde_threshold;
    ret = sample_infra_algo_proc_start(&algo_handle);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start algo proc\n");
        return ret;
    }

    return VS_SUCCESS;

}
#ifdef RTSP_ENABLED
vs_int32_t sample_venc_chn_init(vs_int32_t venc_chnid, vs_payload_type_e type, vs_venc_profile_e profile,
               vs_size_s frame_size, sample_brc_mode_e brc_mode, vs_venc_gop_attr_s *p_gop_attr)

{
    vs_int32_t ret = VS_SUCCESS;
    sample_venc_cfg_s sample_venc_cfg;

    memset(&sample_venc_cfg, 0, sizeof(sample_venc_cfg_s));
    sample_venc_cfg.format = SAMPLE_VENC_INPUT_FORMAT;
    //sample_venc_cfg.compress = (g_compress_mode == E_COMPRESS_MODE_NONE) ? VS_FALSE : VS_TRUE;
    sample_venc_cfg.compress = VS_FALSE;
    sample_venc_cfg.type = type;
    sample_venc_cfg.profile = profile;
    sample_venc_cfg.frame_size = frame_size;
    sample_venc_cfg.brc_mode = brc_mode;
    sample_venc_cfg.frc.dst_framerate = INFRA_FPS;
    sample_venc_cfg.frc.src_framerate = INFRA_FPS;
	sample_venc_cfg.bandwidth_save_strength = 0;
    if (p_gop_attr != NULL) {
        sample_venc_cfg.gop_attr = *p_gop_attr;
    }
    ret = sample_common_venc_start(venc_chnid, &sample_venc_cfg);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("sample_common_venc_start failed, ret[0x%x]\n", ret);
        return ret;
    }

    return ret;
}
#endif

static vs_int32_t sample_infra_case_venc(sample_infra_sensor_type_e sensor_type, double dde_threshold = 5.0f)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    algo_handle.vb_pool = VS_INVALID_POOLID;

    vs_vpp_grp_attr_s vpp_grp_attr = {0};

    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = IR_VPP_CHN_ID, vpp_extchnid = IR_VPP_EXTCHN_ID, vpp_chnid_nn = IR_VPP_NN_CHN_ID, vpp_chnid_sr = IR_VPP_SR_CHN_ID  ;
    vs_int32_t vo_devid = IR_VO_DEV_ID, vo_chnid = IR_VO_CHN_ID;
    vs_int32_t vii_dst_pipe = VII_DST_PIPE, vii_chnid = IR_VII_CHN_ID;
    vs_uint32_t g_sensor_framerate = 30;
    sample_vii_cfg_s *p_vii_cfg = &vii_cfg;

#ifdef RTSP_ENABLED 
    vs_int32_t venc_devid = 0;
    vs_int32_t venc_chnid[1] = {0};
#ifdef H265_VENC_ENABLED
    vs_payload_type_e encode_type[1] = {E_PT_TYPE_H265};
    vs_venc_profile_e profile[1] = {E_VENC_PROFILE_H265_MAIN};
    sample_brc_mode_e brc_mode = E_VENC_BRC_CBR;
    vs_venc_gop_mode_e gop_mode = E_VENC_GOP_MODE_NORMP;
#else
    vs_payload_type_e encode_type[1] = {E_PT_TYPE_H264};
    vs_venc_profile_e profile[1] = {E_VENC_PROFILE_H264_MAIN};
    sample_brc_mode_e brc_mode = E_VENC_BRC_VBR;
    vs_venc_gop_mode_e gop_mode = E_VENC_GOP_MODE_LOWDELAYB;
#endif
    vs_venc_gop_attr_s gop_attr;
#endif

    //< infra registers set , integration, fos etc
    uint32_t fos, integration;

    fos = GetIniKeyInt("ql64017","fos", g_strConfigIni);
    integration = GetIniKeyInt("ql64017","integration", g_strConfigIni);
    char *ptroocpath = GetIniKeyString("ql64017","oocpath", g_strConfigIni);

    memcpy(oocpath, ptroocpath, 255);
    airvisen_trace("fos:%d, integration:%d, oocpath:%s\n", fos, integration, oocpath);

    infra_set_fos(fos);
    infra_set_integration(integration);

    p_sensor_cfg = sample_infra_sensor_cfg_get(sensor_type);
    format = p_sensor_cfg->pixel_format;
    if (p_sensor_cfg->width > p_sensor_cfg->algo_output_width) {
        img_size.width = p_sensor_cfg->width;
        img_size.height = p_sensor_cfg->height;
    } else {
        img_size.width = p_sensor_cfg->algo_output_width;
        img_size.height = p_sensor_cfg->algo_output_height;
    }
    if (img_size.width < s_vo_size.width) {
        img_size = s_vo_size;
    }

    // sys init
    vb_cfg.pool_cnt = 1;
    for (i = 0; i < vb_cfg.pool_cnt; i++) {
        vb_cfg.ast_commpool[i].blk_size =
            sample_common_buffer_size_get(&img_size, format,
                    E_COMPRESS_MODE_NONE, frame_num);
        vb_cfg.ast_commpool[i].blk_cnt = s_comm_vb_cnt;
        vb_cfg.ast_commpool[i].remap_mode = VB_REMAP_MODE_NONE;
    }

    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        return ret;
    }

    // vii start
    memset(&vii_cfg, 0, sizeof(sample_vii_cfg_s));
    if (npuenable == 1)
    {
        vii_cfg.vii_vpp_mode = E_VII_OFFLINE_VPP_OFFLINE;
    }
    else
    {
        vii_cfg.vii_vpp_mode = E_VII_OFFLINE_VPP_ONLINE;

    }
    vii_cfg.route_num = 1;
    //s_vii_vpp_mode = vii_cfg.vii_vpp_mode;

    ret = sample_infra_init(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to init ooc\n");
        return ret;
    }

    ret = sample_infra_vii_start(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vii\n");
        return ret;
    }

    // vpp start
    ret = sample_common_vii_bind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vii vpp\n");
        return ret;
    }

    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);
   
    vpp_chn_enable[vpp_chnid] = VS_TRUE;

    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
    if (npuenable==1)
    {
        vpp_chn_enable[vpp_chnid_nn] = VS_TRUE;
        sample_infra_vpp_chn_nn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid_nn]);

//        vpp_extchn_enable[vpp_extchnid] = VS_TRUE;
//
//        sample_infra_vpp_extchn_attr_get(vpp_chnid, sensor_type,
//                &vpp_extchn_attr[vpp_extchnid]);
    }
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        return ret;
    }

//    if (npuenable==1)
//    {
//        ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
//                vpp_extchn_enable, vpp_extchn_attr);
//        if (ret != VS_SUCCESS) {
//            airvisen_trace("failed to set vpp extchn attr\n");
//            return ret;
//        }
//    }

#ifdef RTSP_ENABLED
    ret = sample_common_venc_gop_attr_get(gop_mode, &gop_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("gop_attr_get failed, ret[0x%x]\n", ret);
        //goto exit_vii_unbind_vpp;
        return ret;
    }
    //ret = sample_venc_chn_init(venc_chnid[0], encode_type[0], profile[0], s_vpp_size, brc_mode, &gop_attr);
    ret = sample_venc_chn_init(venc_chnid[0], encode_type[0], profile[0], s_vo_size, brc_mode, &gop_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("sample_venc_chn_init failed, ret[0x%x]\n", ret);
        //goto exit_vii_unbind_vpp;
        return ret;
    }

    if (npuenable==0)
    {
        ret = sample_common_vpp_bind_venc(vpp_grpid, vpp_chnid, venc_devid, venc_chnid[0]);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("sample_common_vpp_bind_venc failed, ret[0x%x]\n", ret);
            //goto exit_venc0_stop;
            return ret;
        }
    }
#else
    //airvisen_trace("vii_cfg.vii_vpp_mode:%d\n", vii_cfg.vii_vpp_mode);
    //if (vii_cfg.vii_vpp_mode == E_VII_OFFLINE_VPP_ONLINE)
    if (npuenable==0)
    {
        // vo start
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                vpp_chnid, vo_devid, vo_chnid);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    }

    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);
    if (vo_cfg.vo_intf_type == E_VO_INTERFACE_TYPE_MIPI) {
        ret = sample_common_dsp_init(DSP_ID, (vs_char_t*)"/lib/firmware/vs_dsp0.bin");
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to init dsp\n");
            return ret;
        }
        s_vo_dsp = VS_TRUE;
    }

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        return ret;
    }
#endif

    // algo start
    algo_handle.sensor_type = sensor_type;

    algo_handle.algo_params.dde_threshold = dde_threshold;
    ret = sample_infra_algo_proc_start(&algo_handle);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start algo proc\n");
        return ret;
    }

    return VS_SUCCESS;

}

/****************************** dual case ******************************************/


static vs_void_t sample_vii_get_vpp_grp_attr(vs_size_s *img_size, vs_vpp_grp_attr_s *grp_attr)
{
    grp_attr->max_width = img_size->width;
    grp_attr->max_height = img_size->height;
    grp_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    grp_attr->pixel_format = E_PIXEL_FORMAT_YVU_420SP;
    grp_attr->framerate.dst_framerate = -1;
    grp_attr->framerate.src_framerate = -1;
}

static vs_void_t sample_vii_get_vpp_chn_attr(vs_size_s *img_size, vs_vpp_chn_attr_s *chn_attr)
{
    chn_attr->chn_mode = E_VPP_CHN_MODE_USER;
    chn_attr->width = img_size->width;
    chn_attr->height = img_size->height;
    chn_attr->video_format = E_VIDEO_FORMAT_LINEAR;
    chn_attr->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    chn_attr->dynamic_range = E_DYNAMIC_RANGE_SDR8;
#ifdef VS_ORION
    chn_attr->compress_mode = E_COMPRESS_MODE_NONE;
#else
    chn_attr->compress_mode = E_COMPRESS_MODE_RASTER;
#endif
    chn_attr->framerate.src_framerate = -1;
    chn_attr->framerate.dst_framerate = -1;
#ifdef DUAL_LENSES
    chn_attr->mirror_enable = VS_FALSE;
    chn_attr->depth = 6;
#else
    chn_attr->mirror_enable = VS_FALSE;
    chn_attr->depth = 0;
#endif
    
    chn_attr->flip_enable = VS_FALSE;
    
    chn_attr->aspect_ratio.mode = E_ASPECT_RATIO_MODE_NONE;
}

static vs_void_t sample_vio_get_vo_cfg(vs_size_s *img_size, sample_vo_cfg_s *vo_cfg)
{
    vo_cfg->vo_devid = 0;
    vo_cfg->vo_layerid = 0;

    vo_cfg->vo_intf_type = E_VO_INTERFACE_TYPE_HDMI;
    vo_cfg->vo_output = E_VO_OUTPUT_TYPE_1080P60;
    vo_cfg->bg_color = 0;
    vo_cfg->dynamic_range = E_DYNAMIC_RANGE_SDR8;
    vo_cfg->pixel_format = E_PIXEL_FORMAT_YUV_420SP;
    vo_cfg->vo_mode = E_VO_MODE_1MUX;
    vo_cfg->img_width = 1920;
    vo_cfg->img_height = 1080;
    vo_cfg->enable = VS_TRUE;
    vo_cfg->zorder = 0;

#ifdef VS_ORION
    vo_cfg->vo_intf_type = E_VO_INTERFACE_TYPE_MIPI;
    vo_cfg->vo_output = E_VO_OUTPUT_TYPE_USER;
#endif
}


 vs_int32_t sample_dual_case(sample_infra_sensor_type_e sensor_type, double dde_threshold = 5.0f)
{

    /******************************  红外参数配置 ******************************************/
    vs_int32_t ret = VS_SUCCESS, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_infra_sensor_cfg_s *p_sensor_cfg;
    
    algo_handle.vb_pool = VS_INVALID_POOLID;

    vs_int32_t vii_dst_pipe = VII_DST_PIPE, vii_chnid = IR_VII_CHN_ID, vis_vpp_extchnid = 6;

    vs_int32_t vpp_grpid = VII_VPP_GRP_ID, vpp_chnid = IR_VPP_CHN_ID, vpp_extchnid = 5, vpp_chnid_nn = 1, vpp_chnid_sr = 2;
    vs_vpp_grp_attr_s vpp_grp_attr = {0};
    vs_bool_t vpp_chn_enable[VPP_MAX_PHYCHN_NUM] = {VS_FALSE};
    vs_vpp_chn_attr_s vpp_chn_attr[VPP_MAX_PHYCHN_NUM] ;
    vs_bool_t vpp_extchn_enable[VPP_MAX_EXTCHN_NUM] = {VS_FALSE};
    vs_vpp_extchn_attr_s vpp_extchn_attr[VPP_MAX_EXTCHN_NUM] = {0};

    vs_int32_t vo_devid = 0, vo_chnid = 0;
    sample_vo_cfg_s vo_cfg = {VS_FALSE};
    

    /******************************  可见光参数配置 ******************************************/
#ifdef DUAL_LENSES
    vs_int32_t vis_sensor_id = 0;
    vs_pixel_format_e vis_pixel_format;
    vs_uint32_t vis_frame_num;
    vs_size_s vis_img_size = {0};
    vs_vb_cfg_s vis_vb_cfg = {0};
    vs_int32_t vis_vii_pipeid[1] = {VIS_VII_SRC_PIPE};
    vs_int32_t vis_vii_chnid = VIS_VII_CHN_ID;

    vs_int32_t vis_vpp_grpid[1] = {VIS_VPP_GRP_ID};
    vs_int32_t vis_vpp_chnid = VIS_VPP_CHN_ID;
    vs_int32_t vis_vo_devid = 0;
    vs_int32_t vis_vo_chnid[1] = {2};
    vs_bool_t vis_chn_enable[VPP_MAX_PHYCHN_NUM] = {VS_FALSE, VS_TRUE, VS_TRUE, VS_FALSE};
    vs_vpp_grp_attr_s vis_vpp_grp_attr = {0};
    vs_vpp_chn_attr_s vis_vpp_chn_attr[VPP_MAX_PHYCHN_NUM];
    sample_vo_cfg_s vis_vo_cfg;// = {0};
    vs_char_t vis_name[100] = "/lib/firmware/vs_dsp0.bin";
#endif

    //< infra registers set , integration, fos etc
    uint32_t fos, integration;
    fos = GetIniKeyInt("ql64017","fos", g_strConfigIni);
    integration = GetIniKeyInt("ql64017","integration", g_strConfigIni);
    airvisen_trace("fos:%d, integration:%d\n", fos, integration);

    infra_set_fos(fos);
    infra_set_integration(integration);
    p_sensor_cfg = sample_infra_sensor_cfg_get(sensor_type);
    format = p_sensor_cfg->pixel_format;
    if (p_sensor_cfg->width > p_sensor_cfg->algo_output_width) {
        img_size.width = p_sensor_cfg->width;
        img_size.height = p_sensor_cfg->height;
    } else {
        img_size.width = p_sensor_cfg->algo_output_width;
        img_size.height = p_sensor_cfg->algo_output_height;
    }
    if (img_size.width < s_vo_size.width) {
        img_size = s_vo_size;
    }

#ifdef DUAL_LENSES
    // vis_sensor_id = 0 
    sample_common_vii_sensor_img_size_get(vis_sensor_id, &vis_img_size);
    if (vis_img_size.width >= 3840 || vis_img_size.height >= 2160) {
        airvisen_trace("Notice: the case don't support this sensor type!\n");
        return VS_FAILED;
    }

    airvisen_trace("vis image size: %d, %d\n", vis_img_size.width, vis_img_size.height);
     frame_num = sample_common_vii_wdr_frame_num_get(vis_sensor_id);
     if (frame_num >= 3) {
         airvisen_trace("Notice: the case don't support this sensor type!\n");
         return VS_FAILED;
     }

    sample_common_vii_sensor_pixel_format_get(vis_sensor_id, &vis_pixel_format);
#else //<@Aaron, here the vb pool will always created for vis sensor for better debug reason
    vs_vb_cfg_s vis_vb_cfg = {0};
    vs_pixel_format_e vis_pixel_format;
    vs_size_s vis_img_size = {0};
    vis_img_size.width = s_vpp_sr_size.width;
    vis_img_size.height = 1440;
    vis_pixel_format = E_PIXEL_FORMAT_BAYER_10BPP;
#endif
    vis_vb_cfg.pool_cnt = 2;
    vis_vb_cfg.ast_commpool[IR_VII_ROUTE_CFG_ID].blk_size =
        sample_common_buffer_size_get(&img_size, format,
                E_COMPRESS_MODE_NONE, frame_num);
    vis_vb_cfg.ast_commpool[IR_VII_ROUTE_CFG_ID].blk_cnt = s_comm_vb_cnt;
    vis_vb_cfg.ast_commpool[IR_VII_ROUTE_CFG_ID].remap_mode = VB_REMAP_MODE_NONE;

    vis_vb_cfg.ast_commpool[VIS_VII_ROUTE_CFG_ID].blk_size = sample_common_buffer_size_get(&vis_img_size, vis_pixel_format, E_COMPRESS_MODE_NONE, frame_num);
    airvisen_trace("blk_size:%d\n", vis_vb_cfg.ast_commpool[VIS_VII_ROUTE_CFG_ID].blk_size);
    vis_vb_cfg.ast_commpool[VIS_VII_ROUTE_CFG_ID].blk_cnt = 13;
    vis_vb_cfg.ast_commpool[VIS_VII_ROUTE_CFG_ID].remap_mode = VB_REMAP_MODE_NONE;

    /******************************  程序启动 ******************************************/
    // sys init
    ret = sample_common_sys_init(&vis_vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        return ret;
    }

    vii_cfg.vii_vpp_mode = E_VII_OFFLINE_VPP_ONLINE;

#ifdef DUAL_LENSES
    vii_cfg.route_num = 2;
#else
    vii_cfg.route_num = 1;
#endif

#if 1
    ret = sample_infra_init(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to init ooc\n");
        return ret;
    }
#else
    ///infra_vii_config_get(sensor_type, &vii_cfg);
    sample_infra_vii_config_get(sensor_type, &vii_cfg);
    
    ret = sample_infra_ooc_init(sensor_type, &ooc_handle);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to init ooc\n");
        return ret;
    }
    vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_addr = ooc_handle.phys_addr;
    vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_size = ooc_handle.buf_size;
#endif

#ifdef DUAL_LENSES
    sample_common_vii_default_cfg_get(vis_sensor_id, &vii_cfg.route_cfg[VIS_VII_ROUTE_CFG_ID]);
    vii_cfg.route_cfg[VIS_VII_ROUTE_CFG_ID].pipe_num = 1;
    vii_cfg.route_cfg[VIS_VII_ROUTE_CFG_ID].pipe_id[0] = vis_vii_pipeid[0];
    vii_cfg.route_cfg[VIS_VII_ROUTE_CFG_ID].pipe_cfg[0].pipe_param.mode = E_VII_PIPE_ADVANCE_MODE_NONE;
    vii_cfg.route_cfg[VIS_VII_ROUTE_CFG_ID].pipe_cfg[0].pipe_id = vis_vii_pipeid[0];
    vii_cfg.route_cfg[VIS_VII_ROUTE_CFG_ID].pipe_cfg[0].pipe_attr.compress_mode = E_COMPRESS_MODE_NONE;
    vii_cfg.route_cfg[VIS_VII_ROUTE_CFG_ID].pipe_cfg[0].phys_chn_cfg[VIS_VII_ROUTE_CFG_ID].chn_attr.compress_mode = E_COMPRESS_MODE_NONE;
#endif    

    ret = sample_infra_vii_start(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vii\n");
        return ret;
    }
    
    // vpp start
    ret = sample_common_vii_bind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vii vpp\n");
        return ret;
    }
  
#ifdef DUAL_LENSES
    ret = sample_common_vii_bind_vpp(vis_vii_pipeid[0], vis_vii_chnid, vis_vpp_grpid[0]);
    if (ret != VS_SUCCESS) {
         return ret;
    }
#endif
  
    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);

    vpp_chn_enable[vpp_chnid] = VS_TRUE;
    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
#ifndef DUAL_LENSES 
    vpp_chn_enable[vpp_chnid_nn] = VS_TRUE;
    sample_infra_vpp_chn_nn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid_nn]);
#endif

    vpp_chn_enable[vpp_chnid_sr] = VS_TRUE;
    sample_infra_vpp_chn_sr_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid_sr]);

    vpp_extchn_enable[vpp_extchnid] = VS_TRUE;
    sample_infra_vpp_extchn_attr_get(vpp_chnid, sensor_type,&vpp_extchn_attr[vpp_extchnid]);

  
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable, &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        return ret;
    }
  
    ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
            vpp_extchn_enable, vpp_extchn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to set vpp extchn attr\n");
        return ret;
    }

#ifdef DUAL_LENSES 
    sample_vii_get_vpp_grp_attr(&vis_img_size, &vis_vpp_grp_attr);
    sample_vii_get_vpp_chn_attr(&vis_img_size, &vis_vpp_chn_attr[vis_vpp_chnid]);

   
    sample_infra_vpp_chn_nn_attr_get(0, &vis_vpp_chn_attr[vpp_chnid_nn]);


    sample_infra_vpp_extchn_attr_get(vis_vpp_chnid, 0, &vpp_extchn_attr[6]);

    ret = sample_common_vpp_start(vis_vpp_grpid[0], vis_chn_enable, &vis_vpp_grp_attr, vis_vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        return ret;
    }

    vpp_extchn_attr[6].width = 1920;
    vpp_extchn_attr[6].height = 1080;

    ret = vs_mal_vpp_extchn_attr_set(vis_vpp_grpid[0], 10, &vpp_extchn_attr[6]);
	if (ret != VS_SUCCESS) {
		airvisen_trace("vs_mal_vpp_extchn_attr_set(grp %d chn %d) failed with 0x%x\n", vis_vpp_grpid[0], 10, ret);
		return ret;
	}

	ret = vs_mal_vpp_chn_enable(vis_vpp_grpid[0], 10);
	if (ret != VS_SUCCESS) {
		airvisen_trace("vs_mal_vpp_chn_enable(grp %d chn %d) failed with 0x%x\n", vis_vpp_grpid[0], 10, ret);
		return ret;
	}
    // ret = sample_common_vpp_bind_vo(vis_vpp_grpid[0], vis_vpp_chnid, vis_vo_devid, vis_vo_chnid[0]);
    // if (ret != VS_SUCCESS) {
    //     airvisen_trace(" failed to bind vpp vo\n");
    //     return ret;
    // }
#else  

    // vo start
    ret = sample_common_vpp_bind_vo(vpp_grpid,
            vpp_extchnid + VPP_MAX_PHYCHN_NUM, vo_devid, vo_chnid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vpp vo\n");
        return ret;
    }
#endif
   
    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);

#ifdef DUAL_LENSES 
    vo_cfg.vo_mode = E_VO_MODE_1MUX;
#else
    vo_cfg.vo_mode = E_VO_MODE_1MUX;
#endif

#ifdef FORCE_VO_ONE_CHN_MODE
    vo_cfg.vo_mode = E_VO_MODE_1MUX;
#endif

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        return ret;
    }

    // algo start
    algo_handle.sensor_type = sensor_type;

    algo_handle.algo_params.dde_threshold = dde_threshold;
    ret = sample_infra_algo_proc_start(&algo_handle);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start algo proc\n");
        return ret;
    }

    return VS_SUCCESS;
}

#ifdef VS_CARINA
static vs_int32_t sample_infra_reconfig_case(sample_infra_sensor_type_e sensor_type)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_vii_cfg_s vii_cfg ;
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    sample_infra_algo_handle_s algo_handle ;
    algo_handle.vb_pool = VS_INVALID_POOLID;
    sample_infra_ooc_handle_s ooc_handle = {0};

    vs_int32_t vii_dst_pipe = VII_DST_PIPE, vii_chnid = 0;

    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = 0, vpp_extchnid = 5;
    vs_vpp_grp_attr_s vpp_grp_attr = {0};
    vs_bool_t vpp_chn_enable[VPP_MAX_PHYCHN_NUM] = {VS_FALSE};
    vs_vpp_chn_attr_s vpp_chn_attr[VPP_MAX_PHYCHN_NUM] ;
    vs_bool_t vpp_extchn_enable[VPP_MAX_EXTCHN_NUM] = {VS_FALSE};
    vs_vpp_extchn_attr_s vpp_extchn_attr[VPP_MAX_EXTCHN_NUM] = {0};

    vs_int32_t vo_devid = 0, vo_chnid = 0;
    sample_vo_cfg_s vo_cfg = {VS_FALSE};

    vs_int32_t reconfig_times = 2;


    p_sensor_cfg = sample_infra_sensor_cfg_get(sensor_type);
    format = p_sensor_cfg->pixel_format;
    if (p_sensor_cfg->width > p_sensor_cfg->algo_output_width) {
        img_size.width = p_sensor_cfg->width;
        img_size.height = p_sensor_cfg->height;
    } else {
        img_size.width = p_sensor_cfg->algo_output_width;
        img_size.height = p_sensor_cfg->algo_output_height;
    }
    if (img_size.width < s_vo_size.width) {
        img_size = s_vo_size;
    }

    // sys init
    vb_cfg.pool_cnt = 1;
    for (i = 0; i < vb_cfg.pool_cnt; i++) {
        vb_cfg.ast_commpool[i].blk_size =
            sample_common_buffer_size_get(&img_size, format,
                    E_COMPRESS_MODE_NONE, frame_num);
        vb_cfg.ast_commpool[i].blk_cnt = s_comm_vb_cnt;
        vb_cfg.ast_commpool[i].remap_mode = VB_REMAP_MODE_NONE;
    }

    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        goto exit0;
    }

    // vii start
    sample_infra_vii_config_get(sensor_type, &vii_cfg);
    ret = sample_infra_ooc_init(sensor_type, &ooc_handle);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to init ooc\n");
        goto exit1;
    }
    vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_addr = ooc_handle.phys_addr;
    vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_size = ooc_handle.buf_size;

    ret = sample_infra_vii_start(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vii\n");
        goto exit2;
    }

    // vpp start
    ret = sample_common_vii_bind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vii vpp\n");
        goto exit3;
    }

    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);
    vpp_chn_enable[vpp_chnid] = VS_TRUE;
    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
    vpp_extchn_enable[vpp_extchnid] = VS_TRUE;
    sample_infra_vpp_extchn_attr_get(vpp_chnid, sensor_type,
            &vpp_extchn_attr[vpp_extchnid]);
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        goto exit4;
    }
    ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
            vpp_extchn_enable, vpp_extchn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to set vpp extchn attr\n");
        goto exit5;
    }

    // vo start
    ret = sample_common_vpp_bind_vo(vpp_grpid,
            vpp_extchnid + VPP_MAX_PHYCHN_NUM, vo_devid, vo_chnid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vpp vo\n");
        goto exit6;
    }

    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);
    if (vo_cfg.vo_intf_type == E_VO_INTERFACE_TYPE_MIPI) {
        ret = sample_common_dsp_init(DSP_ID, (vs_char_t*)"/lib/firmware/vs_dsp0.bin");
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to init dsp\n");
            goto exit7;
        }
        s_vo_dsp = VS_TRUE;
    }

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        goto exit8;
    }

    // algo start
    algo_handle.sensor_type = sensor_type;
    ret = sample_infra_algo_proc_start(&algo_handle);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start algo proc\n");
        goto exit9;
    }

reconfig:
    airvisen_trace("\nPress enter to reconfig...\n");
    while (1) {
        if (getchar() == '\n') {
            break;
        }
        usleep(1000);
    };

    // change config
    switch (reconfig_times) {
    case 2:
        airvisen_trace("change reg[%d] from 0x%x to 0x%x\n",
                19,
                vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.reg_array[19],
                0x326E464C);
        vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.reg_array[19] = 0x326E464C;
        break;
    case 1:
        airvisen_trace("change reg[%d] from 0x%x to 0x%x\n",
                19,
                vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.reg_array[19],
                0x326E564C);
        vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.reg_array[19] = 0x326E564C;
        break;
    default:
        goto exit10;
    }

    // stop
    sample_common_vpp_unbind_vo(vpp_grpid,
            vpp_extchnid + VPP_MAX_PHYCHN_NUM, vo_devid, vo_chnid);
    vs_mal_vpp_chn_disable(vpp_grpid, vpp_extchnid + VPP_MAX_PHYCHN_NUM);
    sample_common_vpp_stop(vpp_grpid, vpp_chn_enable);
    sample_common_vii_unbind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
    sample_infra_vii_stop(&vii_cfg);

    vs_mal_mipi_rx_sensor_reset(VII_INFRA_DEV);
    vs_mal_mipi_rx_sensor_unreset(VII_INFRA_DEV);

    // restart
    ret = sample_infra_vii_start(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vii\n");
        goto exit10;
    }
    ret = sample_common_vii_bind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vii vpp\n");
        goto exit10;
    }
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        goto exit10;
    }
    ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
            vpp_extchn_enable, vpp_extchn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to set vpp extchn attr\n");
        goto exit10;
    }
    ret = sample_common_vpp_bind_vo(vpp_grpid,
            vpp_extchnid + VPP_MAX_PHYCHN_NUM, vo_devid, vo_chnid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vpp vo\n");
        goto exit10;
    }


    reconfig_times--;
    if (reconfig_times > 0) {
        goto reconfig;
    } else {
        sample_common_pause();
    }

exit10:
    sample_infra_algo_proc_stop(&algo_handle);
exit9:
    sample_common_vo_stop(&vo_cfg);
exit8:
    if (vo_cfg.vo_intf_type == E_VO_INTERFACE_TYPE_MIPI) {
        sample_common_dsp_exit(DSP_ID);
    }
exit7:
    sample_common_vpp_unbind_vo(vpp_grpid,
            vpp_extchnid + VPP_MAX_PHYCHN_NUM, vo_devid, vo_chnid);
exit6:
    vs_mal_vpp_chn_disable(vpp_grpid, vpp_extchnid + VPP_MAX_PHYCHN_NUM);
exit5:
    sample_common_vpp_stop(vpp_grpid, vpp_chn_enable);
exit4:
    sample_common_vii_unbind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
exit3:
    sample_infra_vii_stop(&vii_cfg);
    sample_infra_algo_pool_destroy(&algo_handle);
exit2:
    sample_infra_ooc_exit(&ooc_handle);
exit1:
    sample_common_sys_exit();
exit0:
    return ret;
}

#else // ifdef VS_CARINA


#define SAMPLE_INFRA_SMART_DATA_NUM     408
static vs_uint8_t s_smart_data[SAMPLE_INFRA_SMART_DATA_NUM];

static vs_int32_t sample_infra_reg_init(const vs_vii_dev_attr_s *p_dev_attr)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_int32_t i, j, index;
    const vs_uint32_t *reg_arr = p_dev_attr->infrared_attr.reg_array;

    index = SAMPLE_INFRA_SMART_DATA_NUM - 1;

    for (i = 0; i < 13 ; i++) {
        j = (i <= 11) ? 31 : 23;
        for (; j >= 0; j--) {
            s_smart_data[index] = (reg_arr[13 + i] >> j) & 1;
            index--;
        }
    }

    
    airvisen_trace("\nsmart data init:\n");
    for (i = SAMPLE_INFRA_SMART_DATA_NUM-1; i >= 0; i--) {
        if (((i - 15) % 28) == 0) {
            airvisen_trace("[%3d - %3d]: ", i, (i - 27 > 0) ? i - 27 : 0);
        }
        airvisen_trace("%d", s_smart_data[i]);
        if ((SAMPLE_INFRA_SMART_DATA_NUM - i) % 7 == 0) {
            airvisen_trace(" ");
        }
        if (((i - 15) % 28) == 1) {
            airvisen_trace("\n");
        }
    }
    airvisen_trace("\n");
    

    return ret;
}

static vs_void_t sample_infra_reg_enable(const vs_vii_dev_attr_s *p_dev_attr)
{
    char str[50] = {0};
    vs_uint32_t reg_val = p_dev_attr->infrared_attr.reg_array[12] | 0x1;

    snprintf(str, 50, "devmem 0x0d2a2848 32 %#x", reg_val);
    system(str);
    system("devmem 0x0d2a2878 32 1");
}

static vs_void_t sample_infra_reg_disable(const vs_vii_dev_attr_s *p_dev_attr)
{
    char str[50] = {0};
    vs_uint32_t reg_val = p_dev_attr->infrared_attr.reg_array[12];

    snprintf(str, 50, "devmem 0x0d2a2848 32 %#x", reg_val);
    system(str);
    system("devmem 0x0d2a2878 32 0");
}

static vs_void_t sample_infra_reg_set(vs_uint32_t bit, vs_uint8_t val)
{
    char str[50] = {0};
    vs_int32_t i, reg_idx;
    vs_uint32_t tmp_bit, reg_val;
    vs_uint32_t reg_base_addr = 0x0d2a2800;

    airvisen_trace("change smart data %d bit from %d to %d\n",
            bit, s_smart_data[bit], val);

    s_smart_data[bit] = val;

    reg_val = 0;

    if (bit <= 15) {
        reg_idx = 14;

        tmp_bit = 15;
        for (i = 6; i >= 0; i--) {
            reg_val |= ((s_smart_data[tmp_bit] & 1) << i);
            tmp_bit--;
        }

        tmp_bit = 8;
        for (i = 14; i >= 8; i--) {
            reg_val |= ((s_smart_data[tmp_bit] & 1) << i);
            tmp_bit--;
        }

        tmp_bit = 1;
        for (i = 22; i >= 21; i--) {
            reg_val |= ((s_smart_data[tmp_bit] & 1) << i);
            tmp_bit--;
        }
    } else {
        reg_idx = 14 - (bit - 15 + 28 - 1) / 28;

        tmp_bit = 43 + (13 - reg_idx) * 28;
        for (i = 6; i >= 0; i--) {
            reg_val |= ((s_smart_data[tmp_bit] & 1) << i);
            tmp_bit--;
        }

        tmp_bit = 36 + (13 - reg_idx) * 28;
        for (i = 14; i >= 8; i--) {
            reg_val |= ((s_smart_data[tmp_bit] & 1) << i);
            tmp_bit--;
        }

        tmp_bit = 29 + (13 - reg_idx) * 28;
        for (i = 22; i >= 16; i--) {
            reg_val |= ((s_smart_data[tmp_bit] & 1) << i);
            tmp_bit--;
        }

        tmp_bit = 22 + (13 - reg_idx) * 28;
        for (i = 30; i >= 24; i--) {
            reg_val |= ((s_smart_data[tmp_bit] & 1) << i);
            tmp_bit--;
        }
    }

    snprintf(str, 50, "devmem %#x 32 %#x", reg_base_addr + reg_idx * 4, reg_val);
    //printf("reg_idx %d, %s\n", reg_idx, str);
    system(str);
}

static vs_int32_t sample_infra_reconfig_case(sample_infra_sensor_type_e sensor_type)
{
    vs_int32_t ret = VS_FAILED, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_vii_cfg_s vii_cfg ;
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    sample_infra_algo_handle_s algo_handle ;
    algo_handle.vb_pool = VS_INVALID_POOLID;
    sample_infra_ooc_handle_s ooc_handle = {0};

    vs_int32_t vii_dst_pipe = VII_DST_PIPE, vii_chnid = 0;

    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = 0, vpp_extchnid = 5;
    vs_vpp_grp_attr_s vpp_grp_attr = {0};
    vs_bool_t vpp_chn_enable[VPP_MAX_PHYCHN_NUM] = {VS_FALSE};
    vs_vpp_chn_attr_s vpp_chn_attr[VPP_MAX_PHYCHN_NUM] ;
    vs_bool_t vpp_extchn_enable[VPP_MAX_EXTCHN_NUM] = {VS_FALSE};
    vs_vpp_extchn_attr_s vpp_extchn_attr[VPP_MAX_EXTCHN_NUM] = {0};

    vs_int32_t vo_devid = 0, vo_chnid = 0;
    sample_vo_cfg_s vo_cfg = {VS_FALSE};

    vs_int32_t reconfig_times = 2;


    p_sensor_cfg = sample_infra_sensor_cfg_get(sensor_type);
    format = p_sensor_cfg->pixel_format;
    if (p_sensor_cfg->width > p_sensor_cfg->algo_output_width) {
        img_size.width = p_sensor_cfg->width;
        img_size.height = p_sensor_cfg->height;
    } else {
        img_size.width = p_sensor_cfg->algo_output_width;
        img_size.height = p_sensor_cfg->algo_output_height;
    }
    if (img_size.width < s_vo_size.width) {
        img_size = s_vo_size;
    }

    // sys init
    vb_cfg.pool_cnt = 1;
    for (i = 0; i < vb_cfg.pool_cnt; i++) {
        vb_cfg.ast_commpool[i].blk_size =
            sample_common_buffer_size_get(&img_size, format,
                    E_COMPRESS_MODE_NONE, frame_num);
        vb_cfg.ast_commpool[i].blk_cnt = s_comm_vb_cnt;
        vb_cfg.ast_commpool[i].remap_mode = VB_REMAP_MODE_NONE;
    }

    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        goto exit0;
    }

    // vii start
    sample_infra_vii_config_get(sensor_type, &vii_cfg);
    ret = sample_infra_ooc_init(sensor_type, &ooc_handle);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to init ooc\n");
        goto exit1;
    }
    vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_addr = ooc_handle.phys_addr;
    vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_size = ooc_handle.buf_size;
    airvisen_trace("phys_addr:%p, size:%d\n", vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_addr, vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr.infrared_attr.phys_size);

    ret = sample_infra_vii_start(&vii_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vii\n");
        goto exit2;
    }

    // vpp start
    ret = sample_common_vii_bind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vii vpp\n");
        goto exit3;
    }

    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);
    vpp_chn_enable[vpp_chnid] = VS_TRUE;
    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
    vpp_extchn_enable[vpp_extchnid] = VS_TRUE;
    sample_infra_vpp_extchn_attr_get(vpp_chnid, sensor_type,
            &vpp_extchn_attr[vpp_extchnid]);
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        goto exit4;
    }
    ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
            vpp_extchn_enable, vpp_extchn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to set vpp extchn attr\n");
        goto exit5;
    }

    // vo start
    ret = sample_common_vpp_bind_vo(vpp_grpid,
            vpp_extchnid + VPP_MAX_PHYCHN_NUM, vo_devid, vo_chnid);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to bind vpp vo\n");
        goto exit6;
    }

    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);
    if (vo_cfg.vo_intf_type == E_VO_INTERFACE_TYPE_MIPI) {
        ret = sample_common_dsp_init(DSP_ID, (vs_char_t*)"/lib/firmware/vs_dsp0.bin");
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to init dsp\n");
            goto exit7;
        }
        s_vo_dsp = VS_TRUE;
    }

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        goto exit8;
    }

    // algo start
    algo_handle.sensor_type = sensor_type;
    ret = sample_infra_algo_proc_start(&algo_handle);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start algo proc\n");
        goto exit9;
    }

    // change config
    sample_infra_reg_init(&vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr);

reconfig:
    airvisen_trace("\nPress enter to reconfig...\n");
    while (1) {
        if (getchar() == '\n') {
            break;
        }
        usleep(1000);
    };

    switch (reconfig_times) {
    case 2:
        sample_infra_reg_disable(&vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr);
        sample_infra_reg_set(198, 1);
        sample_infra_reg_set(197, 0);
        sample_infra_reg_set(196, 0);
        sample_infra_reg_enable(&vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr);
        s_recfg_drop_cnt = 5;
        break;
    case 1:
        sample_infra_reg_disable(&vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr);
        sample_infra_reg_set(198, 1);
        sample_infra_reg_set(197, 0);
        sample_infra_reg_set(196, 1);
        sample_infra_reg_enable(&vii_cfg.route_cfg[IR_VII_ROUTE_CFG_ID].dev_attr);
        s_recfg_drop_cnt = 5;
        break;
    default:
        goto exit10;
    }

    reconfig_times--;
    if (reconfig_times > 0) {
        goto reconfig;
    } else {
        sample_common_pause();
    }

exit10:
    sample_infra_algo_proc_stop(&algo_handle);
exit9:
    sample_common_vo_stop(&vo_cfg);
exit8:
    if (vo_cfg.vo_intf_type == E_VO_INTERFACE_TYPE_MIPI) {
        sample_common_dsp_exit(DSP_ID);
    }
exit7:
    sample_common_vpp_unbind_vo(vpp_grpid,
            vpp_extchnid + VPP_MAX_PHYCHN_NUM, vo_devid, vo_chnid);
exit6:
    vs_mal_vpp_chn_disable(vpp_grpid, vpp_extchnid + VPP_MAX_PHYCHN_NUM);
exit5:
    sample_common_vpp_stop(vpp_grpid, vpp_chn_enable);
exit4:
    sample_common_vii_unbind_vpp(vii_dst_pipe, vii_chnid, vpp_grpid);
exit3:
    sample_infra_vii_stop(&vii_cfg);
    sample_infra_algo_pool_destroy(&algo_handle);
exit2:
    sample_infra_ooc_exit(&ooc_handle);
exit1:
    sample_common_sys_exit();
exit0:
    return ret;
}

#endif // ifdef VS_CARINA



static vs_void_t sample_infra_register_signal_handler(void (*sig_handler)(int))
{
    struct sigaction sa;
    memset(&sa, 0, sizeof(struct sigaction));
    sa.sa_handler = sig_handler;
    sa.sa_flags = 0;

    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
}

static vs_void_t sample_infra_signal_handle(vs_int32_t s_no)
{
    (vs_void_t)s_no;
    s_stop_flag = 1;
}
static vs_void_t sample_vio_register_signal_handler(void (*sig_handler)(int))
{
    struct sigaction sa;
    memset(&sa, 0, sizeof(struct sigaction));
    sa.sa_handler = sig_handler;
    sa.sa_flags = 0;

    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
}

vs_void_t vii_signal_handle(vs_int32_t s_no)
{
    if (s_no == SIGINT || s_no == SIGTERM) {
        g_stop_flag = 1;
    }
}
#ifdef VII_FROM_UVC
int uvc_host_infra_case(vs_size_s s_uvc_frame_size)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    algo_handle.vb_pool = VS_INVALID_POOLID;

    vs_vpp_grp_attr_s vpp_grp_attr = {0};
#ifndef VII_FROM_UVC_RGB
    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = IR_VPP_CHN_ID, vpp_extchnid = IR_VPP_EXTCHN_ID, vpp_chnid_nn = IR_VPP_NN_CHN_ID;
#else
    vs_int32_t vpp_grpid = VIS_VPP_GRP_ID, vpp_chnid = VIS_VPP_CHN_ID, vpp_extchnid = VIS_VPP_PIP_CHN_ID, vpp_chnid_nn = VIS_VPP_NN_CHN_ID;
#endif
    vs_int32_t vo_devid = IR_VO_DEV_ID, vo_chnid = IR_VO_CHN_ID;
    int sensor_type = 0;

    img_size = s_vo_size;
    format =  E_PIXEL_FORMAT_YUV_420SP,

    // sys init
    vb_cfg.pool_cnt = 1;
    for (i = 0; i < vb_cfg.pool_cnt; i++) {
        vb_cfg.ast_commpool[i].blk_size =
            sample_common_buffer_size_get(&img_size, format,
                    E_COMPRESS_MODE_NONE, frame_num);
        vb_cfg.ast_commpool[i].blk_cnt = s_comm_vb_cnt;
        vb_cfg.ast_commpool[i].remap_mode = VB_REMAP_MODE_NONE;
    }

    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        return ret;
    }

    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);
   
    vpp_chn_enable[vpp_chnid] = VS_TRUE;

    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
#ifdef VII_FROM_UVC_640_SR_COMP
    vpp_chn_enable[IR_VPP_UVC_RAW_ID] = VS_TRUE;
    memcpy(&vpp_chn_attr[IR_VPP_UVC_RAW_ID], &vpp_chn_attr[vpp_chnid], sizeof(vs_vpp_chn_attr_s));
    vpp_chn_attr[IR_VPP_UVC_RAW_ID].depth = 0;
#endif
    if (npuenable==1)
    {
        vpp_chn_enable[vpp_chnid_nn] = VS_TRUE;
        sample_infra_vpp_chn_nn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid_nn]);
//        vpp_extchn_enable[vpp_extchnid] = VS_TRUE;
//
//        sample_infra_vpp_extchn_attr_get(vpp_chnid, sensor_type,
//                &vpp_extchn_attr[vpp_extchnid]);
    }
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        return ret;
    }

//    if (npuenable==1)
//    {
//        ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
//                vpp_extchn_enable, vpp_extchn_attr);
//        if (ret != VS_SUCCESS) {
//            airvisen_trace("failed to set vpp extchn attr\n");
//            return ret;
//        }
//    }
    if (npuenable==0)
    {
    // vo start
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                vpp_chnid, vo_devid, vo_chnid);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    }

    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);
#ifdef VII_FROM_UVC_640_SR_COMP
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                IR_VPP_UVC_RAW_ID, vo_devid, IR_VO_UVC_RAW_ID);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    vo_cfg.vo_mode = E_VO_MODE_4MUX;
#endif

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        return ret;
    }
    g_uvc_host_retcode = uvc_host_infra_case_start(s_uvc_frame_size, &camera_info);
    if (g_uvc_host_retcode != VS_SUCCESS)
    {
        return -1;
    }
    
    return VS_SUCCESS;
} 
             
int uvc_host_infra_case_venc(vs_size_s s_uvc_frame_size)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    algo_handle.vb_pool = VS_INVALID_POOLID;

    vs_vpp_grp_attr_s vpp_grp_attr = {0};

    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = IR_VPP_CHN_ID, vpp_extchnid = IR_VPP_EXTCHN_ID, vpp_chnid_nn = IR_VPP_NN_CHN_ID;
    vs_int32_t vo_devid = IR_VO_DEV_ID, vo_chnid = IR_VO_CHN_ID;
    int sensor_type = 0;

    img_size = s_vo_size;
    format =  E_PIXEL_FORMAT_YUV_420SP;
#ifdef RTSP_ENABLED 
    vs_int32_t venc_devid = 0;
    vs_int32_t venc_chnid[1] = {0};
#ifdef H265_VENC_ENABLED
    vs_payload_type_e encode_type[1] = {E_PT_TYPE_H265};
    vs_venc_profile_e profile[1] = {E_VENC_PROFILE_H265_MAIN};
    sample_brc_mode_e brc_mode = E_VENC_BRC_CBR;
    vs_venc_gop_mode_e gop_mode = E_VENC_GOP_MODE_NORMP;
#else
    vs_payload_type_e encode_type[1] = {E_PT_TYPE_H264};
    vs_venc_profile_e profile[1] = {E_VENC_PROFILE_H264_MAIN};
    sample_brc_mode_e brc_mode = E_VENC_BRC_VBR;
    vs_venc_gop_mode_e gop_mode = E_VENC_GOP_MODE_LOWDELAYB;
#endif
    vs_venc_gop_attr_s gop_attr;
#endif

    // sys init
    vb_cfg.pool_cnt = 1;
    for (i = 0; i < vb_cfg.pool_cnt; i++) {
        vb_cfg.ast_commpool[i].blk_size =
            sample_common_buffer_size_get(&img_size, format,
                    E_COMPRESS_MODE_NONE, frame_num);
        vb_cfg.ast_commpool[i].blk_cnt = s_comm_vb_cnt;
        vb_cfg.ast_commpool[i].remap_mode = VB_REMAP_MODE_NONE;
    }

    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        return ret;
    }

    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);
   
    vpp_chn_enable[vpp_chnid] = VS_TRUE;

    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
#ifdef VII_FROM_UVC_640_SR_COMP
    vpp_chn_enable[IR_VPP_UVC_RAW_ID] = VS_TRUE;
    memcpy(&vpp_chn_attr[IR_VPP_UVC_RAW_ID], &vpp_chn_attr[vpp_chnid], sizeof(vs_vpp_chn_attr_s));
    vpp_chn_attr[IR_VPP_UVC_RAW_ID].depth = 0;
#endif
    if (npuenable==1)
    {
        vpp_chn_enable[vpp_chnid_nn] = VS_TRUE;
        sample_infra_vpp_chn_nn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid_nn]);
//        vpp_extchn_enable[vpp_extchnid] = VS_TRUE;
//
//        sample_infra_vpp_extchn_attr_get(vpp_chnid, sensor_type,
//                &vpp_extchn_attr[vpp_extchnid]);
    }
//#if defined (UVC_PIC_SEQUENCE) && defined (VII_FROM_UVC_RGB)
//   vs_vpp_crop_info_s crop_info;
//   crop_info.enable = VS_TRUE;
//   crop_info.coordinate_mode = E_COORDINATE_MODE_ABSOLUTE;
//   crop_info.rect.x = 0; 
//   crop_info.rect.y = 0; 
//   crop_info.rect.width = 960; 
//   crop_info.rect.height = 512; 
//   ret =  vs_mal_vpp_grp_crop_set(vpp_grpid, &crop_info);
//    if (ret != VS_SUCCESS) {
//        airvisen_trace("failed to set vpp grp crop \n");
//        printf("failed to set vpp grp crop \n");
//        return ret;
//    }
//#endif


    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        return ret;
    }

//    if (npuenable==1)
//    {
//        ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
//                vpp_extchn_enable, vpp_extchn_attr);
//        if (ret != VS_SUCCESS) {
//            airvisen_trace("failed to set vpp extchn attr\n");
//            return ret;
//        }
//    }
#ifdef RTSP_ENABLED
    ret = sample_common_venc_gop_attr_get(gop_mode, &gop_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("gop_attr_get failed, ret[0x%x]\n", ret);
        //goto exit_vii_unbind_vpp;
        return ret;
    }
    //ret = sample_venc_chn_init(venc_chnid[0], encode_type[0], profile[0], s_vpp_size, brc_mode, &gop_attr);
    ret = sample_venc_chn_init(venc_chnid[0], encode_type[0], profile[0], s_vo_size, brc_mode, &gop_attr);
    if (ret != VS_SUCCESS) {
        vs_sample_trace("sample_venc_chn_init failed, ret[0x%x]\n", ret);
        //goto exit_vii_unbind_vpp;
        return ret;
    }
#ifndef TRACKER_ENABLED    
    if (npuenable==0)
    {
        ret = sample_common_vpp_bind_venc(vpp_grpid, vpp_chnid, venc_devid, venc_chnid[0]);
        if (ret != VS_SUCCESS) {
            vs_sample_trace("sample_common_vpp_bind_venc failed, ret[0x%x]\n", ret);
            //goto exit_venc0_stop;
            return ret;
        }
    }
#endif
#else //< RTSP_ENABLED
    if (npuenable==0)
    {
    // vo start
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                vpp_chnid, vo_devid, vo_chnid);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    }

    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);
#ifdef VII_FROM_UVC_640_SR_COMP
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                IR_VPP_UVC_RAW_ID, vo_devid, IR_VO_UVC_RAW_ID);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    vo_cfg.vo_mode = E_VO_MODE_4MUX;
#endif

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        return ret;
    }
#endif //< RTSP_ENABLED
#if 1
    g_uvc_host_retcode = uvc_host_infra_case_start(s_uvc_frame_size, &camera_info);
    
    if (g_uvc_host_retcode != VS_SUCCESS)
    {
        return -1;
    }
#endif
    
    return VS_SUCCESS;
} 

int uvc_host_infra_case_256(vs_size_s s_uvc_frame_size)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    algo_handle.vb_pool = VS_INVALID_POOLID;

    vs_vpp_grp_attr_s vpp_grp_attr = {0};

    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = IR_VPP_CHN_ID, vpp_extchnid = IR_VPP_EXTCHN_ID, vpp_chnid_nn = IR_VPP_NN_CHN_ID;
    vs_int32_t vo_devid = IR_VO_DEV_ID, vo_chnid = IR_VO_CHN_ID;
    int sensor_type = 0;

    img_size = s_vo_size;
    format =  E_PIXEL_FORMAT_YUV_420SP,

    // sys init
    vb_cfg.pool_cnt = 1;
    for (i = 0; i < vb_cfg.pool_cnt; i++) {
        vb_cfg.ast_commpool[i].blk_size =
            sample_common_buffer_size_get(&img_size, format,
                    E_COMPRESS_MODE_NONE, frame_num);
        vb_cfg.ast_commpool[i].blk_cnt = s_comm_vb_cnt;
        vb_cfg.ast_commpool[i].remap_mode = VB_REMAP_MODE_NONE;
    }

    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        return ret;
    }

    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);
   
    vpp_chn_enable[vpp_chnid] = VS_TRUE;
    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
#ifdef VII_FROM_UVC_256_SR_COMP
    vpp_chn_enable[IR_VPP_UVC_RAW_ID] = VS_TRUE;
    memcpy(&vpp_chn_attr[IR_VPP_UVC_RAW_ID], &vpp_chn_attr[vpp_chnid], sizeof(vs_vpp_chn_attr_s));
    vpp_chn_attr[IR_VPP_UVC_RAW_ID].depth = 0;
#endif

    if (npuenable==1)
    {
       vpp_chn_enable[vpp_chnid_nn] = VS_TRUE;
       sample_infra_vpp_chn_nn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid_nn]);
//       vpp_extchn_enable[vpp_extchnid] = VS_TRUE;
//
//       sample_infra_vpp_extchn_attr_get(vpp_chnid, sensor_type,
//               &vpp_extchn_attr[vpp_extchnid]);
    }
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        return ret;
    }

//    if (npuenable==1)
//    {
//       ret = sample_comm_vpp_extchn_attr_set(vpp_grpid,
//               vpp_extchn_enable, vpp_extchn_attr);
//       if (ret != VS_SUCCESS) {
//           airvisen_trace("failed to set vpp extchn attr\n");
//           return ret;
//       }
//    }
    if (npuenable==0)
    {
    // vo start
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                vpp_chnid, vo_devid, vo_chnid);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    }

    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);

#ifdef VII_FROM_UVC_256_SR_COMP
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                IR_VPP_UVC_RAW_ID, vo_devid, IR_VO_UVC_RAW_ID);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    vo_cfg.vo_mode = E_VO_MODE_4MUX;
#endif

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        return ret;
    }
#if 1
    g_uvc_host_retcode = uvc_host_infra_case_start(s_uvc_frame_size, &camera_info);
    
    if (g_uvc_host_retcode != VS_SUCCESS)
    {
        return -1;
    }
#endif
    
    return VS_SUCCESS;
} 

int uvc_host_infra_case_384(vs_size_s s_uvc_frame_size)
{
    vs_int32_t ret = VS_SUCCESS, i = 0;

    vs_uint32_t frame_num = 1;
    vs_size_s img_size;
    vs_pixel_format_e format;
    vs_vb_cfg_s vb_cfg = {0};
    sample_infra_sensor_cfg_s *p_sensor_cfg;

    algo_handle.vb_pool = VS_INVALID_POOLID;

    vs_vpp_grp_attr_s vpp_grp_attr = {0};

    vs_int32_t vpp_grpid = VII_DST_PIPE, vpp_chnid = IR_VPP_CHN_ID, vpp_extchnid = IR_VPP_EXTCHN_ID, vpp_chnid_nn = IR_VPP_NN_CHN_ID;
    vs_int32_t vo_devid = IR_VO_DEV_ID, vo_chnid = IR_VO_CHN_ID;
    int sensor_type = 0;

    img_size = s_vo_size;
    format =  E_PIXEL_FORMAT_YUV_420SP,

    // sys init
    vb_cfg.pool_cnt = 1;
    for (i = 0; i < vb_cfg.pool_cnt; i++) {
        vb_cfg.ast_commpool[i].blk_size =
            sample_common_buffer_size_get(&img_size, format,
                    E_COMPRESS_MODE_NONE, frame_num);
        vb_cfg.ast_commpool[i].blk_cnt = s_comm_vb_cnt;
        vb_cfg.ast_commpool[i].remap_mode = VB_REMAP_MODE_NONE;
    }

    ret = sample_common_sys_init(&vb_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to sys init\n");
        return ret;
    }

    sample_infra_vpp_grp_attr_get(sensor_type, &vpp_grp_attr);
   
    vpp_chn_enable[vpp_chnid] = VS_TRUE;
    sample_infra_vpp_chn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid]);
#ifdef VII_FROM_UVC_384_SR_COMP
    vpp_chn_enable[IR_VPP_UVC_RAW_ID] = VS_TRUE;
    memcpy(&vpp_chn_attr[IR_VPP_UVC_RAW_ID], &vpp_chn_attr[vpp_chnid], sizeof(vs_vpp_chn_attr_s));
    vpp_chn_attr[IR_VPP_UVC_RAW_ID].depth = 0;
#endif

    if (npuenable==1)
    {
       vpp_chn_enable[vpp_chnid_nn] = VS_TRUE;
       sample_infra_vpp_chn_nn_attr_get(sensor_type, &vpp_chn_attr[vpp_chnid_nn]);
   }
    ret = sample_common_vpp_start(vpp_grpid, vpp_chn_enable,
            &vpp_grp_attr, vpp_chn_attr);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vpp\n");
        return ret;
    }

    if (npuenable==0)
    {
    // vo start
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                vpp_chnid, vo_devid, vo_chnid);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    }

    sample_infra_vo_cfg_get(sensor_type, &vo_cfg);

#ifdef VII_FROM_UVC_384_SR_COMP
        ret = sample_common_vpp_bind_vo(vpp_grpid,
                IR_VPP_UVC_RAW_ID, vo_devid, IR_VO_UVC_RAW_ID);
        if (ret != VS_SUCCESS) {
            airvisen_trace("failed to bind vpp vo\n");
            return ret;
        }
    vo_cfg.vo_mode = E_VO_MODE_4MUX;
#endif

    ret = sample_common_vo_start(&vo_cfg);
    if (ret != VS_SUCCESS) {
        airvisen_trace("failed to start vo\n");
        return ret;
    }
#if 1
    g_uvc_host_retcode = uvc_host_infra_case_start(s_uvc_frame_size, &camera_info);
    
    PRINTLINE
    if (g_uvc_host_retcode != VS_SUCCESS)
    {
        return -1;
    }
#endif
    
    PRINTLINE
    return VS_SUCCESS;
} 
#endif //< VII_FROM_UVC

int infra_proc_initialize(double dde_threshold)
{
    vs_int32_t ret = VS_SUCCESS;
    vs_int32_t idx;
    sample_infra_sensor_type_e sensor_type;

//    sample_vio_register_signal_handler(vii_signal_handle);
    sample_infra_register_signal_handler(sample_infra_signal_handle);
#ifdef TRACKER_ENABLED
#if defined (VII_FROM_UVC_RGB)
    idx = 4;
    vs_size_s s_uvc_frame_size = {RGB_WIDTH, RGB_HEIGHT};
#else
    idx = 8;
    sensor_type = 0;
    g_sensor_type[0] =(sample_sensor_type_e)(7);
    g_bus_id[0] = 0;
    vs_size_s s_uvc_frame_size = {640, 512};
#endif
#else

#if defined (DUAL_LENSES)
    idx = 1;
    sensor_type = 0;

    g_sensor_type[0] =(sample_sensor_type_e)(6);
    g_bus_id[0] = 5;
#elif defined (VII_FROM_UVC_640)
    idx = 4;
    vs_size_s s_uvc_frame_size = {640, 512};
#elif defined (VII_FROM_UVC_256)
    idx = 5;
    vs_size_s s_uvc_frame_size = {256, 192};
#elif defined (VII_FROM_UVC_384)
    idx = 6;
    vs_size_s s_uvc_frame_size = {384, 288};
#else
    idx = 3;
#endif
#endif //< TRACKER_ENABLED
    
    printf("\nidx:%d\n", idx);   
    switch (idx) {
        case 0:
            s_algo_type = SAMPLE_INFRA_ALGO_DSP;
            ret = sample_infra_case(sensor_type);
            break;
        case 1:
            s_algo_type = SAMPLE_INFRA_ALGO_CPU;
            ret = sample_dual_case(sensor_type, dde_threshold);
            break;
        case 2:
            s_algo_type = SAMPLE_INFRA_ALGO_DSP;
            ret = sample_infra_reconfig_case(sensor_type);
            break;
        case 3:
            s_algo_type = SAMPLE_INFRA_ALGO_CPU;
#ifdef RTSP_ENABLED
            ret = sample_infra_case_venc(sensor_type);
#else
            ret = sample_infra_case(sensor_type);
#endif
            break;
        case 4:
            s_algo_type = SAMPLE_INFRA_ALGO_CPU;
#ifdef VII_FROM_UVC
#ifdef RTSP_ENABLED
            ret = uvc_host_infra_case_venc(s_uvc_frame_size);
#else
            ret = uvc_host_infra_case(s_uvc_frame_size);
#endif
#endif
            break;
        case 5:
#ifdef VII_FROM_UVC_256
            s_algo_type = SAMPLE_INFRA_ALGO_CPU;
            ret = uvc_host_infra_case_256(s_uvc_frame_size);
            
#endif
            break;
        case 6:
#ifdef VII_FROM_UVC_384
            s_algo_type = SAMPLE_INFRA_ALGO_CPU;
            ret = uvc_host_infra_case_384(s_uvc_frame_size);
            PRINTLINE
            
#endif
            break;
        case 8:
            {
                s_algo_type = SAMPLE_INFRA_ALGO_CPU;
#ifdef RTSP_ENABLED
                //ret = tracker_rgbvii_infrauvc_venc_case(s_uvc_frame_size);
#else
                //ret = tracker_rgbvii_infrauvc_vo_case(s_uvc_frame_size);
#endif
            }
            break;

        default:
            
            return VS_FAILED;
    }

    return ret;
}

