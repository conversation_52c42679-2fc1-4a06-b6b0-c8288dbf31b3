/**
 * @file    sample_nn.c
 * @brief   sample nn implementation
 * @details
 * <AUTHOR> Software Group
 * @date    2022-05-25
 * @version v1.00
 * @Copyright (c) 2022 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#include <stdint.h>
#include <stdlib.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <signal.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <errno.h>
#include <dlfcn.h>
#include <pthread.h>
#include <linux/videodev2.h>
#include "sample_common.h"
#include "sample_common_infra.h"
#include "configparser.h"
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui_c.h>
#include <opencv2/highgui/highgui.hpp>
#include<opencv2/opencv.hpp>
#include "denoise.h"
#include <iostream>
#include "webserver_message.h"
#include "laser_range.h"
#include "ir_ctrl_message.h"
#include "osd_message.h"

#ifdef SHUTTER_AVAILABLE  
#include "shutter.h"
#endif
#ifdef SERIAL_COMM_ENABLED
//#include "../serial/Serial.hpp"
#endif
#ifdef TRACKER_ENABLED
#include "tracker.h"
// #include "./single_tracker/tracker.h"
#endif
#ifdef AIRVISEN_RGN_ENABLED
#include "rgn.h"
#endif
#ifdef DUMP_USING_RINGBUFFER
extern int destory_ringbuffer();
extern int initialize_ringbuffer();
extern int dumpToRingBuffer(void *ptr, int datalen, char* filename);
#endif


#include "store_jpeg_message.h"
#include "video_record_message.h"
#include "video_zoom_message.h"
#include "algorithm_message.h"

char *g_strConfigIni = "config.ini";
int g_traceinfo;

using namespace std;
using namespace cv;
#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"
#ifdef NN_ENABLED
#include "nn.h"

#ifdef ENCRYPT_MODEL_ENABLE
#include "security.h"
#endif

#ifdef HEQI_RGN_OVERLAY
#include "heq_rgn.h"
#endif

#include "config_tool.h"
#include "report_message.h"

sample_nn_info_s nn_model_denoise ;

sample_nn_info_s nn_model_sr;

char configparserbuf[1024];
char denoisemodelname[256];
char srmodelname[256];
char srmode[256]; //< x2, x4, etc
char detectionmodelname[256];
char nanotrackmodelname[256];
float claritylevel=8.0f;
float blurradius=1.5f;
int npuenable=1;
int detectenable=1;
int nanotrackenable=1;
sample_nn_model_id_e detect_model_id=NN_MODEL_ID_YOLOV5N;
sample_nn_model_id_e nanotrack_model_id=NN_MODEL_ID_NANOTRACKV3;
int denoiseenable=1;
int srenable=1;
int sharpenenable=1;
int showdebuglogo=1;

#endif

using namespace std;

static vs_int32_t               g_cluster_id = 0;
static vs_int32_t               g_cluster_id_track = 1;
static vs_int32_t               g_cluster_id_de = 1;
static vs_int32_t               g_cluster_id_sr = 1;
volatile sig_atomic_t    g_stop_flag = 0;
char ddealgotype[256];
#ifdef ASPECT_RATIO_16_9
vs_size_s s_vo_size = {1920, 1080};
#else
vs_size_s s_vo_size = {640, 512};
#endif
vs_uint32_t s_vo_fps_supported[] = {25, 30, 50, 60};
vs_uint32_t s_vo_fps = 60;

#ifdef VII_FROM_UVC_256
vs_size_s s_vpp_size = {256, 192};
vs_size_s s_vpp_sr_size = {1024, 768};
#elif defined(VII_FROM_UVC_384)
vs_size_s s_vpp_size = {384, 288};
vs_size_s s_vpp_sr_size = {1536, 1152};
#elif defined(VII_FROM_UVC_RGB)
vs_size_s s_vpp_size = {RGB_WIDTH, RGB_HEIGHT};
vs_size_s s_vpp_sr_size = {RGB_WIDTH * 4, RGB_HEIGHT * 4};
#else
vs_size_s s_vpp_size = {640, 512};
vs_size_s s_vpp_sr_size = {1280, 1024};
#endif

#ifdef SERIAL_COMM_ENABLED
//Serial* g_ptrSerial;
//上位机通讯类型：-1-未知、0-串口通讯、1-tcp通讯、2-gpio触发、3-tcp+串口（调试用）
int g_uppercommtype = -1;
#endif

#ifdef TRACKER_ENABLED
int b_drawTrackingBox = 1;
//跟踪偏移采样中间倍率(0, 1]，用来减缓云台运动速度过快导致的震荡，6、8mm相机为0.5，3mm相机为1
float g_tracksampledivisor = 1.0;
float g_long_lens_tracksampledivisor = 1.0;
// std::shared_ptr<NNSingleTracker> nanotrack_tracker;
#endif


//serial for fpv control, reply nn detect info.
char g_strSerialName[256] = {"/dev/ttyAMA0"};



void airvisen_pause()
{
    printf("\n=====Press enter to exit=====\n");
    char ch[20];
    std::cin.ignore(-1, '\n');
    while (1) {
        std::cin.get(ch, 1);
        if (ch[0] = 'q') {
            printf("will break!\n");
            break;
        }
        else
        {
            printf("%c\n", ch[0]);
        }
        usleep(1000);
    };
    printf("\n=====exit=====\n");
}

/* get file size */
static vs_uint32_t nn_file_size_get(const vs_char_t *name)
{
    FILE *fp = fopen((char*)name,"rb");
    vs_uint32_t size = 0;
    if (fp) {
        fseek(fp,0,SEEK_END);
        size = ftell(fp);
        fclose(fp);
    } else {
        airvisen_trace("fopen file %s failed.\n", name);
        return 0;
    }
    return size;
}

/* read file */
static vs_int32_t nn_file_read(const vs_char_t *name, vs_void_t *dst)
{
    vs_uint32_t size = 0;

    FILE *fp = fopen((char*)name,"rb");
    if (fp) {
        fseek(fp,0,SEEK_END);
        size = ftell(fp);
        fseek(fp,0,SEEK_SET);
        fread(dst,size,1,fp);
        fclose(fp);
        airvisen_trace("fread file: %s.\n",name);
    } else {
        airvisen_trace("fopen file %s failed.\n", name);
        return VS_FAILED;
    }
    return VS_SUCCESS;
}



    // callback
void onLaserCallback(laser_range_s &laser_range_data)
{

    // std::cout << "direct_distance: " << laser_range_data.direct_distance << std::endl;
    // std::cout << "horizontal_distance: " << laser_range_data.horizontal_distance << std::endl;
    // std::cout << "pitch_angle: " << laser_range_data.pitch_angle << std::endl;
    // std::cout << "sine_height: " << laser_range_data.sine_height << std::endl;
    update_laser_osd(laser_range_data.direct_distance);
}


#define STRING(x) #x
#define TO_STRING(x) STRING((x))
#ifdef GIT_SHA1
  const char* gitsha1 = TO_STRING(GIT_SHA1);
#endif
#ifdef GIT_DESC
  const char* gitdesc = TO_STRING(GIT_DESC);
#endif
int main(int argc, char *argv[])
{
#ifdef GIT_DESC
    printf("%s\n", gitdesc);
#endif

#ifdef GIT_SHA1
    printf("%s\n", gitsha1);
#endif

    printf("buildinfo: %s, %s\n", __DATE__, __TIME__);
    
    vs_int32_t ret = VS_SUCCESS;
    pthread_t  vo_thread_id = 0;
    vs_void_t* thread_result = VS_NULL;


#ifdef ENCRYPT_MODEL_ENABLE

    if(security_auth() != 0){
        printf("security_auth failed!\n");
        return VS_FAILED;
    }

#endif
    
    printf("opencv_version:%s\n", CV_VERSION);
    //return 0;

    if (argc > 1)
    {
        printf("argv[1]:%s\n", argv[1]);
        if (strstr(argv[1], ".ini") != NULL)
        {
            g_strConfigIni = argv[1];
            printf("config file from argv[1]: %s\n", g_strConfigIni);
        }
        else
        {
            printf("config file should have .ini suffix\n");
            return 0;
        }
    }

    int traceenable = GetIniKeyInt("debug","trace", g_strConfigIni);
    set_traceinfo(traceenable);

    airvisen_trace("buildinfo: %s, %s\n", __DATE__, __TIME__);
#ifdef QLW_38B_VERSION
    airvisen_trace("shutter drv version: %d\n", QLW_38B_VERSION);
#endif

    // PRINTLINE
    showdebuglogo = GetIniKeyInt("debug","showdebuglogo", g_strConfigIni);
    // PRINTLINE

#ifdef SERIAL_COMM_ENABLED
    char * tmp_serial_name = GetIniKeyString("tracking","serialname", g_strConfigIni, configparserbuf, 256);
    if (tmp_serial_name != NULL)
    {
        memset(g_strSerialName, 0, 256);
        strcpy(g_strSerialName, tmp_serial_name);
        printf("command control serial name:%s ,len:%d\n", g_strSerialName,strlen(tmp_serial_name));
    }
    // PRINTLINE
    std::string serialID(g_strSerialName);
    // PRINTLINE
	// PRINTLINE
	//上位机通讯类型：-1-未知、0-串口通讯、1-tcp通讯、2-gpio触发、3-tcp+串口（调试用）
	g_uppercommtype = GetIniKeyInt("tracking", "uppercommtype", g_strConfigIni);
	airvisen_trace("uppercommtype:%d\n", g_uppercommtype);

#endif
#ifdef TRACKER_ENABLED
    b_drawTrackingBox = GetIniKeyInt("tracking", "drawtrackingbox", g_strConfigIni);    
    airvisen_trace("b_drawTrackingBox:%d\n", b_drawTrackingBox);
	
    //跟踪偏移采样中间倍率(0, 1]，用来减缓云台运动速度过快导致的震荡，6、8mm相机为0.5，3mm相机为1
    g_tracksampledivisor = GetIniKeyFloat("tracking", "tracksampledivisor", g_strConfigIni);
    airvisen_trace("g_tracksampledivisor: %f\n", g_tracksampledivisor);


    //long lens divisor
    char *tmp_divisior = GetIniKeyString("tracking", "long_lens_tracksampledivisor", g_strConfigIni);
    if(tmp_divisior == NULL){
        airvisen_trace("can not find long_lens_tracksampledivisor,use default value 0.2\n");
        g_long_lens_tracksampledivisor = 0.2;
    }else{
        g_long_lens_tracksampledivisor = atof(tmp_divisior);
        airvisen_trace("long_lens_tracksampledivisor: %s divisor:%f\n", tmp_divisior, g_long_lens_tracksampledivisor);
    }


    char *tmp_dx = GetIniKeyString("tracking", "x_long_lens_offset", g_strConfigIni);
    if(tmp_dx == NULL){
        airvisen_trace("can not find x_long_lens_offset\n");
    }else{
        int dx = atoi(tmp_dx);
        dx = (dx % 16) == 0 ? dx : dx + (16 - (dx % 16));
        set_long_focal_lens_x_offset(dx);
        airvisen_trace("x_long_lens_offset: %s g_long_lens_dx:%d\n", tmp_dx, dx);
    }
    

    char *tmp_dy = GetIniKeyString("tracking", "y_long_lens_offset", g_strConfigIni);
    if(tmp_dy == NULL){
        airvisen_trace("can not find y_long_lens_offset\n");
    }else{
        int dy = atoi(tmp_dy);
        set_long_focal_lens_y_offset(dy);
        airvisen_trace("y_long_lens_offset: %s g_long_lens_dy:%d\n", tmp_dy, dy);
    }

#ifdef GPIO_TRIGGER_MODE
    int gpio_num = 81;
    int gpio_jitter_time = 2000;
    int gpio_trigger_level = 1; 
    char* gpio_str = GetIniKeyString("tracking","gpio_num", g_strConfigIni, configparserbuf, 256);
    if(gpio_str != NULL){
        gpio_num = atoi(gpio_str);
    }

    gpio_str = GetIniKeyString("tracking","gpio_jitter_time", g_strConfigIni, configparserbuf, 256);
    if(gpio_str != NULL){
        gpio_jitter_time = atoi(gpio_str);
    }

    gpio_str = GetIniKeyString("tracking","gpio_trigger_level", g_strConfigIni, configparserbuf, 256);
    if(gpio_str != NULL){
        gpio_trigger_level = atoi(gpio_str);
    }
    printf("gpio_num:%d, gpio_jitter_time:%d, gpio_trigger_level:%d\n", gpio_num, gpio_jitter_time, gpio_trigger_level) ;
#endif
#endif



#ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
    vs_uint32_t rgn_cover_errcode = 0; 
#endif

#ifdef HEQI_RGN_OVERLAY
    vs_uint32_t rgn_heq_errcode = 0;
#endif

#ifdef AIRVISEN_RGN_OVERLAYEX_ENABLED
    vs_uint32_t rgn_coverex_errcode = 0; 
#endif

#ifdef SHUTTER_AVAILABLE  
    //< init shutter
    create_shutter_daemon();
    shutter_on_async();
#endif

    unsigned short dde_threshold = 5;
    // PRINTLINE

    dde_threshold = GetIniKeyInt("algo","ddethreshold", g_strConfigIni);
    vs_uint32_t displayWidth = GetIniKeyInt("display","width", g_strConfigIni);
    vs_uint32_t displayHeight = GetIniKeyInt("display","height", g_strConfigIni);
    s_vo_size = {displayWidth, displayHeight};
    s_vo_fps = GetIniKeyInt("display","fps", g_strConfigIni);
    bool is_vo_fps_setting_valid = false;
    for (int i = 0; i < sizeof(s_vo_fps_supported)/sizeof(vs_uint32_t); i++)
    {
        printf("supported VO fps[%d]: %d\n", i, s_vo_fps_supported[i]);
        if (s_vo_fps == s_vo_fps_supported[i])
        {
            is_vo_fps_setting_valid = true;
            break;
        }
    }

    if (!is_vo_fps_setting_valid)
    {
        s_vo_fps = 30;
        printf("fallback to 30fps for VO\n");
    }

    // PRINTLINE
    npuenable = GetIniKeyInt("npu","npuenable", g_strConfigIni);
    airvisen_trace("npuenable:%d\n", npuenable);
    airvisen_trace("dde threshold :%d\n", dde_threshold);
    airvisen_trace("@@@@@@displayWidth:%d, height:%d\n", displayWidth, displayHeight);
    airvisen_trace("traceenable = %d\n", traceenable);
    char * tmpPtr =GetIniKeyString("algo","ddealgotype", g_strConfigIni, configparserbuf, 256);
    memset(ddealgotype, 0, 256);
    memcpy(ddealgotype, tmpPtr, 256);
    airvisen_trace("ddealgotype:%s\n", ddealgotype);

    // PRINTLINE
#ifdef DUMP_USING_RINGBUFFER
    initialize_ringbuffer();
#endif

    // IPC, PAIR MODE
    airvisen_start_store_jpeg_ipc();
    airvisen_start_video_record_ipc();
    airvisen_start_video_zoom_ipc();
    airvisen_start_ir_ctrl_ipc();
    airvisen_start_osd_ctrl_ipc();
    airvisen_start_detector_ipc();
    airvisen_start_tracker_ipc();

    // PRINTLINE
    ret = infra_proc_initialize(dde_threshold);

#ifdef NN_ENABLED
    sample_nn_producer_info_s producer_info;
    //npuenable = GetIniKeyInt("npu","npuenable", g_strConfigIni);

    //if (npuenable == 1)
    if (0)
    {
    // PRINTLINE
        detectenable = GetIniKeyInt("detection","detectenable", g_strConfigIni);
        detect_model_id = sample_nn_model_id_e(GetIniKeyInt("detection","model_id", g_strConfigIni));
        nanotrackenable = GetIniKeyInt("nanotrack","nanotrackenable", g_strConfigIni);
        nanotrack_model_id = sample_nn_model_id_e(GetIniKeyInt("nanotrack","model_id", g_strConfigIni));
        denoiseenable = GetIniKeyInt("denoise","denoiseenable", g_strConfigIni);
        srenable = GetIniKeyInt("sr","srenable", g_strConfigIni);
        sharpenenable = GetIniKeyInt("sharpen","sharpenenable", g_strConfigIni);
        airvisen_trace("npuenable:%d\n", npuenable);
        airvisen_trace("denoiseenable:%d\n", denoiseenable);
        airvisen_trace("detectenable:%d\n", detectenable);
        airvisen_trace("sharpenenable:%d\n", sharpenenable);

        int tempclaritylevel = GetIniKeyInt("sharpen","claritylevel", g_strConfigIni);
        int tempblurradius = GetIniKeyInt("sharpen","blurradius", g_strConfigIni);
        blurradius = tempblurradius / 100.f;
        claritylevel = tempclaritylevel / 100.f;

        tmpPtr = GetIniKeyString("denoise","modelname", g_strConfigIni, configparserbuf, 256);
        memset(denoisemodelname, 0, 256);
        memcpy(denoisemodelname, tmpPtr, 256);
        airvisen_trace("denoisemodelname:%s\n", denoisemodelname);
        airvisen_trace("claritylevel:%f\n", claritylevel);
        airvisen_trace("blurradius:%f\n", blurradius);

        tmpPtr = GetIniKeyString("sr","modelname", g_strConfigIni, configparserbuf, 256);
        memset(srmodelname, 0, 256);
        memcpy(srmodelname, tmpPtr, 256);
        airvisen_trace("srmodelname:%s\n", srmodelname);

        tmpPtr = GetIniKeyString("sr","srmode", g_strConfigIni, configparserbuf, 256);
        memset(srmode, 0, 256);
        memcpy(srmode, tmpPtr, 256);
        airvisen_trace("srmode:%s\n", srmode);

        tmpPtr = GetIniKeyString("nanotrack","modelname", g_strConfigIni, configparserbuf, 256);
        memset(nanotrackmodelname, 0, 256);
        memcpy(nanotrackmodelname, tmpPtr, 256);
        airvisen_trace("nanotrackmodelname:%s\n", nanotrackmodelname);
        if (0 == strcmp(tmpPtr, "x4"))
        {
            s_vpp_sr_size.width = s_vpp_size.width * 4;
            s_vpp_sr_size.height = s_vpp_size.height * 4;
        }
        else if (0 == strcmp(tmpPtr, "x8"))
        {
            s_vpp_sr_size.width = s_vpp_size.width * 8;
            s_vpp_sr_size.height = s_vpp_size.height * 8;
        }
        else //< currently, only support x4, x2
        {
            s_vpp_sr_size.width = s_vpp_size.width * 2;
            s_vpp_sr_size.height = s_vpp_size.height * 2;
        }


        printf("s_vpp_sr_size:[%d, %d]\n", s_vpp_sr_size.width, s_vpp_sr_size.height);

        tmpPtr = GetIniKeyString("detection","modelname", g_strConfigIni, configparserbuf, 256);
        memset(detectionmodelname, 0, 256);
        memcpy(detectionmodelname, tmpPtr, 256);
        airvisen_trace("detectionmodelname:%s\n", detectionmodelname);


        if ((denoiseenable == 1) && (srenable == 1))
        {
            printf("ERROR! denoise & SR can't be ENABLED both!!!\n");
            return -1;
        }

        //sample_nn_producer_info_s producer_info;

        nn_default_producer_info_get(&producer_info);

        ret = sample_common_nn_init();
        if (ret != VS_SUCCESS) {
            vs_sample_trace("sample_common_nn_init failed with err 0x%x\n", ret);
            return ret;
        }

        // if (detectenable == 1)
        // {
        //     vs_sample_trace("detect_model_id:%d, detectionmodelname: %s\n", detect_model_id, detectionmodelname);
        //     sample_common_nn_nb_info_get(detect_model_id, VS_TRUE, &producer_info.nn_info.nb_info);
        //     memcpy(&producer_info.nn_info.nb_info.nb_file, detectionmodelname,NN_NB_FILE_PATH_MAX); 
        //     // yolo init
        //     producer_info.nn_info.cluster_id = g_cluster_id;
        //     producer_info.nn_info.disp_rgn_handle = 1;

        //     //printf("#1. nn_model->output_num:%d\n", producer_info.nn_info.model.output_num);
        //     ret = sample_common_nn_create(&producer_info.nn_info);
        //     if (ret != VS_SUCCESS) {
        //         vs_sample_trace("sample_common_nn_create failed with err 0x%x\n", ret);
        //         goto exit0;
        //     }

        //     //printf("#2. nn_model->output_num:%d\n", producer_info.nn_info.model.output_num);

        //     producer_info.nn_input_img_size.width = producer_info.nn_info.model.inputs[0].dim_size[0];
        //     producer_info.nn_input_img_size.height = producer_info.nn_info.model.inputs[0].dim_size[1];
        //     vs_sample_trace("get width[%u] height[%u]\n", producer_info.nn_input_img_size.width, producer_info.nn_input_img_size.height );

        //     // ret = sample_common_nn_run(&producer_info.nn_info);

        // }
// #ifdef TRACKER_ENABLED
//         if (nanotrackenable == 1)
//         {
//             nanotrack_tracker = create_nanotrack_tracker();
//             sample_common_nn_nb_info_get(nanotrack_model_id, VS_TRUE, &producer_info.nn_info_track.nb_info);
//             memcpy(&producer_info.nn_info_track.nb_info.nb_file, nanotrackmodelname,NN_NB_FILE_PATH_MAX); 
//             // yolo init
//             producer_info.nn_info_track.cluster_id = g_cluster_id_track;
//             producer_info.nn_info_track.disp_rgn_handle = 1;

//             //printf("#1. nn_model->output_num:%d\n", producer_info.nn_info.model.output_num);
//             // ret = sample_common_nn_create(&producer_info.nn_info_track);
//             if (ret != VS_SUCCESS) {
//                 vs_sample_trace("sample_common_nn_create failed with err 0x%x\n", ret);
//                 goto exit0;
//             }

//             //printf("#2. nn_model->output_num:%d\n", producer_info.nn_info.model.output_num);

//             producer_info.nn_input_img_size.width = producer_info.nn_info_track.model.inputs[0].dim_size[0];
//             producer_info.nn_input_img_size.height = producer_info.nn_info_track.model.inputs[0].dim_size[1];
//             vs_sample_trace("================================\n");
//             vs_sample_trace("get width[%u] height[%u]\n", producer_info.nn_input_img_size.width, producer_info.nn_input_img_size.height );
//             vs_sample_trace("================================\n");

//             // ret = sample_common_nn_run(&producer_info.nn_info_track);

//         }
// #endif
        if (denoiseenable == 1)
        {
            sample_common_nn_nb_info_get(NN_MODEL_ID_DENOISING, VS_TRUE, &producer_info.nn_info_denoise.nb_info);
            memcpy(&producer_info.nn_info_denoise.nb_info.nb_file, denoisemodelname,NN_NB_FILE_PATH_MAX); 
            // denoise init 
            producer_info.nn_info_denoise.cluster_id = g_cluster_id_de;
            producer_info.nn_info_denoise.disp_rgn_handle = 2;

            ret = sample_common_nn_create(&producer_info.nn_info_denoise);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("sample_common_nn_create failed with err 0x%x\n", ret);
                goto exit0;
            }
            nn_model_denoise = producer_info.nn_info_denoise;
        }

        if (srenable == 1)
        {
            sample_common_nn_nb_info_get(NN_MODEL_ID_SR, VS_TRUE, &producer_info.nn_info_sr.nb_info);
            memcpy(&producer_info.nn_info_sr.nb_info.nb_file, srmodelname,NN_NB_FILE_PATH_MAX); 
            // sr init 
            producer_info.nn_info_sr.cluster_id = g_cluster_id_sr;
            producer_info.nn_info_sr.disp_rgn_handle = 2;

            ret = sample_common_nn_create(&producer_info.nn_info_sr);
            if (ret != VS_SUCCESS) {
                vs_sample_trace("sample_common_nn_create failed with err 0x%x\n", ret);
                goto exit0;
            }
            nn_model_sr = producer_info.nn_info_sr;

        }

    // PRINTLINE
#ifndef TRACKER_ENABLED
        pthread_create(&vo_thread_id, 0, nn_producer_vo_proc, (vs_void_t *)&producer_info);
#ifdef  SEND_DETECT_RESULT_VIA_SERIAL
        pthread_create(&vo_thread_id, 0, nn_comm_send_proc, (vs_void_t *)&producer_info);
#endif

#endif
    }
#endif


    start_webserver_message_proc();
   // PRINTLINE

// #ifdef SERIAL_COMM_ENABLED
// 	if (access(serialID.c_str(), F_OK) != -1)
// 	{  
// 		g_ptrSerial = new Serial(); 
// 		g_ptrSerial->OpenSerial(serialID, _115200, _8, None, _1);  
// 	} else {  
// 	   vs_sample_trace("%s does not exist.\n", serialID.c_str());  
// 	} 
// #endif
#ifdef TRACKER_ENABLED
     //gpio trigger mode
#ifdef GPIO_TRIGGER_MODE
    gpio_trigger_s gpio_trigger;
    gpio_trigger.gpio_num = gpio_num;
    gpio_trigger.jitter_time = gpio_jitter_time;
    gpio_trigger.trigger_level = gpio_trigger_level;
    gpio_trigger_init((void*)&gpio_trigger);
#endif

	tracker_initialize((void *)&producer_info);
#endif

    start_report_message_proc();

#ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
    rgn_cover_errcode = airvisen_rgn_venc_overlay_initialize();


    //PRINTLINE
    //sleep(15);
    airvisen_rgn_venc_overlay_display_on_off(true);
    airvisen_video_record_overlay_display_on_off(false);
    //PRINTLINE

    //airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 0, "MODE YAW_INIT");
    //airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 1, "TRACK OFF");
    //airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 2, "DETECT OFF");
    airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 3, "ZOOM:1.0X");
    //airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 4, "0 0@");
    //
#ifndef HEQI_RGN_OVERLAY
    airvisen_draw_triangle(AIRVISEN_TRACKER_DRAW_TRIANGLE_HOR_RGN_HANDLE, -60.0f);
    //PRINTLINE
    airvisen_draw_triangle(AIRVISEN_TRACKER_DRAW_TRIANGLE_VER_RGN_HANDLE, 30.0f);
    //PRINTLINE
#endif
    
    airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_LEFT_RGN_HANDLE, 0, "W:1.00050");
//    sleep(15);
//    airvisen_rgn_venc_overlay_display_on_off(false);
    //PRINTLINE
if(traceenable == 1)
    airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 4, __TIME__);


#endif


#ifdef HEQI_RGN_OVERLAY

    rgn_heq_errcode = airvisen_rgn_venc_overlay_initialize();

    //PRINTLINE
    //sleep(15);
    airvisen_rgn_venc_overlay_display_on_off(true);
    //PRINTLINE

    //airvisen_draw_tracker_text(40, 0,   "ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ");
    //airvisen_draw_tracker_text(40, 0, "Z:1.0X H:400.0M S:30.0M/S B:4.2V G:50.0@, U:-60.0@");
    //airvisen_draw_tracker_text(40, 1, "ATTACK");

    //airvisen_draw_tracker_text_align_center(40, 0, "Z:1.0X H:400.0M S:30.0M/S B:4.2V G:50.0@, U:-60.0@",0xFFFFFFFF ,12, VS_TRUE);
    //airvisen_draw_tracker_text_align_center(40, 1, "ATTACK", 0xFFFFFFFF, 12, VS_TRUE);
    //airvisen_draw_tracker_text_align_center(40, 2, "Z:1.0X H:400.0M S:30.0M/S B:4.2V G:50.0@, U:-60.0@",0xFFFFFFFF ,12, VS_TRUE);
    //heq_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 1, "TRACK OFF");
    //heq_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 2, "DETECT OFF");
    //airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 3, "ZOOM:1.0X");
    //heq_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 4, "0 0@");
    //
    
    //heq_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_LEFT_RGN_HANDLE, 0, "W:1.00050");

//if(traceenable == 1)
//   airvisen_draw_tracker_text(AIRVISEN_TRACKER_DRAW_TEXT_RIGHT_RGN_HANDLE, 4, __TIME__);
    
#endif

#ifdef LASER_DIST_OSD_ENABLED
    start_laser_range(onLaserCallback);
#endif

#ifdef AIRVISEN_RGN_OVERLAYEX_ENABLED
    rgn_coverex_errcode = airvisen_rgn_overlayex_initialize();
#endif

   sample_common_pause();

    stop_report_message_proc();

#ifdef AIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
    airvisen_rgn_venc_overlay_destory(rgn_cover_errcode);
#endif

#ifdef HEQI_RGN_OVERLAY
    airvisen_rgn_venc_overlay_destory(rgn_heq_errcode);
#endif

#ifdef AIRVISEN_RGN_OVERLAYEX_ENABLED
    airvisen_rgn_overlayex_destory(rgn_coverex_errcode);
#endif


#ifdef TRACKER_ENABLED
   tracker_destory();
   airvisen_stop_tracker_ipc();
   airvisen_stop_detector_ipc();
#endif

// #ifdef SERIAL_COMM_ENABLED
// 	if (access(g_strSerialName, F_OK) != -1)
// 	{
// 	    g_ptrSerial->CloseSerial();
// 	    sleep(2);    
// 	    delete g_ptrSerial;
// 	}
// #endif

    printf("come after tracker destory()\n");
    //PRINTLINE 
    infra_proc_destory();
    //PRINTLINE 
#ifdef SHUTTER_AVAILABLE  
    destory_shutter_daemon();
#endif

    //PRINTLINE 
#ifdef DUMP_USING_RINGBUFFER
    destory_ringbuffer();
#endif
    //PRINTLINE 

    g_stop_flag = VS_TRUE;
#if NN_ENABLED
    //PRINTLINE 
    if (npuenable == 1)
    {
        sample_common_nn_stop(&producer_info.nn_info);
        sample_common_nn_stop(&producer_info.nn_info_track);
    exit3:
        if (producer_info.nn_info.nb_info.model_type == E_NN_MODE_STYLE_TRANSFER
                || producer_info.nn_info.nb_info.model_type == E_NN_MODE_SEGMENTATION) {
            nn_vpp_rgn_destroy(producer_info.nn_info.disp_rgn_handle, producer_info.vpp_grpid, producer_info.vpp_chnid_vo);
        }
    //exit2:
    //    nn_producer_pipeline_stop(&producer_info);
    exit1:
        sample_common_nn_destroy(&producer_info.nn_info);
        sample_common_nn_destroy(&producer_info.nn_info_track);
    exit0:
        sample_common_nn_exit();
    }
#endif

    stop_laser_range();
    //PRINTLINE 
    return ret;
}   
