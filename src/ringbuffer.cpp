#ifdef DUMP_USING_RINGBUFFER
#include <stdio.h>
#include <stdlib.h>
#include <cstring>
#include <stdint.h>
#include <pthread.h>
#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"

#include <mutex>
#include <iostream>
#include <thread>

std::mutex mtx;


#define MAGIC_HEADER (0xAA55BB66)
#define MAGIC_FOOTER (0x55AA66BB)

typedef struct infra_dump_data_header {
    int magicHeader = MAGIC_HEADER;
    char filename[256];
    int datalen;
    void * ptrBuf;
    int magicFooter = MAGIC_FOOTER;
} infra_dump_data_header_s;

static int TotalWrite=0;
static int TotalRead=0;

#define MIN(a, b) (a) > (b) ? (b) : (a)

#define ENABLE_WRITE_OUT_FILE (1) 
//循环buffer结构体
typedef struct ringbuffer_t
{
    int (*readelem)(struct ringbuffer_t *rb, infra_dump_data_header_s *elem);    //读数据函数指针
    int (*writeelem)(struct ringbuffer_t *rb, infra_dump_data_header_s *elem);   //写数据函数指针
    int (*read)(struct ringbuffer_t *rb, uint8_t *buffer, int len);    //读数据函数指针
    int (*write)(struct ringbuffer_t *rb, uint8_t *buffer, int len);   //写数据函数指针
    uint8_t     *buffer;                                        //缓冲区
    int         buffer_size;                                    //缓冲区大小
    uint8_t     is_init;                                        //是否初始化
    volatile    int rpos;                                       //读指针
    volatile    int wpos;                                       //写指针
}ringbuffer_t;

int is_running = 0;
/**
 * @brief:向循环buffer中写入数据
 * @rb: 输入的循环buffer结构体
 * @buffer:写入循环buffer的数据
 * @len:要写入的长度
 * @return: -1:失败 其他:写入的长度
*/
int write_ringbuffer (ringbuffer_t *rb, uint8_t *buffer, int len)
{
    mtx.lock();
    int left_len = 0;
    volatile int buf_size = rb->buffer_size;
    int wpos = rb->wpos;
    int rpos = rb->rpos;
    int available_len = ((rpos-wpos)+buf_size)&(buf_size-1);
 

    //usleep(100);
    airvisen_trace("Write: wpos:%d, rpos:%d, available_len:%d\n", wpos, rpos, available_len);
    
    if(available_len <= len){
        if(!rb->is_init){
            rb->is_init = 1;
            goto run;
        }
        //
        usleep(50);
        mtx.unlock();
        return -1;
    }

run:
    left_len = MIN(len, buf_size-wpos);
    memcpy(rb->buffer+wpos, buffer, left_len);
    memcpy(rb->buffer, buffer+left_len, len-left_len);

    rb->wpos += len;
    rb->wpos &= (buf_size-1);

    //

    TotalWrite += len;
    mtx.unlock();
    return len;
}

int write_elem_to_ringbuffer (ringbuffer_t *rb, infra_dump_data_header_s *elem)
{
    mtx.lock();
    int left_len = 0;
    volatile int buf_size = rb->buffer_size;
    int wpos = rb->wpos;
    int rpos = rb->rpos;
    int available_len = ((rpos-wpos)+buf_size)&(buf_size-1);
 
    //usleep(100);
    //printf("Write: wpos:%d, rpos:%d, available_len:%d\n", wpos, rpos, available_len);
    
    int len = sizeof(infra_dump_data_header_s);
    if(available_len <= len){
        if(!rb->is_init){
            rb->is_init = 1;
            goto run;
        }
        //
        //usleep(50);
        mtx.unlock();
        return -1;
    }

run:
    left_len = MIN(len, buf_size-wpos);
    memcpy(rb->buffer+wpos, elem, left_len);
    memcpy(rb->buffer, elem+left_len, len-left_len);

    rb->wpos += len;
    rb->wpos &= (buf_size-1);

    //

    TotalWrite += len;
    mtx.unlock();
    return len;
}
/**
 * @brief:从循环buffer中读取数据
 * @rb: 输入的循环buffer结构体
 * @buffer:存储读取到的数据buffer
 * @len:要读取的长度
 * @return: -1:失败 其他:读取到的长度
*/
int read_ringbuffer (ringbuffer_t *rb, uint8_t *buffer, int len)
{
    mtx.lock();
    int left_len = 0;
    int buf_size = rb->buffer_size;
    int wpos = rb->wpos;
    int rpos = rb->rpos;
    int available_len = ((wpos-rpos)+buf_size)&(buf_size-1);
    
    //sleep(1);
    airvisen_trace("Read: wpos:%d, rpos:%d, available_len:%d\n", wpos, rpos, available_len);
    if(available_len <= len) 
    {
        //
        //usleep(50);
        mtx.unlock();
        return -1;
    }

    left_len = MIN(len, buf_size-rpos);
    memcpy(buffer, rb->buffer+rpos, left_len);
    memcpy(buffer+left_len, rb->buffer, len-left_len);

    rb->rpos += len;
    rb->rpos &= (buf_size-1);

    //
    //printf("len:%d\n", len);
    TotalRead += len;
    mtx.unlock();
    
    return len;
}

int read_elem_from_ringbuffer (ringbuffer_t *rb, infra_dump_data_header_s *elem)
{
    mtx.lock();
    int left_len = 0;
    int buf_size = rb->buffer_size;
    int wpos = rb->wpos;
    int rpos = rb->rpos;
    int available_len = ((wpos-rpos)+buf_size)&(buf_size-1);
    
    //sleep(1);
    //printf("Read: wpos:%d, rpos:%d, available_len:%d\n", wpos, rpos, available_len);

    int len = sizeof(infra_dump_data_header_s);

    if(available_len <= len) 
    {
        //
        //usleep(50);
        mtx.unlock();
        return -1;
    }

    left_len = MIN(len, buf_size-rpos);
    memcpy(elem, rb->buffer+rpos, left_len);
    memcpy(elem+left_len, rb->buffer, len-left_len);

    rb->rpos += len;
    rb->rpos &= (buf_size-1);

    //
    //printf("len:%d\n", len);
    TotalRead += len;
    mtx.unlock();
    
    return len;
}

void* write_data_task(void* args)               //线程1 往循环buffer中写数据
{
    FILE *fp = fopen("test.dat", "rb");
    if(!fp){
        printf("open src.dat error!\n");
        is_running=0;
        return NULL;
    }
    
    uint8_t buf[640 * 512]={0};
    ringbuffer_t *rb = (ringbuffer_t*)args;     //获取传入进来的循环buffer参数
    
    int ret = -1;
    int data_len = 0;

    while(!feof(fp))
    {

        //data_len = 512+rand()%512;              //获取随机长度写入循环buffer
        data_len = 640 * 512;              //获取随机长度写入循环buffer
        int nCount = fread(buf, 1, data_len,  fp);            //根据长度从文件中读出原始数据写入循环buffer
        airvisen_trace("nCount:%d\n", nCount);
        if (nCount != data_len)
        {
            
                break;
        }
        do{
            //printf("data_len:%d\n", data_len); 
            ret = rb->write(rb, buf, data_len); //往循环buffer中写数据
        }while(ret == -1);                      //阻塞等待写入成功

        //
        usleep(30);
        //
    }
    printf("EOF!!!\n");
//    is_running=0;

    
    
    fclose(fp);
    
    return NULL;
}


void* read_data_task(void* args)
{
    #if ENABLE_WRITE_OUT_FILE
    FILE *fp = fopen("./dataout.dat", "wb");

    if(!fp){
        printf("Open out.dat error! \n");
        is_running=0;
        return NULL;
    }
    #endif
    int ret = -1;
    ringbuffer_t *rb = (ringbuffer_t*)args;     //获取传入进来的循环buffer参数
    uint8_t buf[640 * 512]={0};
    
    //long start_time = get_sys_time();           //获取系统时间
    WALLTIMETRACKING
    int data_len=0;
    while (is_running)
    {
        
        //data_len = 512+rand()%512;              //获取随机长度从循环buffer中读取数据
        data_len = 640 * 512;              //获取随机长度从循环buffer中读取数据
        do{
            ret = rb->read(rb, buf, data_len);  //从循环buffer中读数据
            
            usleep(100);
        }while(ret==-1);                        //阻塞等待读取数据成功

        #if ENABLE_WRITE_OUT_FILE
        printf("write to output file ...\n");
        fwrite(buf, 1, data_len,  fp);           //将从循环buffer中读取的数据
        #endif
        //sleep(1);
    }
    WALLTIMESTAT("Read Data") 
    //long end_time = get_sys_time();             //获取系统时间
    //int use_time = end_time-start_time;
    //double use_s = ((double)use_time/1000000.0);
    //double rate = ((500*1024*1024*8.0)/use_s)/(1024*1024*1024.0);
    //printf("500M Data Use time=%dus(%.2lfS) rate=%.2lfGbps\n", use_time, use_s, rate);

    
    #if ENABLE_WRITE_OUT_FILE
    fclose(fp);
    #endif
    
    return NULL;
}
#ifndef MAIN_TEST_FUNC

extern int dumpToFile(char * ptrFileName, void *ptrData, unsigned int nDataLen);


void* dump_data_task(void* args)
{
    pthread_setname_np(pthread_self(), "dumpdatatask");
    int ret = -1;
    ringbuffer_t *rb = (ringbuffer_t*)args;     //获取传入进来的循环buffer参数
    
    int data_len=0;
    infra_dump_data_header_s elem;
    while (is_running)
    {
        
        do{
            ret = rb->readelem(rb, &elem);  //从循环buffer中读数据
            
            usleep(100);
        }while(ret==-1);                        //阻塞等待读取数据成功

        if(elem.magicHeader != MAGIC_HEADER || elem.magicFooter != MAGIC_FOOTER)
        {
            printf("ERROR! data corrupt may occurred!\n");
        } 

        airvisen_trace("dumpToFile: %s, elem.datalen:%d\n", elem.filename, elem.datalen);
        airvisen_trace("dumpToFile, elem.ptrBuf = %p\n", elem.ptrBuf);
        dumpToFile(elem.filename, elem.ptrBuf, elem.datalen);
        if (elem.ptrBuf != NULL)
        {
            free(elem.ptrBuf);
            elem.ptrBuf = NULL;
        }

    }

    return NULL;
}
static ringbuffer_t rb;
int initialize_ringbuffer()
{
   pthread_t tx, rx;

   memset(&rb, 0, sizeof(ringbuffer_t));

   rb.readelem = read_elem_from_ringbuffer;
   rb.writeelem = write_elem_to_ringbuffer;
   rb.buffer_size = 1024 * 1024 * 32; 
   rb.buffer = (uint8_t*)(malloc(rb.buffer_size));

   is_running = 1;

   //pthread_create(&tx, 0, write_data_task, &rb);
   //pthread_create(&rx, 0, read_data_task, &rb);
   pthread_create(&rx, 0, dump_data_task, &rb);
   
   return 0;
}


int dumpToRingBuffer(void *ptr, int datalen, char* filename)
{
    int ret = -1;
    infra_dump_data_header_s elem;

    elem.ptrBuf = malloc(datalen);
    if (elem.ptrBuf == NULL)
    {
        printf("ERROR! in dumpToRingBuffer for malloc mem\n");
        return -1;
    }

    airvisen_trace("dumpToRingBuffer, elem.ptrBuf = %p\n", elem.ptrBuf);

    memcpy(elem.filename, filename, 256);
    memcpy(elem.ptrBuf, ptr, datalen); 
    elem.datalen = datalen;
    airvisen_trace("dumpToRingBuffer, elem.filename = %s, datalen:%d\n", elem.filename, datalen);
    
    int loopIndex = 0; 
    do
    {
        loopIndex += 1;
        ret = rb.writeelem(&rb, &elem); 
        usleep(100);
    }while (ret == -1 && loopIndex < 5);
    return ret;
}

int destory_ringbuffer()
{
   is_running = 0;
   free(rb.buffer);
   airvisen_trace("TotalWrite:%d, TotalRead:%d, diff = %d\n", TotalWrite, TotalRead, TotalWrite - TotalRead);
   return 0;
}
#else
int g_traceinfo = 1;
int main(int argc, char* argv[])
{
   set_traceinfo(1); 

   pthread_t tx, rx;
   ringbuffer_t rb;
   memset(&rb, 0, sizeof(ringbuffer_t));

   rb.read = read_ringbuffer;
   rb.write = write_ringbuffer;
   rb.buffer_size = 1024 * 1024 * 32; 
   rb.buffer = (uint8_t*)(malloc(rb.buffer_size));

   is_running = 1;

   
   pthread_create(&tx, 0, write_data_task, &rb);
   pthread_create(&rx, 0, read_data_task, &rb);
   
   
   printf("press any key to exit\n");
   
   waitForPause();

   printf("key pressed, will exit\n");
   is_running = 0;
   //
   //pthread_join(tx, 0);
   //
   //pthread_join(rx, 0);
   //
   sleep(1);
   
   free(rb.buffer);
   


   
   airvisen_trace("TotalWrite:%d, TotalRead:%d, diff = %d\n", TotalWrite, TotalRead, TotalWrite - TotalRead);
   
   return 0;
}
#endif
#endif
