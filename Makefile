PROJECT_HOME = ./
BIN_DIR := bin
TARGET_DIR := /opt/nfs3/
OPENCV_INC_PATH=${PROJECT_HOME}/include/opencv
OPENCV_LIB_PATH=${PROJECT_HOME}/lib/opencv
NN_INC_PATH=${PROJECT_HOME}/vs839_libs/include
NN_LIB_PATH=${PROJECT_HOME}/vs839_libs/lib/nn
VS_LIB_PATH=${PROJECT_HOME}/vs839_libs/lib
# NN_LIB_PATH=/opt/nfs3/M1_lib/
EIGEN_PATH=${PROJECT_HOME}/bytetrack/eigen3
CXX =aarch64-linux-gnu-g++
CC =aarch64-linux-gnu-gcc

MP_CFLAGS += -DVS_CARINA
# MP_CFLAGS += -DDEBUG_ENABLED
MP_CFLAGS+=-DGIT_SHA1="$(shell git log --format='[sha1]:%h [author]:%cn [time]:%ci [commit]:%s [branch]:%d' -1)"
MP_CFLAGS+=-DGIT_DESC="$(shell git describe --tags --always --dirty)"

#MODE, QL64017_VII | UVC_640 | UVC_256 | TRACKER | DUAL_LENSES + HDMI | RTSP
# MODE ?= UVC_256_HDMI
# MODE ?= UVC_384_HDMI
# MODE ?= UVC_640_HDMI
# MODE ?= UVC_640_RTSP
# MODE ?= QL64017_VII_HDMI
# MODE ?= QL64017_VII_RTSP
# MODE ?= TRACKER_RGBUVC_RTSP # RGB UVC + INFRA UVC
# MODE ?= TRACKER_RGBUVC_HDMI # RGB UVC + INFRA UVC
# MODE ?= TRACKER_RGBVII_RTSP # RGB VII + INFRA UVC
#MODE ?= TRACKER_RGBVII_RTSP # RGB VII + INFRA UVC
# MODE ?= TRACKER_RGBVII_RTSP_GPIO # RGB VII + INFRA UVC
# MODE ?= TRACKER_RGBVII_CVBS_GPIO # RGB VII + CVBS VO
# MODE ?= TRACKER_RGBVII_CVBS_GPIO # RGB VII + CVBS VO
# MODE ?= TRACKER_RGBVII_HDMI # RGB VII + INFRA UVC
# MODE ?= TRACKER_RGBVII_HDMI_GPIO # RGB VII + INFRA UVC
MODE ?= TRACKER_RGBVII_RTSP_DUAL # RGB VII + INFRA UVC

ifneq (,$(findstring DUAL, $(MODE)))
	MP_CFLAGS += -DDUAL_CAM
endif

ifneq (,$(findstring QL64017_VII,$(MODE)))
	BIN = ${PROJECT_HOME}/${BIN_DIR}/irqlw64017_ai_demo
	MP_CFLAGS += -DSHUTTER_AVAILABLE
	MP_CFLAGS += -DQLW_38B_VERSION=12
	#MP_CFLAGS += -DVII_VIRT_FROM_RAW_SEQUENCE
	#MP_CFLAGS += -DVII_VIRT_FROM_RAW_SEQUENCE_SUB_FOLDER_SEARCHING
	MP_CFLAGS += -DINFRA_VII_SRC_FRM_USE_CACHED_MEM
endif

ifneq (,$(findstring DUAL_LENSES, $(MODE)))
	BIN = ${PROJECT_HOME}/${BIN_DIR}/irqlw64017_os04a10_dual_ai_demo
	MP_CFLAGS += -DDUAL_LENSES
	MP_CFLAGS += -DVO_FORCE_CENTER_MODE
	#MP_CFLAGS += -DFORCE_VO_ONE_CHN_MODE
	#MP_CFLAGS += -DASPECT_RATIO_16_9
endif

ifneq (,$(findstring UVC_640,$(MODE)))
	BIN = ${PROJECT_HOME}/${BIN_DIR}/irqlw_uvc640_ai_demo
	MP_CFLAGS += -DVII_FROM_UVC
	MP_CFLAGS += -DVII_FROM_UVC_640
	MP_CFLAGS += -DVO_FORCE_CENTER_MODE
	MP_CFLAGS += -DSEND_DETECT_RESULT_VIA_SERIAL
	MP_CFLAGS += -DSERIAL_COMM_ENABLED
#	MP_CFLAGS += -DAIRVISEN_RGN_ENABLED
#	MP_CFLAGS += -DAIRVISEN_RGN_LOGO_OVERLAYEX_ENABLED
	#MP_CFLAGS += -DVII_FROM_UVC_640_SR_COMP
	#MP_CFLAGS += -DUVC_PIC_SEQUENCE
endif

ifneq (,$(findstring UVC_256,$(MODE)))
	BIN = ${PROJECT_HOME}/${BIN_DIR}/irqlw_uvc256_ai_demo
	MP_CFLAGS += -DVII_FROM_UVC
	MP_CFLAGS  += -DVII_FROM_UVC_256
	MP_CFLAGS += -DVO_FORCE_CENTER_MODE
	MP_CFLAGS  += -DVII_FROM_UVC_256_SR_COMP
endif

ifneq (,$(findstring VII_FROM_UVC_256_SR_COMP,$(MP_CFLAGS)))
	BIN := $(BIN)_4mux
endif

ifneq (,$(findstring UVC_384,$(MODE)))
	BIN = ${PROJECT_HOME}/${BIN_DIR}/irqlw_uvc384_ai_demo
	MP_CFLAGS += -DVII_FROM_UVC
	MP_CFLAGS  += -DVII_FROM_UVC_384
	MP_CFLAGS += -DVO_FORCE_CENTER_MODE
	MP_CFLAGS  += -DVII_FROM_UVC_384_SR_COMP
	MP_CFLAGS  += -DGZ_384_HANDLING
endif
ifneq (,$(findstring VII_FROM_UVC_384_SR_COMP,$(MP_CFLAGS)))
	BIN := $(BIN)_4mux
endif

ifneq (,$(findstring TRACKER,$(MODE)))
	BIN = ${PROJECT_HOME}/${BIN_DIR}/tracker_app
	MP_CFLAGS += -DTRACKER_ENABLED
	MP_CFLAGS += -DSERIAL_COMM_ENABLED
	MP_CFLAGS += -DTRACKING_BUF_USE_CACHED_MEM
	#MP_CFLAGS += -DONLY_RGB_4_DEBUG
	MP_CFLAGS += -DFORCE_VO_ONE_CHN_MODE
	MP_CFLAGS += -DIR_WIDTH=640
	MP_CFLAGS += -DIR_HEIGHT=512
	MP_CFLAGS += -DDISP_WIDTH=1920
	MP_CFLAGS += -DDISP_HEIGHT=1080
# MP_CFLAGS += -DTRACKER_DRAW_DETECT
#	MP_CFLAGS += -DSTORE_TRACK_DRAW_FRAME
	MP_CFLAGS += -DSHOW_SEARCHING_AREA
# 0:normal MODE; 1: debug mode with save detection boxes overlay on jpg
# MP_CFLAGS += -DINFERENCE_SAVED_JPEG_WITH_YOLO_MODE=1
#	MP_CFLAGS += -DIR_CMD_PASS_THROUGH
# auto trigger tracking proc after 500 frames  
# MP_CFLAGS += -DAUTO_TRIGGER_WITHOUT_GPIO_BUTTON
	MP_CFLAGS += -DRGB_4K_TRACKING_ENABLED
	MP_CFLAGS += -DAUTO_SWITCH_PROTOCOL
	
	MP_CFLAGS += -DHEQ_PROTOCOL_TYPE=0
	MP_CFLAGS += -DMOUNTMASTER_PROTOCOL_TYPE=1

#	MP_CFLAGS += -DENABLE_INFRA_PIP_USE_WBC
	
ifneq (,$(findstring GPIO,$(MODE)))
    BIN := $(BIN)_gpio
	MP_CFLAGS += -DGPIO_TRIGGER_MODE
ifneq (,$(findstring CVBS,$(MODE)))
	BIN := $(BIN)_cvbs
	MP_CFLAGS += -DCVBS_WIDTH=736
	MP_CFLAGS += -DCVBS_VO_ENABLED
	MP_CFLAGS += -DCVBS_HEIGHT=576
	MP_CFLAGS += -DAIRVISEN_RGN_ENABLED
	MP_CFLAGS += -DAIRVISEN_RGN_OVERLAYEX_ENABLED
endif
ifneq (,$(findstring HDMI,$(MODE)))
	BIN := $(BIN)_hdmi 
	MP_CFLAGS += -DAIRVISEN_RGN_ENABLED
	MP_CFLAGS += -DAIRVISEN_RGN_OVERLAYEX_ENABLED
	MP_CFLAGS += -DDRAW_FRONT_SIGHT_ENABLED
endif
endif


ifneq (,$(findstring RGBUVC,$(MODE)))
	MP_CFLAGS += -DVII_FROM_UVC
	MP_CFLAGS += -DVII_FROM_UVC_RGB
	MP_CFLAGS += -DTRACKING_BUF_USE_CACHED_MEM
	MP_CFLAGS += -DVII_VIRT_FROM_RAW_SEQUENCE
	MP_CFLAGS += -DVII_VIRT_FROM_RAW_SEQUENCE_SUB_FOLDER_SEARCHING
	MP_CFLAGS += -DUVC_PIC_SEQUENCE
	MP_CFLAGS += -DUVC_PIC_VO_FPS=30
	#MP_CFLAGS += -DC505E_FORCE_640
	ifneq (,$(findstring C505E_FORCE_640,$(MODE)))
		MP_CFLAGS += -DRGB_WIDTH=640
		MP_CFLAGS += -DRGB_HEIGHT=360
	else
		MP_CFLAGS += -DRGB_WIDTH=1920
		MP_CFLAGS += -DRGB_HEIGHT=1080
	endif
else

ifneq (,$(findstring RGB_4K_TRACKING_ENABLED,$(MP_CFLAGS)))
	MP_CFLAGS += -DRGB_WIDTH=3840
	MP_CFLAGS += -DRGB_HEIGHT=2160
else
	MP_CFLAGS += -DRGB_WIDTH=1920
	MP_CFLAGS += -DRGB_HEIGHT=1080
endif

	MP_CFLAGS += -DVENC_CHN_WIDTH=1920
	MP_CFLAGS += -DVENC_CHN_HEIGHT=1080

	MP_CFLAGS += -DPROTOCOL_WIDTH=1920
	MP_CFLAGS += -DPROTOCOL_HEIGHT=1080

	MP_CFLAGS += -DMOUNTMASTER_WIDTH=1920
	MP_CFLAGS += -DMOUNTMASTER_HEIGHT=1080
	
endif

endif

ifneq (,$(findstring RTSP,$(MODE)))
	BIN := $(BIN)_rtsp
	MP_CFLAGS += -DSOFT_PIP_ENABLED
	MP_CFLAGS += -DPIP_MODE3_ENABLED
	MP_CFLAGS += -DRTSP_ENABLED
	MP_CFLAGS += -DAIRVISEN_VENC_BITRATE_FORCE_3M
	#MP_CFLAGS += -DH265_VENC_ENABLED
	#MP_CFLAGS += -DVENC_COLOR2GRE_MODE_MONO
	MP_CFLAGS += -DTRACKER_DRAW_DETECT
	MP_CFLAGS += -DDRAW_RGB_ZOOM_FACTOR
ifneq (,$(findstring TRACKER,$(MODE)))
	MP_CFLAGS += -DAIRVISEN_RGN_ENABLED
#   MP_CFLAGS += -DAIRVISEN_TRACKER_RGN_OVERLAY_ENABLED
	MP_CFLAGS += -DAIRVISEN_TRACKER_DRAW_CHAR_ENABLED
# 1: 24x29 bitmap char; 0: 10x14 bitmap char;
	MP_CFLAGS += -DAIRVISEN_TRACKER_DRAW_CHAR_SIZE=1
	MP_CFLAGS += -DHEQI_RGN_OVERLAY
	MP_CFLAGS += -DHEQI_PROTICOL
endif
else
    # Not found
endif


## npu part(NN)
MP_CFLAGS += -DNN_ENABLED 
MP_CFLAGS += -DADD_ID_PTS_IN_NN_POST_RESULT_S
# denoise
MP_CFLAGS += -DLIYI_AI_DENOISE_NEW_PIPELINE
MP_CFLAGS += -DDENOISE_OUTPUT_BUF_USE_CACHED_MEM

ifneq (,$(findstring VII_FROM_UVC, $(MP_CFLAGS))) #UVC
	#MP_CFLAGS += -DNR_MODEL_OUTPUT_WITHOUT_CAST_TO_U8 
else
    # ai direct output
	MP_CFLAGS += -DNR_MODEL_OUTPUT_WITHOUT_CAST_TO_U8 
endif

#sr
MP_CFLAGS += -DLIYI_AI_SR
#MP_CFLAGS += -DSR_MODEL_OUTPUT_WITHOUT_CAST_TO_U8


#heq heartbeat command control
MP_CFLAGS += -DHEQ_HEARTBEAT_CONTROL_ENABLED

#yolov5/8 detection

#laser DIST osd
MP_CFLAGS += -DLASER_DIST_OSD_ENABLED

MP_CFLAGS += -DYOLOV8_ENABLED
ifneq (,$(findstring DYOLOV8_ENABLED,$(MP_CFLAGS)))
ifneq (,$(findstring DTRACKER_ENABLED,$(MP_CFLAGS)))
	MP_CFLAGS += -DNN_CHN_WIDTH=960
	MP_CFLAGS += -DNN_CHN_HEIGHT=512
	MP_CFLAGS += -DANCHOR_TYPE=11
	MP_CFLAGS += -DYOLO_CLS_NUM=4
	MP_CFLAGS += -DYOLOV8_REG_MAX_EQ=16
#if model_name has r8, then set YOLOV8_REG_MAX=8, else set YOLOV8_REG_MAX=4
	MP_CFLAGS += -DYOLOV8_REG_MAX=8
	#MP_CFLAGS += -DSIGMOID_IN_NB
# MP_CFLAGS += -DLT_FDSST_TRACKER
	MP_CFLAGS += -DNANOTRACK
	# MP_CFLAGS += -DBYTE_TRACKER
# MP_CFLAGS += -DPRESSURE_TEST
# MP_CFLAGS += -DPRESSURE_TEST_INSTANCE_NUM=80
	MP_CFLAGS += -DDETECTION_FILTER_BIGTARGET
	MP_CFLAGS += -DSHOW_DETECTION
	MP_CFLAGS += -DHEQI_XY
else
	MP_CFLAGS += -DNN_CHN_WIDTH=640
	MP_CFLAGS += -DNN_CHN_HEIGHT=512
	MP_CFLAGS += -DYOLO_CLS_NUM=3
	MP_CFLAGS += -DYOLOV8_REG_MAX_EQ=16
#if model_name has r8, then set YOLOV8_REG_MAX=8, else set YOLOV8_REG_MAX=4
	MP_CFLAGS += -DYOLOV8_REG_MAX=8
	# MP_CFLAGS += -DBYTE_TRACKER
endif #< TRACKER_ENABLED 
ifneq (,$(findstring UVC_384,$(MODE)))
	MP_CFLAGS += -DNN_CHN_WIDTH=384
	MP_CFLAGS += -DNN_CHN_HEIGHT=288
	MP_CFLAGS += -DANCHOR_TYPE=8
	MP_CFLAGS += -DYOLO_CLS_NUM=3
endif
else # // YOLOV8
	MP_CFLAGS += -DNN_CHN_WIDTH=320
	MP_CFLAGS += -DNN_CHN_HEIGHT=256
	MP_CFLAGS += -DANCHOR_TYPE=8
	MP_CFLAGS += -DYOLO_CLS_NUM=3
	#MP_CFLAGS += -DSIGMOID_IN_NB
endif

ifneq (,$(findstring DBYTE_TRACKER,$(MP_CFLAGS)))
	MP_CFLAGS += -DSHOW_AFTER_LOST
	MP_CFLAGS += -DBYTE_XYWH
	MP_CFLAGS += -DBYTETRACK_SHORTED
endif

ifneq (,$(findstring DNANOTRACK,$(MP_CFLAGS)))
	MP_CFLAGS += -DNANOTRACK_KF
	MP_CFLAGS += -DNANO_WO_FINDING
	MP_CFLAGS += -DNANO_WO_DETECTION
endif

#gpe related
MP_CFLAGS += -DGPE_RECT_FORCE_DRAW   #will enlarge the box_w, box_h to 32, 32 which is the MIN draw rect size of GPE

# vo related
#MP_CFLAGS += -DQUICK_SWITCH_VO

#debug
#MP_CFLAGS += -DDUMP_USING_RINGBUFFER

#enable encryption
#MP_CFLAGS += -DENCRYPT_MODEL_ENABLE

#MP_CFLAGS += -DUSE_MK1001_LASER_RANGE

#DISABLE all log
#MP_CFLAGS += -DDISABLE_ALL_LOGS
MP_CFLAGS += -DLOG_LEVEL=LOG_LEVEL_ERROR

#USE 640T PROTOCOL
#MP_CFLAGS += -DHQ640T_PROTOCOL
MP_CFLAGS += -DK40T_PROTOCOL_ENABLED

VPATH := ./common: ./src: ./include

SOURCES = $(wildcard *.c)
SOURCES += $(wildcard ./ipc/*.c)
SOURCES += $(wildcard ./ipc/store/*.c)
SOURCES += $(wildcard ./ipc/video/*.c)
SOURCES += $(wildcard ./ipc/algo/*.c)
SOURCES += $(wildcard ./ipc/osd/*.c)
SOURCES += $(wildcard ./ipc/infra/*.c)
SOURCES += $(wildcard ./ipc/webserver/*.c)
SOURCES += $(wildcard ./common/*.c)
SOURCES += $(wildcard ./src/*.c)
#SOURCES += $(wildcard ./uvc_host/*.c)
SOURCES += $(wildcard ./venc/*.c)
SOURCES += $(wildcard ./tracker/*.c)
SOURCES += $(wildcard ./tracker/SDSST/*.c)
SOURCES += $(wildcard ./serial/*.c)
SOURCES += $(wildcard ./message/*.c)
SOURCES += $(wildcard ./protocol/*.c)
SOURCES += $(wildcard ./tcpserver/*.c)


ifneq (,$(findstring SHUTTER_AVAILABLE, $(MP_CFLAGS)))
SOURCES += $(wildcard ./shutter/*.c)
endif

ifneq (,$(findstring AIRVISEN_RGN_ENABLED, $(MP_CFLAGS)))
ifneq (,$(findstring HEQI_RGN_OVERLAY, $(MP_CFLAGS)))
	SOURCES += $(wildcard ./heqrgn/*.c)
else
	SOURCES += $(wildcard ./rgn/*.c)
endif
endif


ifneq (,$(findstring AIRVISEN_TRACKER_DRAW_CHAR_ENABLED, $(MP_CFLAGS)))
SOURCES += $(wildcard ./drawchar/*.c)
endif


OBJS = $(SOURCES:.c=.o)

CXX_SOURCES = $(wildcard *.cpp)
CXX_SOURCES += $(wildcard ./src/*.cpp)
CXX_SOURCES += $(wildcard ./bytetrack/src/*.cpp)
CXX_SOURCES += $(wildcard ./web/*.cpp)
CXX_SOURCES += $(wildcard ./laser/*.cpp)

ifneq (,$(findstring DTRACKER_ENABLED, $(MP_CFLAGS)))
CXX_SOURCES += $(wildcard ./tracker/*.cpp)
CXX_SOURCES += $(wildcard ./tracker/single_tracker/nanotrack/*.cpp)
CXX_SOURCES += $(wildcard ./tracker/single_tracker/FDSST/*.cpp)
endif

ifneq (,$(findstring DSERIAL_COMM_ENABLED, $(MP_CFLAGS)))
CXX_SOURCES += $(wildcard ./serial/*.cpp)
SOURCES += $(wildcard ./serial/*.c)
endif

OBJS_CXX = $(CXX_SOURCES:.cpp=.o)


ifneq (,$(findstring DDEBUG_ENABLED,$(MP_CFLAGS)))
OPT_CFLAGS = O0
OPT_CXXFLAGS = O0
else
OPT_CFLAGS = Os
OPT_CXXFLAGS = O3
endif

CFLAGS += -Wall -${OPT_CFLAGS} -c -Wno-unused-function -fstack-protector-strong -fPIC
CFLAGS += -I${PROJECT_HOME}/include

CXXFLAGS = -std=c++17 -${OPT_CXXFLAGS} -I${PROJECT_HOME}/include \
	-I${PROJECT_HOME}/bytetrack/include \
	-I${OPENCV_INC_PATH}/include/opencv4 \
	-I${EIGEN_PATH} \
	-I${NN_INC_PATH}


ifneq (,$(findstring AIRVISEN_RGN_ENABLED, $(MP_CFLAGS)))
ifneq (,$(findstring HEQI_RGN_OVERLAY, $(MP_CFLAGS)))
	CXXFLAGS += -I${PROJECT_HOME}/heqrgn
else
	CXXFLAGS += -I${PROJECT_HOME}/rgn
endif
endif

CXXFLAGS += -I${PROJECT_HOME}/drawchar

SENSOR0_TYPE ?= SONY_IMX334_MIPI_8M_60FPS_12BIT
SENSOR1_TYPE ?= OV_OS04A10_MIPI_4M_30FPS_10BIT
SENSOR2_TYPE ?= AIRVISEN_QL64017_INFRARED_330K_60FPS_14BIT 
SENSOR3_TYPE ?= OV_OS04A10_MIPI_4M_30FPS_10BIT

MP_CFLAGS += -DSENSOR0_TYPE=$(SENSOR0_TYPE)
MP_CFLAGS += -DSENSOR1_TYPE=$(SENSOR1_TYPE)
MP_CFLAGS += -DSENSOR2_TYPE=$(SENSOR2_TYPE)
MP_CFLAGS += -DSENSOR3_TYPE=$(SENSOR3_TYPE)

LIBFLAGS = \
-L${NN_LIB_PATH}/ -lVIPuser -lVIPlite \
-L${VS_LIB_PATH}/ -lnn -lbase \
-lvii -lisp -lmipirx -lvpp -lgdc -lrgn -lvo -lmipitx -lhdmi \
-lgpe -lGAL -lsys -linit -ldsp \
-L${OPENCV_LIB_PATH}/lib -lopencv_world \
-lpthread -lm -lstdc++ -ldl

LIBFLAGS += -L./lib -ldde -lnuc -lconfigparser -lnanomsg

LDFLAGS = \
	-L${NN_LIB_PATH}/ -lVIPuser -lVIPlite \
	-L${VS_LIB_PATH}/ -lnn -lbase \
	-lvii -lisp -lmipirx -lvpp -lgdc -lrgn -lvo -lmipitx -lhdmi \
	-lgpe -lGAL -lsys -linit -ldsp \
	-live  -lvdec -lvenc \
	-L${OPENCV_LIB_PATH}/lib -lopencv_world \
	-lpthread -lm -lstdc++ -ldl
LDFLAGS += -L./lib -ldde -lnuc -lconfigparser -lnanomsg -lcipher

#LDFLAGS += -fopenmp

CFLAGS += -Wall -Ofast -c -Wno-unused-function -fstack-protector-strong -fPIC -c
#CFLAGS += -I /opt/opencv/include/opencv4
# CFLAGS += -I ${OPENCV_INC_PATH}/include/opencv4
CFLAGS += -I${PROJECT_HOME}/include \
	-I${PROJECT_HOME}/bytetrack/include \
	-I${OPENCV_INC_PATH}/include/opencv4 \
	-I${EIGEN_PATH} \
	-I${NN_INC_PATH}
#CFLAGS += -fopenmp

#CFLAGS += -I${PROJECT_HOME}/uvc_host
CFLAGS += -I${PROJECT_HOME}/tracker
# CFLAGS += -I${PROJECT_HOME}/nanomsg
CFLAGS += -I./ipc -I./ipc/algo
CFLAGS += -I./ipc -I./ipc/store
CFLAGS += -I./ipc -I./ipc/osd
CFLAGS += -I./ipc -I./ipc/video
CFLAGS += -I./ipc -I./ipc/infra
CFLAGS += -I./ipc -I./ipc/webserver
CFLAGS += -I./ipc -I./web
CFLAGS += -I./laser
CFLAGS += -I./serial
CFLAGS += -I./protocol
CFLAGS += -I./message
CFLAGS += -I./tcpserver
CFLAGS += -I./mavlink/common

#CXXFLAGS += -I${PROJECT_HOME}/uvc_host
# CXXFLAGS += -I${PROJECT_HOME}/nanomsg
CXXFLAGS += -I./ipc -I./ipc/algo
CXXFLAGS += -I./ipc -I./ipc/store
CXXFLAGS += -I./ipc -I./ipc/osd
CXXFLAGS += -I./ipc -I./ipc/video
CXXFLAGS += -I./ipc -I./ipc/infra
CXXFLAGS += -I./ipc -I./ipc/webserver
CXXFLAGS += -I./ipc -I./web
CXXFLAGS += -I./laser
CXXFLAGS += -I./serial
CXXFLAGS += -I./protocol
CXXFLAGS += -I./message
CXXFLAGS += -I./tcpserver
CXXFLAGS += -I./mavlink/common

ifneq (,$(findstring TRACKER,$(MODE)))
CXXFLAGS += -I${PROJECT_HOME}/tracker
CXXFLAGS += -I${PROJECT_HOME}/tracker/SDSST
CXXFLAGS += -I${PROJECT_HOME}/nanotrack
endif

.PHONY: all
all: $(BIN)
ifneq ("$(wildcard $(TARGET_DIR))","")
	cp $(BIN) ${TARGET_DIR} && echo "sync to NFS Done!"
endif

build: $(BIN)

$(BIN): $(OBJS) $(OBJS_CXX)
ifeq ("$(wildcard $(BIN_DIR))","")
	mkdir -p ${BIN_DIR}
endif
	/bin/bash $(PROJECT_HOME)/sensitivepurger/purgesensitiveinfo.sh $^
	$(CXX) $^ $(LDFLAGS) -o $@ 

$(OBJS):%.o:%.c
	#$(CC) $(CFLAGS) -c -o $@ $<
	$(CC) -c $(CFLAGS) $(MP_CFLAGS) $< -o $@
$(OBJS_CXX):%.o:%.cpp
	$(CXX) -c $(CXXFLAGS) $(MP_CFLAGS) $< -o $@

clean:
	rm -f $(BIN) ./src/*.o ./common/*.o $(OBJS) $(OBJS_CXX)

