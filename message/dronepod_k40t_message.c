#include "dronepod_k40t_message.h"
#include <pthread.h>
#include <stdio.h>
#include "serial.h"
#include "dronepod_k40t_protocol.h"
#include <unistd.h>
#include <string.h>
#include "k40t_server.h"
#include "ring_buffer_v2.h"
#include "mavlink.h"
#define LOG_LEVEL LOG_LEVEL_ERROR
#include "log_utils.h"

#define MAX_CALLBACK_ENTRIES 250
#define BUFFER_SIZE 4096


//static char m_buffer[BUFFER_SIZE];
static serial_context_t serial_context = {0};
pthread_mutex_t send_lock = PTHREAD_MUTEX_INITIALIZER;


typedef struct {
    uint32_t cmd_id;
    fpv_callback_t callback;
} fpv_callback_entry_t;

typedef struct {
    uint32_t cmd_id;
    fpv_callback_t callback;;
} fc_callback_entry_t;

static fpv_callback_entry_t fpv_callback_entries[MAX_CALLBACK_ENTRIES];
static fc_callback_entry_t fc_callback_entries[MAX_CALLBACK_ENTRIES];
static size_t fpv_callback_count = 0;
static size_t fc_callback_count = 0;

//thread
pthread_t receive_thread, process_thread, mavlink_thread;


static unsigned char calcCrc8(unsigned char *buffer, int len)
{
    unsigned char sum = 0;
    for (int i = 0; i < len; i++)
    {
        sum += buffer[i];
    }
    return sum;
}


// uint16_t crc16_ccitt(uint8_t *data, size_t length) {
//     uint16_t crc = 0xFFFF; // 初始值
//     for (size_t i = 0; i < length; i++) {
//         crc ^= (uint16_t)data[i] << 8; // 将当前字节与CRC寄存器的高8位进行异或
//         for (uint8_t j = 0; j < 8; j++) {
//             if (crc & 0x8000) { // 如果最高位为1
//                 crc = (crc << 1) ^ 0x1021; // 左移并异或多项式0x1021
//             } else {
//                 crc <<= 1; // 左移
//             }
//         }
//     }
//     return crc;
// }


static void k_crc_accumulate(uint8_t data, uint16_t *crcAccum) {
    uint8_t tmp;
    tmp = data ^ (uint8_t)(*crcAccum & 0xff);
    tmp ^= (tmp << 4);
    *crcAccum = (*crcAccum >> 8) ^ (tmp << 8) ^ (tmp << 3) ^ (tmp >> 4);
}

#define X25_INIT_CRC 0xffff

static void k_crc_init(uint16_t *crcAccum) {
    *crcAccum = X25_INIT_CRC;
}

uint16_t k_crc_calculate(const uint8_t *pBuffer, uint16_t length) {
    uint16_t crcTmp;
    k_crc_init(&crcTmp);
    while (length--) {
        k_crc_accumulate(*pBuffer++, &crcTmp);
    }
    return crcTmp;
}


#if 0
int pop_fpv_command(ring_buffer_t *m_ring_buffer, char *buffer, int buff_length)
{
    char sof;
    uint8_t payload_len_from_header; // LEN 字段的值，即载荷长度
    int total_frame_length;

    do
    {
        // 1. 检查是否有足够的字节形成最小帧头 (STX + LEN = 2字节)
        if (ring_buffer_num_items(m_ring_buffer) < 2) {
            return 0; // 不足以判断
        }

        // 2. 检查STX
        ring_buffer_peek(m_ring_buffer, &sof, 0);
        if (sof != 0xFD) {
            printf("Dirty data: SOF incorrect (0x%02X), dropping 1 byte.\n", sof);
            ring_buffer_dequeue_arr(m_ring_buffer, &sof, 1); // 丢弃一个字节
            continue; // 重新开始循环
        }

        // 3. 读取载荷长度 (LEN 字段)
        ring_buffer_peek(m_ring_buffer, (char*)&payload_len_from_header, 1);

        // K40T_Protocol_Frame_Header_t 中 STX 和 LEN 之后的固定头部长部分为:
        // dest_sys_id (1) + dest_comp_id (1) + seq (1) + src_sys_id (1) + src_comp_id (1) + msg_id (3) = 8字节
        // 完整帧长度 = STX(1) + LEN字段(1) + 固定头(8) + 载荷(payload_len_from_header) + CRC(2)
        //             = 1 + 1 + 8 + payload_len_from_header + 2 = 12 + payload_len_from_header
        total_frame_length = 12 + payload_len_from_header;

        // 4. 检查是否有足够的字节形成完整帧
        if (ring_buffer_num_items(m_ring_buffer) < total_frame_length) {
            printf("Frame data incomplete. Need %d, have %zu\n", total_frame_length, ring_buffer_num_items(m_ring_buffer));
            return 0; // 数据不完整
        }

        // 5. 确保提供的缓冲区足够大
        if (buff_length < total_frame_length) {
            printf("Provided buffer too small. Need %d, have %d\n", total_frame_length, buff_length);
            // 这里应该考虑如何处理，是丢弃数据还是返回错误
            // 暂时丢弃以避免覆盖
            char temp_discard[total_frame_length];
            ring_buffer_dequeue_arr(m_ring_buffer, temp_discard, total_frame_length);
            return 0; // 缓冲区不足
        }
        
        // 6. 读取完整帧数据
        ring_buffer_dequeue_arr(m_ring_buffer, buffer, total_frame_length);

        // 7. CRC校验
        // 校验数据从0XFD之后开始，即从LEN字段开始，到Payload的末尾
        // CRC计算的数据指针: buffer + 1 (跳过STX)
        // CRC计算的数据长度: 1 (LEN字段) + 8 (固定头) + payload_len_from_header
        //                   = 9 + payload_len_from_header
        // 或者也可以是: total_frame_length - 1 (STX) - 2 (CRC) = total_frame_length - 3
        uint16_t calculated_crc = crc_calculate((uint8_t*)buffer + 1, total_frame_length - 3); // 使用文档提供的crc_calculate
        
        uint16_t received_crc = *(uint16_t *)(buffer + total_frame_length - 2); // 小端假设

        if (calculated_crc != received_crc) {
            printf("CRC error! Calculated: 0x%04X, Received: 0x%04X. Frame dropped.\n", calculated_crc, received_crc);
            // print_fuffer_in_Hex((unsigned char*)buffer, total_frame_length); // 打印错误帧以便调试
            return 0; // CRC校验失败
        }
        
        printf("Frame OK. Total length: %d, Payload length: %d\n", total_frame_length, payload_len_from_header);
        return total_frame_length; // 返回读取的帧的总长度

    } while (1); // 如果SOF不对会continue，如果成功或数据不足会return
}
#endif

int pop_fpv_command(ring_buffer_v2_t *rb, char *buffer, int buff_length)
{
    uint8_t sof;
    uint8_t payload_len_from_header; // LEN 字段的值，即载荷长度
    int total_frame_length;
    do
    {
        ring_buffer_v2_wait_for_data(rb);

        // 1. 检查是否有足够的字节形成最小帧头 (STX + LEN = 2字节)
        //printf("rb len : %d\n", rb->count);
        int ret = ring_buffer_v2_get_count(rb);
        if (ret < 2) {
            usleep(2000);
            return 0; // 不足以判断
        }

        //printf("pop_fpv_command 222\n");
        // 2. 检查STX
        ret = ring_buffer_v2_peek_at(rb, &sof, 0);
        if (ret == 0) {
            //printf("ring_buffer_v2_peek_at ERROR ret: %d\n", ret);
            return 0; // 不足以判断
        }

        if (sof != 0xFD) {
            //printf("Dirty data: SOF incorrect (0x%02X), dropping 1 byte.\n", sof);
            uint8_t discard;
            ring_buffer_v2_read(rb, &discard, 1); // 丢弃一个字节
            continue; // 重新开始循环
        }

        // 3. 读取载荷长度 (LEN 字段)
        ring_buffer_v2_peek_at(rb, &payload_len_from_header, 1);

        // K40T_Protocol_Frame_Header_t 中 STX 和 LEN 之后的固定头部长部分为:
        // dest_sys_id (1) + dest_comp_id (1) + seq (1) + src_sys_id (1) + src_comp_id (1) + msg_id (3) = 8字节
        // 完整帧长度 = STX(1) + LEN字段(1) + 固定头(8) + 载荷(payload_len_from_header) + CRC(2)
        //             = 1 + 1 + 8 + payload_len_from_header + 2 = 12 + payload_len_from_header
        total_frame_length = 12 + payload_len_from_header;
        // 4. 检查是否有足够的字节形成完整帧
        if (ring_buffer_v2_get_count(rb) < total_frame_length) {
            //printf("Frame data incomplete. Need %d, have %zu\n", total_frame_length, ring_buffer_v2_get_count(rb));
            return 0; // 数据不完整
        }

        // 5. 确保提供的缓冲区足够大
        if (buff_length < total_frame_length) {
            printf("Provided buffer too small. Need %d, have %d\n", total_frame_length, buff_length);
            // 这里应该考虑如何处理，是丢弃数据还是返回错误
            // 暂时丢弃以避免覆盖
            uint8_t temp_discard[total_frame_length];
            ring_buffer_v2_read(rb, temp_discard, total_frame_length);
            return 0; // 缓冲区不足
        }
        
        // 6. 读取完整帧数据
        ring_buffer_v2_read(rb, (uint8_t*)buffer, total_frame_length);

        // 7. CRC校验
        // 校验数据从0XFD之后开始，即从LEN字段开始，到Payload的末尾
        // CRC计算的数据指针: buffer + 1 (跳过STX)
        // CRC计算的数据长度: 1 (LEN字段) + 8 (固定头) + payload_len_from_header
        //                   = 9 + payload_len_from_header
        // 或者也可以是: total_frame_length - 1 (STX) - 2 (CRC) = total_frame_length - 3
        uint16_t calculated_crc = k_crc_calculate((uint8_t*)buffer + 1, total_frame_length - 3);
        
        uint16_t received_crc = *(uint16_t*)(buffer + total_frame_length - 2); // 小端假设

        if (calculated_crc != received_crc) {
            //printf("CRC error! Calculated: 0x%04X, Received: 0x%04X. Frame dropped.\n", calculated_crc, received_crc);
            //print_fuffer_in_Hex((unsigned char*)buffer, total_frame_length); // 打印错误帧以便调试
            return 0; // CRC校验失败
        }
        
        //printf("Frame OK. Total length: %d, Payload length: %d\n", total_frame_length, payload_len_from_header);
        return total_frame_length; // 返回读取的帧的总长度

    } while (1); // 如果SOF不对会continue，如果成功或数据不足会return
}



void dronepod_register_callback(uint32_t cmd_id, fpv_callback_t callback) {
    if (fpv_callback_count < MAX_CALLBACK_ENTRIES) {
        fpv_callback_entries[fpv_callback_count].cmd_id = cmd_id;
        fpv_callback_entries[fpv_callback_count].callback = callback;
        fpv_callback_count++;
        printf("register callback cmd_id: %d\n",  cmd_id);
    } else {
        // Handle error: too many callbacks registered
    }
}


void fpv_protocol_parse(const char *data, int length) {
    K40T_Protocol_Frame_Header_t *head = (K40T_Protocol_Frame_Header_t *)data;

    int msg_id = head->msg_id_low | (head->msg_id_mid << 8) | (head->msg_id_high << 16);
    int is_find_callback = 0;
    for (int i = 0; i < fpv_callback_count; i++) {
        if (fpv_callback_entries[i].cmd_id == msg_id) {
            fpv_callback_entries[i].callback(data);
            is_find_callback = 1;
            break;
        }
    }
    if (is_find_callback == 0)
    {
        printf("#1 cannot find message handler, cmd_id: %d\n", msg_id);
    }
    
}



void fc_register_callback(uint32_t cmd_id, fpv_callback_t callback) {
    if (fc_callback_count < MAX_CALLBACK_ENTRIES) {
        fc_callback_entries[fc_callback_count].cmd_id = cmd_id;
        fc_callback_entries[fc_callback_count].callback = callback;
        fc_callback_count++;
        printf("register callback cmd_id: %d\n",  cmd_id);
    } else {
        // Handle error: too many callbacks registered
    }
}


void fc_protocol_parse(const mavlink_message_t *msg) {
    //K40T_Protocol_Frame_Header_t *head = (K40T_Protocol_Frame_Header_t *)data;
    //int msg_id = head->msg_id_low | (head->msg_id_mid << 8) | (head->msg_id_high << 16);

    int is_find_callback = 0;
    for (int i = 0; i < fc_callback_count; i++) {
        if (fc_callback_entries[i].cmd_id == msg->msgid) {
            //printf("find message handler, cmd_id: %d\n", msg->msgid);
            fc_callback_entries[i].callback(msg);
            is_find_callback = 1;
            break;
        }
    }
    if (is_find_callback == 0)
    {
        //printf("cannot find message handler, cmd_id: %d\n", msg->msgid);
    }
    
}



void print_fuffer_in_Hex(unsigned char* buf, int len)
{
    for (int i = 0; i < len; i++)
    {
        printf("0x%02x ", buf[i]);
    }
    printf("\n");
}


void *fpv_message_receive_thread(void *arg) 
{
    pthread_setname_np(pthread_self(), "k40tmsgrecv");
    serial_context_t * p = (serial_context_t *)arg;
    uint8_t buffer[256];
    while (!p->stop) {
        memset(buffer, 0, sizeof(256));
        //usleep(10*1000);
        ssize_t len = serial_read(p->fd, buffer, 256);
        if (len > 0) {
            //lock
            //printf("---------recv len: %d --------\n", len);
            // print_fuffer_in_Hex(buffer,len);
            //ring_buffer_queue_arr(&p->m_ring_buffer, (const char *)buffer, len);
            ring_buffer_v2_write(p->k40rb, buffer, len);
            ring_buffer_v2_write(p->mavlinkrb, buffer, len);
        }
    }
    return NULL;
}


/// @brief send data to serial port and tcp client
/// @param buffer 
/// @param len 
/// @return 
int send_fpv_message(char *buffer, int len) 
{
    pthread_mutex_lock(&send_lock);
    int ret = -1;
    if (serial_context.fd > 0) {
        ret = serial_write(serial_context.fd, buffer, len);
        if (ret < 0)
        {
            return ret;
        }
    }
    pthread_mutex_unlock(&send_lock);
    ret = send_udp_message(buffer, len);
    return ret;
    //return send_tcp_message(buffer, len);
}



static void push_test_message_to_buffer(char *buffer, int len) 
{
    //printf("---------recv udp message len: %d --------\n", len);
    //print_fuffer_in_Hex(buffer,len);
    //ring_buffer_queue_arr(&serial_context.m_ring_buffer, (const char *)buffer, len);
    int ret = ring_buffer_v2_write(serial_context.k40rb, buffer, len);
    if (ret != len)
    {
        printf("ring_buffer_v2_write error\n");
    }
    ret = ring_buffer_v2_write(serial_context.mavlinkrb, buffer, len);
    if (ret != len)
    {
        printf("ring_buffer_v2_write error\n");
    }  
}



void *k40_message_process_thread(void *arg) 
{
    pthread_setname_np(pthread_self(), "k40msgproc");
    serial_context_t * p = (serial_context_t *)arg;
    char temp_buffer[BUFFER_SIZE];
    while (!p->stop) {
        //usleep(10*1000);
        int len = pop_fpv_command(p->k40rb, temp_buffer, BUFFER_SIZE);
        if (len > 0)    
        {
            //printf("---------pop message len: %d --------\n", len);
            //print_fuffer_in_Hex(temp_buffer,len);
            fpv_protocol_parse(temp_buffer, len);
        }
    }
    return NULL;
}


/**
 * @brief MAVLink协议处理线程
 * 
 * 使用MAVLink库函数处理MAVLink协议的数据
 * 
 * @param arg 线程参数
 * @return void* 
 */
void *mavlink_process_thread(void *arg)
{
    pthread_setname_np(pthread_self(), "mavlinkproc");
    serial_context_t *p = (serial_context_t *)arg;
    uint8_t c;
    mavlink_message_t msg;
    mavlink_status_t status;
    
    while (!p->stop) {
        //usleep(5*1000); 
        
        ring_buffer_v2_wait_for_data(p->mavlinkrb);

        ring_buffer_v2_read(p->mavlinkrb, &c, 1);
        
        // 使用MAVLink库函数解析字节
        if (mavlink_parse_char(MAVLINK_COMM_0, c, &msg, &status)) {
            // 成功解析到一条完整的MAVLink消息
            
            // 计算消息的总长度
            int msg_len = 0;
            if (status.flags & MAVLINK_STATUS_FLAG_IN_MAVLINK1) {
                // MAVLink 1.0
                msg_len = MAVLINK_NUM_NON_PAYLOAD_BYTES + msg.len;
            } else {
                // MAVLink 2.0
                msg_len = MAVLINK_NUM_NON_PAYLOAD_BYTES + msg.len;
                if (msg.incompat_flags & MAVLINK_IFLAG_SIGNED) {
                    msg_len += MAVLINK_SIGNATURE_BLOCK_LEN;
                }
            }

            // 处理解析出的MAVLink消息
            //handle_mavlink_message(&msg);
            //printf("---------pop mavlink message len: %d --------\n", msg_len);
            fc_protocol_parse(&msg);
        }
        
        
    }
    
    return NULL;
}


/**
 * @brief 处理MAVLink消息
 * 
 * @param msg 解析出的MAVLink消息
 */
void handle_mavlink_message(const mavlink_message_t *msg)
{
    // 根据消息ID分发到不同的处理函数
    switch (msg->msgid) {
        case MAVLINK_MSG_ID_HEARTBEAT:
            {
                // 解码心跳消息
                mavlink_heartbeat_t heartbeat;
                mavlink_msg_heartbeat_decode(msg, &heartbeat);
                
                LOG_I("收到MAVLink心跳消息: 系统类型=%d, 自动驾驶仪=%d, 系统模式=%d\n", 
                       heartbeat.type, heartbeat.autopilot, heartbeat.base_mode);
            }
            break;
            
            
        case MAVLINK_MSG_ID_GLOBAL_POSITION_INT:
            {
                // 解码全球位置消息
                mavlink_global_position_int_t pos;
                mavlink_msg_global_position_int_decode(msg, &pos);
                
                LOG_I("收到MAVLink位置消息: 纬度=%d, 经度=%d, 高度=%d mm, 相对高度=%d mm\n", 
                       pos.lat, pos.lon, pos.alt, pos.relative_alt);
            }
            break;

        case MAVLINK_MSG_ID_ATTITUDE:
            {
                // 解码姿态消息
                mavlink_attitude_t attitude;
                mavlink_msg_attitude_decode(msg, &attitude);
                
                LOG_I("收到MAVLink姿态消息: 横滚=%.2f, 俯仰=%.2f, 偏航=%.2f\n", 
                       attitude.roll, attitude.pitch, attitude.yaw);
                // 将弧度转换为度以便于阅读
                float roll_deg = attitude.roll * 180.0f / M_PI;
                float pitch_deg = attitude.pitch * 180.0f / M_PI;
                float yaw_deg = attitude.yaw * 180.0f / M_PI;
                
                LOG_I("收到MAVLink姿态消息:\n");
                LOG_I("  时间戳: %u ms\n", attitude.time_boot_ms);
                LOG_I("  横滚(Roll): %.2f° (%.4f rad)\n", roll_deg, attitude.roll);
                LOG_I("  俯仰(Pitch): %.2f° (%.4f rad)\n", pitch_deg, attitude.pitch);
                LOG_I("  偏航(Yaw): %.2f° (%.4f rad)\n", yaw_deg, attitude.yaw);
                LOG_I("  角速度: Roll=%.2f rad/s, Pitch=%.2f rad/s, Yaw=%.2f rad/s\n", 
                       attitude.rollspeed, attitude.pitchspeed, attitude.yawspeed);
                
                // 这里可以添加将姿态信息转发到其他系统的代码
                // 例如: forward_attitude_to_gimbal(&attitude);

            }
            break;
            
        case MAVLINK_MSG_ID_POSITION_TARGET_LOCAL_NED:
            {
                // 解码本地位置目标消息
                mavlink_position_target_local_ned_t pos_target;
                mavlink_msg_position_target_local_ned_decode(msg, &pos_target);
                
                // 创建一个字符串来表示坐标系
                const char* coordinate_frame;
                switch (pos_target.coordinate_frame) {
                    case MAV_FRAME_LOCAL_NED:
                        coordinate_frame = "LOCAL_NED";
                        break;
                    case MAV_FRAME_LOCAL_OFFSET_NED:
                        coordinate_frame = "LOCAL_OFFSET_NED";
                        break;
                    case MAV_FRAME_BODY_NED:
                        coordinate_frame = "BODY_NED";
                        break;
                    case MAV_FRAME_BODY_OFFSET_NED:
                        coordinate_frame = "BODY_OFFSET_NED";
                        break;
                    default:
                        coordinate_frame = "未知";
                        break;
                }
                
                // 创建一个字符串来表示类型掩码
                char type_mask_str[256] = {0};
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_X_IGNORE)
                    strcat(type_mask_str, "X_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_Y_IGNORE)
                    strcat(type_mask_str, "Y_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_Z_IGNORE)
                    strcat(type_mask_str, "Z_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_VX_IGNORE)
                    strcat(type_mask_str, "VX_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_VY_IGNORE)
                    strcat(type_mask_str, "VY_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_VZ_IGNORE)
                    strcat(type_mask_str, "VZ_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_AX_IGNORE)
                    strcat(type_mask_str, "AX_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_AY_IGNORE)
                    strcat(type_mask_str, "AY_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_AZ_IGNORE)
                    strcat(type_mask_str, "AZ_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_FORCE_SET)
                    strcat(type_mask_str, "FORCE_SET ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_YAW_IGNORE)
                    strcat(type_mask_str, "YAW_IGNORE ");
                if (pos_target.type_mask & POSITION_TARGET_TYPEMASK_YAW_RATE_IGNORE)
                    strcat(type_mask_str, "YAW_RATE_IGNORE ");
                
                LOG_I("收到MAVLink位置目标消息(LOCAL_NED):\n");
                LOG_I("  时间戳: %u ms\n", pos_target.time_boot_ms);
                LOG_I("  坐标系: %s\n", coordinate_frame);
                LOG_I("  类型掩码: 0x%X (%s)\n", pos_target.type_mask, type_mask_str);
                LOG_I("  位置(m): X=%.2f, Y=%.2f, Z=%.2f\n", 
                       pos_target.x, pos_target.y, pos_target.z);
                LOG_I("  速度(m/s): VX=%.2f, VY=%.2f, VZ=%.2f\n", 
                       pos_target.vx, pos_target.vy, pos_target.vz);
                LOG_I("  加速度(m/s²): AX=%.2f, AY=%.2f, AZ=%.2f\n", 
                       pos_target.afx, pos_target.afy, pos_target.afz);
                LOG_I("  偏航(rad): %.2f, 偏航速率(rad/s): %.2f\n", 
                       pos_target.yaw, pos_target.yaw_rate);
                
                // 这里可以添加将位置目标信息转发到其他系统的代码
                // 例如: forward_position_target_to_controller(&pos_target);
            }
            break;

        case MAVLINK_MSG_ID_ATTITUDE_TARGET:
            {
                // 解码姿态目标消息
                mavlink_attitude_target_t attitude_target;
                mavlink_msg_attitude_target_decode(msg, &attitude_target);
                
                LOG_I("收到MAVLink姿态目标消息:\n");
                LOG_I("  时间戳: %u ms\n", attitude_target.time_boot_ms);
                LOG_I("  类型掩码: 0x%X\n", attitude_target.type_mask);
                LOG_I("  四元数(w, x, y, z): %.4f, %.4f, %.4f, %.4f\n", 
                       attitude_target.q[0], attitude_target.q[1], attitude_target.q[2], attitude_target.q[3]);
                LOG_I("  角速度(rad/s): Roll=%.2f, Pitch=%.2f, Yaw=%.2f\n", 
                       attitude_target.body_roll_rate, attitude_target.body_pitch_rate, attitude_target.body_yaw_rate);
                LOG_I("  集体推力: %.2f\n", attitude_target.thrust);
                
                // 这里可以添加将姿态目标信息转发到其他系统的代码
                // 例如: forward_attitude_target_to_gimbal(&attitude_target);
            }
            break;
        
        case MAVLINK_MSG_ID_SCALED_IMU:
            {
                // 解码imu消息
                mavlink_scaled_imu_t imu;
                mavlink_msg_scaled_imu_decode(msg, &imu);
                
                printf("收到MAVLink imu消息:\n");
                printf("  时间戳: %u ms\n", imu.time_boot_ms);
                printf("  加速度(m/s²): X=%.2f, Y=%.2f, Z=%.2f\n", 
                       imu.xacc / 1000.0f, imu.yacc / 1000.0f, imu.zacc / 1000.0f);
                printf("  角速度(rad/s): X=%.2f, Y=%.2f, Z=%.2f\n", 
                       imu.xgyro / 1000.0f, imu.ygyro / 1000.0f, imu.zgyro / 1000.0f);
                printf("  磁力计(uT): X=%.2f, Y=%.2f, Z=%.2f\n", 
                       imu.xmag, imu.ymag, imu.zmag);
                printf("  温度(℃): %.2f\n", imu.temperature / 100.0f);
                
                // 这里可以添加将imu信息转发到其他系统的代码
                // 例如: forward_imu_to_controller(&imu);
            }
            break;
        // 添加更多消息处理...
        
        default:
            //printf("收到未处理的MAVLink消息ID: %d, 系统ID: %d, 组件ID: %d\n", 
            //       msg->msgid, msg->sysid, msg->compid);
            break;
    }
}




int start_fpv_message_proc(const char *device) 
{
    //ring buffer
    //ring_buffer_init(&serial_context.m_ring_buffer, m_buffer, sizeof(m_buffer));
    serial_context.k40rb = ring_buffer_v2_init(BUFFER_SIZE);
    if (!serial_context.k40rb) {
        fprintf(stderr, "Failed to initialize ring buffer.\n");
        return -1;
    }

    serial_context.mavlinkrb = ring_buffer_v2_init(BUFFER_SIZE);
    if (!serial_context.mavlinkrb) {
        fprintf(stderr, "Failed to initialize MAVLink ring buffer.\n");
        return -1;
    }

    //init 
    //serial_context.fd = serial_init("/dev/ttyAMA0", E_BaudRate_115200, E_DataSize_8, E_Parity_None, E_StopBit_1);
    serial_context.fd = serial_init(device, E_BaudRate_115200, E_DataSize_8, E_Parity_None, E_StopBit_1);
    if (serial_context.fd < 0) {
        perror("Failed to open serial port");
        return -1;
    }

    //recv process
    pthread_create(&receive_thread, NULL, fpv_message_receive_thread, &serial_context);
    
    //k40t process
    pthread_create(&process_thread, NULL, k40_message_process_thread, &serial_context);

    //mavlink process
    pthread_create(&mavlink_thread, NULL, mavlink_process_thread, &serial_context);

    //tcp server
    // tcp message >> ringbuffer
    net_start(push_test_message_to_buffer);

    return 0;
}



int stop_fpv_message_proc() 
{
    serial_context.stop = 1;
    serial_close(serial_context.fd);
    serial_context.fd = -1;

    pthread_join(receive_thread, NULL);
    pthread_join(process_thread, NULL);
    pthread_join(mavlink_process_thread, NULL);
    ring_buffer_v2_destroy(serial_context.k40rb);
    ring_buffer_v2_destroy(serial_context.mavlinkrb);
    printf("Ring buffer V2 destroyed.\n");
    return 0;
}

