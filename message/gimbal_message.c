
#include "gimbal_message.h"
#include "myserial.h"
#include "ring_buffer_v2.h"
#include "gimbal_protocol.h"


#define MAX_CALLBACK_ENTRIES 50
#define BUFFER_SIZE 8192

static char m_buffer[BUFFER_SIZE];
static gimbal_serial_context_t serial_context = {0};



typedef struct {
    uint32_t cmd_set;
    uint32_t cmd_id;
    fpv_callback_t callback;
} fpv_callback_entry_t;


static fpv_callback_entry_t callback_entries[MAX_CALLBACK_ENTRIES];
static size_t callback_count = 0;

//thread
pthread_t receive_thread, process_thread;


uint8_t crc16_high_table[] =
{
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
    0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
    0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
    0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
    0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
    0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
    0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
    0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
    0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
    0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
    0x80, 0x41, 0x00, 0xC1, 0x81, 0x40
};


/* CRC低位字节值表, 取消 const 的话, 这个表会存在ram里面, 执行速度会更快 */
uint8_t crc16_low_table[] =
{
    0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06,
    0x07, 0xC7, 0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD,
    0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09,
    0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9, 0x1B, 0xDB, 0xDA, 0x1A,
    0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC, 0x14, 0xD4,
    0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
    0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3,
    0xF2, 0x32, 0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4,
    0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
    0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29,
    0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF, 0x2D, 0xED,
    0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
    0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60,
    0x61, 0xA1, 0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67,
    0xA5, 0x65, 0x64, 0xA4, 0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F,
    0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68,
    0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA, 0xBE, 0x7E,
    0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
    0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71,
    0x70, 0xB0, 0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92,
    0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C,
    0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E, 0x5A, 0x9A, 0x9B, 0x5B,
    0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89, 0x4B, 0x8B,
    0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
    0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42,
    0x43, 0x83, 0x41, 0x81, 0x80, 0x40
};

/*********************************************************************************************
* 函数名称: calc_crc16
* 输入参数: uint8_t* 数据指针
            uint16_t 数据长度
* 返回参数: uint16_t 计算出来的crc16结果
* 功能描述: 计算指定个数据长度的crc16
* 修改记录: 
*********************************************************************************************/
uint16_t calc_crc16(uint8_t *pbuf, uint16_t len)
{
    uint8_t crc16_high = 0xff;    /* 高CRC字节初始化 */
    uint8_t crc16_low = 0xff;     /* 低CRC 字节初始化 */
    uint8_t index = 0;          /* CRC循环中的索引 */

    while(len--) /* 传输消息缓冲区 */
    {
        index = crc16_low ^ (*pbuf++); /* 计算CRC */
        crc16_low = crc16_high ^ crc16_high_table[index];
        crc16_high = crc16_low_table[index];
    }

    return (crc16_high << 8 | crc16_low);
}
static unsigned char calcCrc8(unsigned char *buffer, int len)
{
    unsigned char sum = 0;
    for (int i = 0; i < len; i++)
    {
        sum += buffer[i];
    }
    return sum;
}


uint16_t crc16_ccitt(uint8_t *data, size_t length) {
    uint16_t crc = 0xFFFF; // 初始值
    for (size_t i = 0; i < length; i++) {
        crc ^= (uint16_t)data[i] << 8; // 将当前字节与CRC寄存器的高8位进行异或
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x8000) { // 如果最高位为1
                crc = (crc << 1) ^ 0x1021; // 左移并异或多项式0x1021
            } else {
                crc <<= 1; // 左移
            }
        }
    }
    return crc;
}




static void print_fuffer_in_Hex(unsigned char* buf, int len)
{
    for (int i = 0; i < len; i++)
    {
        printf("0x%02x ", buf[i]);
    }
    printf("\n");
}



int pop_gimbal_command(ring_buffer_v2_t *m_ring_buffer, char *buffer, int buff_length)
{
    int drop_dirty_data = 0;
    char tmp_char;
    char sof;
    int command_length = 0;

    do
    {
        ring_buffer_v2_wait_for_data(m_ring_buffer);
        if (ring_buffer_v2_get_count(m_ring_buffer) < 13)
        {
            usleep(1000);
            return 0;
        }

        //sof 0xaa
        ring_buffer_v2_peek_at(m_ring_buffer, &sof, 0);
        if (sof != 0xAA )
        {   
            char buff[8] = {0};
            drop_dirty_data = 1;  
            //drop one bytes 
            ring_buffer_v2_read(m_ring_buffer, buff, 1);
            continue;
        }
        else
        {

            drop_dirty_data = 0;
            unsigned char  high_byte;
            unsigned char  low_byte;

            header_v1_t h = {0};
            int ret = ring_buffer_v2_peek_at(m_ring_buffer, &low_byte, 1);

            //printf("xxxxx tem1 : %d\n", low_byte);
            ring_buffer_v2_peek_at(m_ring_buffer, &high_byte, 2);
            uint16_t ver_len = (high_byte << 8) | low_byte;

             uint8_t ver = (ver_len >> 10) & 0x3F;  // 右移10位，取低6位
                uint16_t len = ver_len & 0x3FF;       
            
            command_length = len;
            //printf("pop_gimbal_command 444 command_length : %d\n", command_length);

            if (command_length < 13)
            {
                ret = ring_buffer_v2_read(m_ring_buffer, buffer, 3);
                continue;
            }
            
            if (ring_buffer_v2_get_count(m_ring_buffer) < command_length  )
            {
                //printf("read data length not enough \n");
                //length not enough
                return 0;
            }

            ret = ring_buffer_v2_read(m_ring_buffer, buffer, command_length);
            memcpy((void*)&h, buffer, sizeof(header_v1_t));
            uint16_t crc16 = calc_crc16(buffer, command_length-2);
            uint16_t *crc16_p = (uint16_t *)&buffer[command_length -2];

            if (*crc16_p != crc16){
                //printf("Error command  crc, should be :%X\n",crc16);
                return 0;
            }
            return ret;
    
        }
        

    } while (drop_dirty_data);
    
}




void gimbal_register_callback(uint32_t cmd_set, uint32_t cmd_id, fpv_callback_t callback) {
    if (callback_count < MAX_CALLBACK_ENTRIES) {
        callback_entries[callback_count].cmd_set = cmd_set;
        callback_entries[callback_count].cmd_id = cmd_id;
        callback_entries[callback_count].callback = callback;
        callback_count++;
        printf("register callback cmd_set: %d cmd_id: %d\n", cmd_set, cmd_id);
    } else {
        // Handle error: too many callbacks registered
    }
}


void gimbal_protocol_parse(const char *data, int length) {
    header_v1_struct *head = (header_v1_struct *)data;
    int is_find_callback = 0;
    for (int i = 0; i < callback_count; i++) {
        if (callback_entries[i].cmd_set == head->cmd_set && callback_entries[i].cmd_id == head->cmd_id) {
            callback_entries[i].callback(data);
            is_find_callback = 1;
            break;
        }
    }
    if (is_find_callback == 0)
    {
        //printf("cannot find message handler, cmd_set: %d cmd_id: %d\n", head->cmd_set, head->cmd_id);
    }
    
}



void *gimbal_message_receive_thread(void *arg) 
{
    pthread_setname_np(pthread_self(), "gimbalmsgrecv");
    gimbal_serial_context_t * p = (gimbal_serial_context_t *)arg;
    uint8_t buffer[256];
    while (!p->stop) {
        memset(buffer, 0, sizeof(256));
        ssize_t len = gimbal_serial_read(p->fd, buffer, 256);
        if (len > 0) {
            ring_buffer_v2_write(p->m_ring_buffer, buffer, len);
        }
    }
    return NULL;
}


/// @brief send data to serial port and tcp client
/// @param buffer 
/// @param len 
/// @return 
int send_gimbal_message(char *buffer, int len) 
{
    int ret = -1;
    if (serial_context.fd > 0) {
        ret = gimbal_serial_write(serial_context.fd, buffer, len);
        if (ret < 0)
        {
            return ret;
        }
    }

    return 0;
}



static void push_test_message_to_buffer(char *buffer, int len) 
{
    int ret = ring_buffer_v2_write(serial_context.m_ring_buffer, buffer, len);
    if (ret < 0)
    {
        printf("push message to buffer error\n");
        return;
    }
}



void *gimbal_message_process_thread(void *arg) 
{
    pthread_setname_np(pthread_self(), "gimbalmsgproc");
    gimbal_serial_context_t * p = (gimbal_serial_context_t *)arg;
    unsigned char temp_buffer[BUFFER_SIZE];
    while (!p->stop) {
        memset(temp_buffer, 0, BUFFER_SIZE);
        usleep(90000);
        int len = pop_gimbal_command(p->m_ring_buffer, temp_buffer, BUFFER_SIZE);
        if (len > 0)    
        {
            gimbal_protocol_parse(temp_buffer, len);
        }
    }
    return NULL;
}






int start_gimbal_message_proc(const char *device) 
{
    serial_context.m_ring_buffer = ring_buffer_v2_init(BUFFER_SIZE);
    serial_context.fd = gimbal_serial_init(device, SERIAL_BAUD_921600, SERIAL_DATA_8, SERIAL_PARITY_NONE, SERIAL_STOP_1);
    if (serial_context.fd < 0) {
        perror("Failed to open serial port");
        return -1;
    }

    //recv
    pthread_create(&receive_thread, NULL, gimbal_message_receive_thread, &serial_context);
    
    //process
    pthread_create(&process_thread, NULL, gimbal_message_process_thread, &serial_context);
    return 0;
}



int stop_gimbal_message_proc() 
{
    serial_context.stop = 1;
    gimbal_serial_close(serial_context.fd);
    serial_context.fd = -1;

    pthread_join(receive_thread, NULL);
    pthread_join(process_thread, NULL);
    return 0;
}
