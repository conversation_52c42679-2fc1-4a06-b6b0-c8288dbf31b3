{"editor.fontSize": 18, "files.associations": {"stdint.h": "c", "heq_rgn.h": "c", "thread": "cpp", "array": "c", "string": "c", "string_view": "c", "atomic": "cpp", "*.tcc": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "complex": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "cinttypes": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "fpv_message.h": "c", "zoom.h": "c", "configparser.h": "c", "*.cppbak": "cpp", "string.h": "c", "stddef.h": "c", "dronepod_k40t.h": "c", "tensor": "c", "unistd.h": "c", "dronepod_k40t_message.h": "c", "gimbal_message.h": "c", "report_message.h": "c", "dronepod_k40t_protocol.h": "c"}}