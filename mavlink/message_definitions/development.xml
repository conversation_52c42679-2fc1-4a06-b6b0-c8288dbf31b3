<?xml version="1.0"?>
<mavlink>
  <!-- XML file for prototyping definitions for common.xml -->
  <include>common.xml</include>
  <version>0</version>
  <dialect>0</dialect>
  <enums>
    <enum name="AIRSPEED_SENSOR_FLAGS" bitmask="true">
      <description>Airspeed sensor flags</description>
      <entry value="1" name="AIRSPEED_SENSOR_UNHEALTHY">
        <description>Airspeed sensor is unhealthy</description>
      </entry>
      <entry value="2" name="AIRSPEED_SENSOR_USING">
        <description>True if the data from this sensor is being actively used by the flight controller for guidance, navigation or control.</description>
      </entry>
    </enum>
    <enum name="MAV_BATTERY_STATUS_FLAGS" bitmask="true">
      <description>Battery status flags for fault, health and state indication.</description>
      <entry value="1" name="MAV_BATTERY_STATUS_FLAGS_NOT_READY_TO_USE">
        <description>
          The battery is not ready to use (fly).
          Set if the battery has faults or other conditions that make it unsafe to fly with.
          Note: It will be the logical OR of other status bits (chosen by the manufacturer/integrator).
        </description>
      </entry>
      <entry value="2" name="MAV_BATTERY_STATUS_FLAGS_CHARGING">
        <description>
          Battery is charging.
        </description>
      </entry>
      <entry value="4" name="MAV_BATTERY_STATUS_FLAGS_CELL_BALANCING">
        <description>
          Battery is cell balancing (during charging).
          Not ready to use (MAV_BATTERY_STATUS_FLAGS_NOT_READY_TO_USE may be set).
        </description>
      </entry>
      <entry value="8" name="MAV_BATTERY_STATUS_FLAGS_FAULT_CELL_IMBALANCE">
        <description>
          Battery cells are not balanced.
          Not ready to use.
        </description>
      </entry>
      <entry value="16" name="MAV_BATTERY_STATUS_FLAGS_AUTO_DISCHARGING">
        <description>
          Battery is auto discharging (towards storage level).
          Not ready to use (MAV_BATTERY_STATUS_FLAGS_NOT_READY_TO_USE would be set).
        </description>
      </entry>
      <entry value="32" name="MAV_BATTERY_STATUS_FLAGS_REQUIRES_SERVICE">
        <description>
          Battery requires service (not safe to fly).
          This is set at vendor discretion.
          It is likely to be set for most faults, and may also be set according to a maintenance schedule (such as age, or number of recharge cycles, etc.).
        </description>
      </entry>
      <entry value="64" name="MAV_BATTERY_STATUS_FLAGS_BAD_BATTERY">
        <description>
          Battery is faulty and cannot be repaired (not safe to fly).
          This is set at vendor discretion.
          The battery should be disposed of safely.
        </description>
      </entry>
      <entry value="128" name="MAV_BATTERY_STATUS_FLAGS_PROTECTIONS_ENABLED">
        <description>
          Automatic battery protection monitoring is enabled.
          When enabled, the system will monitor for certain kinds of faults, such as cells being over-voltage.
          If a fault is triggered then and protections are enabled then a safety fault (MAV_BATTERY_STATUS_FLAGS_FAULT_PROTECTION_SYSTEM) will be set and power from the battery will be stopped.
          Note that battery protection monitoring should only be enabled when the vehicle is landed. Once the vehicle is armed, or starts moving, the protections should be disabled to prevent false positives from disabling the output.
        </description>
      </entry>
      <entry value="256" name="MAV_BATTERY_STATUS_FLAGS_FAULT_PROTECTION_SYSTEM">
        <description>
          The battery fault protection system had detected a fault and cut all power from the battery.
          This will only trigger if MAV_BATTERY_STATUS_FLAGS_PROTECTIONS_ENABLED is set.
          Other faults like MAV_BATTERY_STATUS_FLAGS_FAULT_OVER_VOLT may also be set, indicating the cause of the protection fault.
        </description>
      </entry>
      <entry value="512" name="MAV_BATTERY_STATUS_FLAGS_FAULT_OVER_VOLT">
        <description>One or more cells are above their maximum voltage rating.</description>
      </entry>
      <entry value="1024" name="MAV_BATTERY_STATUS_FLAGS_FAULT_UNDER_VOLT">
        <description>
          One or more cells are below their minimum voltage rating.
          A battery that had deep-discharged might be irrepairably damaged, and set both MAV_BATTERY_STATUS_FLAGS_FAULT_UNDER_VOLT and MAV_BATTERY_STATUS_FLAGS_BAD_BATTERY.
        </description>
      </entry>
      <entry value="2048" name="MAV_BATTERY_STATUS_FLAGS_FAULT_OVER_TEMPERATURE">
        <description>Over-temperature fault.</description>
      </entry>
      <entry value="4096" name="MAV_BATTERY_STATUS_FLAGS_FAULT_UNDER_TEMPERATURE">
        <description>Under-temperature fault.</description>
      </entry>
      <entry value="8192" name="MAV_BATTERY_STATUS_FLAGS_FAULT_OVER_CURRENT">
        <description>Over-current fault.</description>
      </entry>
      <entry value="16384" name="MAV_BATTERY_STATUS_FLAGS_FAULT_SHORT_CIRCUIT">
        <description>
          Short circuit event detected.
          The battery may or may not be safe to use (check other flags).
        </description>
      </entry>
      <entry value="32768" name="MAV_BATTERY_STATUS_FLAGS_FAULT_INCOMPATIBLE_VOLTAGE">
        <description>Voltage not compatible with power rail voltage (batteries on same power rail should have similar voltage).</description>
      </entry>
      <entry value="65536" name="MAV_BATTERY_STATUS_FLAGS_FAULT_INCOMPATIBLE_FIRMWARE">
        <description>Battery firmware is not compatible with current autopilot firmware.</description>
      </entry>
      <entry value="131072" name="MAV_BATTERY_STATUS_FLAGS_FAULT_INCOMPATIBLE_CELLS_CONFIGURATION">
        <description>Battery is not compatible due to cell configuration (e.g. 5s1p when vehicle requires 6s).</description>
      </entry>
      <entry value="262144" name="MAV_BATTERY_STATUS_FLAGS_CAPACITY_RELATIVE_TO_FULL">
        <description>
          Battery capacity_consumed and capacity_remaining values are relative to a full battery (they sum to the total capacity of the battery).
          This flag would be set for a smart battery that can accurately determine its remaining charge across vehicle reboots and discharge/recharge cycles.
          If unset the capacity_consumed indicates the consumption since vehicle power-on, as measured using a power monitor. The capacity_remaining, if provided, indicates the estimated remaining capacity on the assumption that the battery was full on vehicle boot.
          If unset a GCS is recommended to advise that users fully charge the battery on power on.
        </description>
      </entry>
      <entry value="2147483648" name="MAV_BATTERY_STATUS_FLAGS_EXTENDED">
        <description>Reserved (not used). If set, this will indicate that an additional status field exists for higher status values.</description>
      </entry>
    </enum>
    <!-- The MAV_CMD enum entries describe either: -->
    <!--  * the data payload of mission items (as used in the MISSION_ITEM_INT message) -->
    <!--  * the data payload of mavlink commands (as used in the COMMAND_INT and COMMAND_LONG messages) -->
    <!-- ALL the entries in the MAV_CMD enum have a maximum of 7 parameters -->
    <enum name="MAV_CMD">
      <description>Commands to be executed by the MAV. They can be executed on user request, or as part of a mission script. If the action is used in a mission, the parameter mapping to the waypoint/mission message is as follows: Param 1, Param 2, Param 3, Param 4, X: Param 5, Y:Param 6, Z:Param 7. This command list is similar what ARINC 424 is for commercial aircraft: A data format how to interpret waypoint/mission data. NaN and INT32_MAX may be used in float/integer params (respectively) to indicate optional/default values (e.g. to use the component's current yaw or latitude rather than a specific value). See https://mavlink.io/en/guide/xml_schema.html#MAV_CMD for information about the structure of the MAV_CMD entries</description>
      <entry value="35" name="MAV_CMD_DO_FIGURE_EIGHT" hasLocation="true" isDestination="true">
        <wip/>
        <!-- This message is work-in-progress and it can therefore change. It should NOT be used in stable production environments. -->
        <description>Fly a figure eight path as defined by the parameters.
          Set parameters to NaN/INT32_MAX (as appropriate) to use system-default values.
          The command is intended for fixed wing vehicles (and VTOL hybrids flying in fixed-wing mode), allowing POI tracking for gimbals that don't support infinite rotation.
          This command only defines the flight path. Speed should be set independently (use e.g. MAV_CMD_DO_CHANGE_SPEED).
          Yaw and other degrees of freedom are not specified, and will be flight-stack specific (on vehicles where they can be controlled independent of the heading).
        </description>
        <param index="1" label="Major Radius" units="m">Major axis radius of the figure eight. Positive: orbit the north circle clockwise. Negative: orbit the north circle counter-clockwise.
        NaN: The radius will be set to 2.5 times the minor radius and direction is clockwise.
        Must be greater or equal to two times the minor radius for feasible values.</param>
        <param index="2" label="Minor Radius" units="m">Minor axis radius of the figure eight. Defines the radius of the two circles that make up the figure. Negative value has no effect.
        NaN: The radius will be set to the default loiter radius.</param>
        <param index="3" reserved="true" default="NaN"/>
        <param index="4" label="Orientation" units="rad">Orientation of the figure eight major axis with respect to true north (range: [-pi,pi]). NaN: use default orientation aligned to true north.</param>
        <param index="5" label="Latitude/X">Center point latitude/X coordinate according to MAV_FRAME. If no MAV_FRAME specified, MAV_FRAME_GLOBAL is assumed.
        INT32_MAX or NaN: Use current vehicle position, or current center if already loitering.</param>
        <param index="6" label="Longitude/Y">Center point longitude/Y coordinate according to MAV_FRAME. If no MAV_FRAME specified, MAV_FRAME_GLOBAL is assumed.
        INT32_MAX or NaN: Use current vehicle position, or current center if already loitering.</param>
        <param index="7" label="Altitude/Z">Center point altitude MSL/Z coordinate according to MAV_FRAME. If no MAV_FRAME specified, MAV_FRAME_GLOBAL is assumed.
        INT32_MAX or NaN: Use current vehicle altitude.</param>
      </entry>
      <entry value="247" name="MAV_CMD_DO_UPGRADE" hasLocation="false" isDestination="false">
        <description>Request a target system to start an upgrade of one (or all) of its components.
          For example, the command might be sent to a companion computer to cause it to upgrade a connected flight controller.
          The system doing the upgrade will report progress using the normal command protocol sequence for a long running operation.
          Command protocol information: https://mavlink.io/en/services/command.html.</description>
        <param index="1" label="Component ID" enum="MAV_COMPONENT">Component id of the component to be upgraded. If set to 0, all components should be upgraded.</param>
        <param index="2" label="Reboot" minValue="0" maxValue="1" increment="1">0: Do not reboot component after the action is executed, 1: Reboot component after the action is executed.</param>
        <param index="3">Reserved</param>
        <param index="4">Reserved</param>
        <param index="5">Reserved</param>
        <param index="6">Reserved</param>
        <param index="7">WIP: upgrade progress report rate (can be used for more granular control).</param>
      </entry>
      <entry value="550" name="MAV_CMD_SET_AT_S_PARAM" hasLocation="false" isDestination="false">
        <description>Allows setting an AT S command of an SiK radio.
        </description>
        <param index="1" label="Radio instance">The radio instance, one-based, 0 for all.</param>
        <param index="2" label="Index">The Sx index, e.g. 3 for S3 which is NETID.</param>
        <param index="3" label="Value">The value to set it to, e.g. default 25 for NETID</param>
        <param index="4" reserved="true"/>
        <param index="5" reserved="true"/>
        <param index="6" reserved="true"/>
        <param index="7" reserved="true"/>
      </entry>
      <entry value="610" name="MAV_CMD_DO_SET_SYS_CMP_ID" hasLocation="false" isDestination="false">
        <description>
          Set system and component id.
          This allows moving of a system and all its components to a new system id, or moving a particular component to a new system/component id.
          Recipients must reject command addressed to broadcast system ID.
        </description>
        <param index="1" label="System ID" minValue="1" maxValue="255" increment="1">New system ID for target component(s). 0: ignore and reject command (broadcast system ID not allowed).</param>
        <param index="2" label="Component ID" minValue="0" maxValue="255" increment="1">New component ID for target component(s). 0: ignore (component IDs don't change).</param>
        <param index="3" label="Reboot">Reboot components after ID change. Any non-zero value triggers the reboot.</param>
        <param index="4" reserved="true" default="NaN"/>
      </entry>
      <entry value="611" name="MAV_CMD_DO_SET_GLOBAL_ORIGIN" hasLocation="true" isDestination="false">
        <description>Sets the GNSS coordinates of the vehicle local origin (0,0,0) position.
          Vehicle should emit GPS_GLOBAL_ORIGIN irrespective of whether the origin is changed.
          This enables transform between the local coordinate frame and the global (GNSS) coordinate frame, which may be necessary when (for example) indoor and outdoor settings are connected and the MAV should move from in- to outdoor.
          This command supersedes SET_GPS_GLOBAL_ORIGIN.
          Should be sent in a COMMAND_INT (Expected frame is MAV_FRAME_GLOBAL, and this should be assumed when sent in COMMAND_LONG).
        </description>
        <param index="1">Empty</param>
        <param index="2">Empty</param>
        <param index="3">Empty</param>
        <param index="4">Empty</param>
        <param index="5" label="Latitude">Latitude</param>
        <param index="6" label="Longitude">Longitude</param>
        <param index="7" label="Altitude" units="m">Altitude</param>
      </entry>
      <entry value="12900" name="MAV_CMD_ODID_SET_EMERGENCY" hasLocation="false" isDestination="false">
        <description>Used to manually set/unset emergency status for remote id.
          This is for compliance with MOC ASTM docs, specifically F358 section 7.7: "Emergency Status Indicator".
          The requirement can also be satisfied by automatic setting of the emergency status by flight stack, and that approach is preferred.
          See https://mavlink.io/en/services/opendroneid.html for more information.
	</description>
        <param index="1" label="Number" minValue="0" increment="1">Set/unset emergency 0: unset, 1: set</param>
        <param index="2" reserved="true" default="NaN"/>
        <param index="3" reserved="true" default="NaN"/>
        <param index="4">Empty</param>
        <param index="5">Empty</param>
        <param index="5">Empty</param>
        <param index="6">Empty</param>
        <param index="7">Empty</param>
      </entry>
      <entry value="43004" name="MAV_CMD_EXTERNAL_WIND_ESTIMATE" hasLocation="false" isDestination="false">
        <description>Set an external estimate of wind direction and speed.
          This might be used to provide an initial wind estimate to the estimator (EKF) in the case where the vehicle is wind dead-reckoning, extending the time when operating without GPS before before position drift builds to an unsafe level. For this use case the command might reasonably be sent every few minutes when operating at altitude, and the value is cleared if the estimator resets itself.
        </description>
        <param index="1" label="Wind speed" units="m/s" minValue="0">Horizontal wind speed.</param>
        <param index="2" label="Wind speed accuracy" units="m/s">Estimated 1 sigma accuracy of wind speed. Set to NaN if unknown.</param>
        <param index="3" label="Direction" units="deg" minValue="0" maxValue="360">Azimuth (relative to true north) from where the wind is blowing.</param>
        <param index="4" label="Direction accuracy" units="deg">Estimated 1 sigma accuracy of wind direction. Set to NaN if unknown.</param>
        <param index="5">Empty</param>
        <param index="6">Empty</param>
        <param index="7">Empty</param>
      </entry>
      <entry value="32100" name="MAV_CMD_REQUEST_OPERATOR_CONTROL" hasLocation="false" isDestination="false">
        <description>Request GCS control of a system (or of a specific component in a system).

          A controlled system should only accept MAVLink commands and command-like messages that are sent by its controlling GCS, or from other components with the same system id.
          Commands from other systems should be rejected with MAV_RESULT_FAILED (except for this command, which may be acknowledged with MAV_RESULT_ACCEPTED if control is granted).
          Command-like messages should be ignored (or rejected if that is supported by their associated protocol).

          GCS control of the whole system is managed via a single component that we will refer to here as the "system manager component".
          This component streams the CONTROL_STATUS message and sets the GCS_CONTROL_STATUS_FLAGS_SYSTEM_MANAGER flag.
          Other components in the system should monitor for the CONTROL_STATUS message with this flag, and set their controlling GCS to match its published system id.
          A GCS that wants to control the system should also monitor for the same message and flag, and address the MAV_CMD_REQUEST_OPERATOR_CONTROL to its component id.
          Note that integrators are required to ensure that there is only one system manager component in the system (i.e. one component emitting the message with GCS_CONTROL_STATUS_FLAGS_SYSTEM_MANAGER set).

          The MAV_CMD_REQUEST_OPERATOR_CONTROL command is sent by a GCS to the system manager component to request or release control of a system, specifying whether subsequent takeover requests from another GCS are automatically granted, or require permission.

          The system manager component should grant control to the GCS if the system does not require takeover permission (or is uncontrolled) and ACK the request with MAV_RESULT_ACCEPTED.
          The system manager component should then stream CONTROL_STATUS indicating its controlling system: all other components with the same system id should monitor this message and set their own controlling GCS to match that of the system manager component.

          If the system manager component cannot grant control (because takeover requires permission), the request should be rejected with MAV_RESULT_FAILED.
          The system manager component should then send this same command to the current owning GCS in order to notify of the request.
          The owning GCS would ACK with MAV_RESULT_ACCEPTED, and might choose to release control of the vehicle, or re-request control with the takeover bit set to allow permission.
          In case it choses to re-request control with takeover bit set to allow permission, requestor GCS will only have 10 seconds to get control, otherwise owning GCS will re-request control with takeover bit set to disallow permission, and requestor GCS will need repeat the request if still interested in getting control.
          Note that the pilots of both GCS should co-ordinate safe handover offline.

          Note that in most systems the only controlled component will be the "system manager component", and that will be the autopilot.
          However separate GCS control of a particular component is also permitted, if supported by the component.
          In this case the GCS will address MAV_CMD_REQUEST_OPERATOR_CONTROL to the specific component it wants to control.
          The component will then stream CONTROL_STATUS for its controlling GCS (it must not set GCS_CONTROL_STATUS_FLAGS_SYSTEM_MANAGER).
          The component should fall back to the system GCS (if any) when it is not directly controlled, and may stop emitting CONTROL_STATUS.
          The flow is otherwise the same as for requesting control over the whole system.
        </description>
        <param index="1" label="Sysid requesting control">System ID of GCS requesting control. 0 when command sent from GCS to autopilot (autopilot determines requesting GCS sysid from message header). Sysid of GCS requesting control when command sent by autopilot to controlling GCS.</param>
        <param index="2" label="Action">0: Release control, 1: Request control.</param>
        <param index="3" label="Allow takeover">Enable automatic granting of ownership on request (by default reject request and notify current owner). 0: Ask current owner and reject request, 1: Allow automatic takeover.</param>
        <param index="4" label="Request timeout" units="s" minValue="3" maxValue="60">Timeout in seconds before a request to a GCS to allow takeover is assumed to be rejected. This is used to display the timeout graphically on requestor and GCS in control.</param>
        <param index="5">Empty</param>
        <param index="6">Empty</param>
        <param index="7">Empty</param>
      </entry>
    </enum>
    <enum name="GCS_CONTROL_STATUS_FLAGS" bitmask="true">
      <description>CONTROL_STATUS flags.</description>
      <entry value="1" name="GCS_CONTROL_STATUS_FLAGS_SYSTEM_MANAGER">
        <description>If set, this CONTROL_STATUS publishes the controlling GCS for the whole system. If unset, the CONTROL_STATUS indicates the controlling GCS for just the component emitting the message. Note that to request control of the system a GCS should send MAV_CMD_REQUEST_OPERATOR_CONTROL to the component emitting CONTROL_STATUS with this flag set.</description>
      </entry>
      <entry value="2" name="GCS_CONTROL_STATUS_FLAGS_TAKEOVER_ALLOWED">
        <description>Takeover allowed (requests for control will be granted). If not set requests for control will be rejected, but the controlling GCS will be notified (and may release control or allow takeover).</description>
      </entry>
    </enum>
    <enum name="TARGET_ABSOLUTE_SENSOR_CAPABILITY_FLAGS" bitmask="true">
      <description>These flags indicate the sensor reporting capabilities for TARGET_ABSOLUTE.</description>
      <entry value="1" name="TARGET_ABSOLUTE_SENSOR_CAPABILITY_POSITION"/>
      <entry value="2" name="TARGET_ABSOLUTE_SENSOR_CAPABILITY_VELOCITY"/>
      <entry value="4" name="TARGET_ABSOLUTE_SENSOR_CAPABILITY_ACCELERATION"/>
      <entry value="8" name="TARGET_ABSOLUTE_SENSOR_CAPABILITY_ATTITUDE"/>
      <entry value="16" name="TARGET_ABSOLUTE_SENSOR_CAPABILITY_RATES"/>
    </enum>
    <enum name="TARGET_OBS_FRAME">
      <description>The frame of a target observation from an onboard sensor.</description>
      <entry value="0" name="TARGET_OBS_FRAME_LOCAL_NED">
        <description>NED local tangent frame (x: North, y: East, z: Down) with origin fixed relative to earth.</description>
      </entry>
      <entry value="1" name="TARGET_OBS_FRAME_BODY_FRD">
        <description>FRD local frame aligned to the vehicle's attitude (x: Forward, y: Right, z: Down) with an origin that travels with vehicle.</description>
      </entry>
      <entry value="2" name="TARGET_OBS_FRAME_LOCAL_OFFSET_NED">
        <description>NED local tangent frame (x: North, y: East, z: Down) with an origin that travels with vehicle.</description>
      </entry>
      <entry value="3" name="TARGET_OBS_FRAME_OTHER">
        <description>Other sensor frame for target observations neither in local NED nor in body FRD.</description>
      </entry>
    </enum>
    <enum name="RADIO_RC_CHANNELS_FLAGS" bitmask="true">
      <description>RADIO_RC_CHANNELS flags (bitmask).</description>
      <entry value="1" name="RADIO_RC_CHANNELS_FLAGS_FAILSAFE">
        <description>Failsafe is active. The content of the RC channels data in the RADIO_RC_CHANNELS message is implementation dependent.</description>
      </entry>
      <entry value="2" name="RADIO_RC_CHANNELS_FLAGS_OUTDATED">
        <description>Channel data may be out of date. This is set when the receiver is unable to validate incoming data from the transmitter and has therefore resent the last valid data it received.</description>
      </entry>
    </enum>
    <enum name="GPS_SYSTEM_ERROR_FLAGS" bitmask="true">
      <description>Flags indicating errors in a GPS receiver.</description>
      <entry value="1" name="GPS_SYSTEM_ERROR_INCOMING_CORRECTIONS">
        <description>There are problems with incoming correction streams.</description>
      </entry>
      <entry value="2" name="GPS_SYSTEM_ERROR_CONFIGURATION">
        <description>There are problems with the configuration.</description>
      </entry>
      <entry value="4" name="GPS_SYSTEM_ERROR_SOFTWARE">
        <description>There are problems with the software on the GPS receiver.</description>
      </entry>
      <entry value="8" name="GPS_SYSTEM_ERROR_ANTENNA">
        <description>There are problems with an antenna connected to the GPS receiver.</description>
      </entry>
      <entry value="16" name="GPS_SYSTEM_ERROR_EVENT_CONGESTION">
        <description>There are problems handling all incoming events.</description>
      </entry>
      <entry value="32" name="GPS_SYSTEM_ERROR_CPU_OVERLOAD">
        <description>The GPS receiver CPU is overloaded.</description>
      </entry>
      <entry value="64" name="GPS_SYSTEM_ERROR_OUTPUT_CONGESTION">
        <description>The GPS receiver is experiencing output congestion.</description>
      </entry>
    </enum>
    <enum name="GPS_AUTHENTICATION_STATE">
      <description>Signal authentication state in a GPS receiver.</description>
      <entry value="0" name="GPS_AUTHENTICATION_STATE_UNKNOWN">
        <description>The GPS receiver does not provide GPS signal authentication info.</description>
      </entry>
      <entry value="1" name="GPS_AUTHENTICATION_STATE_INITIALIZING">
        <description>The GPS receiver is initializing signal authentication.</description>
      </entry>
      <entry value="2" name="GPS_AUTHENTICATION_STATE_ERROR">
        <description>The GPS receiver encountered an error while initializing signal authentication.</description>
      </entry>
      <entry value="3" name="GPS_AUTHENTICATION_STATE_OK">
        <description>The GPS receiver has correctly authenticated all signals.</description>
      </entry>
      <entry value="4" name="GPS_AUTHENTICATION_STATE_DISABLED">
        <description>GPS signal authentication is disabled on the receiver.</description>
      </entry>
    </enum>
    <enum name="GPS_JAMMING_STATE">
      <description>Signal jamming state in a GPS receiver.</description>
      <entry value="0" name="GPS_JAMMING_STATE_UNKNOWN">
        <description>The GPS receiver does not provide GPS signal jamming info.</description>
      </entry>
      <entry value="1" name="GPS_JAMMING_STATE_OK">
        <description>The GPS receiver detected no signal jamming.</description>
      </entry>
      <entry value="2" name="GPS_JAMMING_STATE_MITIGATED">
        <description>The GPS receiver detected and mitigated signal jamming.</description>
      </entry>
      <entry value="3" name="GPS_JAMMING_STATE_DETECTED">
        <description>The GPS receiver detected signal jamming.</description>
      </entry>
    </enum>
    <enum name="GPS_SPOOFING_STATE">
      <description>Signal spoofing state in a GPS receiver.</description>
      <entry value="0" name="GPS_SPOOFING_STATE_UNKNOWN">
        <description>The GPS receiver does not provide GPS signal spoofing info.</description>
      </entry>
      <entry value="1" name="GPS_SPOOFING_STATE_OK">
        <description>The GPS receiver detected no signal spoofing.</description>
      </entry>
      <entry value="2" name="GPS_SPOOFING_STATE_MITIGATED">
        <description>The GPS receiver detected and mitigated signal spoofing.</description>
      </entry>
      <entry value="3" name="GPS_SPOOFING_STATE_DETECTED">
        <description>The GPS receiver detected signal spoofing but still has a fix.</description>
      </entry>
    </enum>
    <enum name="GPS_RAIM_STATE">
      <description>State of RAIM processing.</description>
      <entry value="0" name="GPS_RAIM_STATE_UNKNOWN">
        <description>RAIM capability is unknown.</description>
      </entry>
      <entry value="1" name="GPS_RAIM_STATE_DISABLED">
        <description>RAIM is disabled.</description>
      </entry>
      <entry value="2" name="GPS_RAIM_STATE_OK">
        <description>RAIM integrity check was successful.</description>
      </entry>
      <entry value="3" name="GPS_RAIM_STATE_FAILED">
        <description>RAIM integrity check failed.</description>
      </entry>
    </enum>
  </enums>
  <messages>
    <message id="295" name="AIRSPEED">
      <description>Airspeed information from a sensor.</description>
      <field type="uint8_t" name="id" instance="true">Sensor ID.</field>
      <field type="float" name="airspeed" units="m/s">Calibrated airspeed (CAS).</field>
      <field type="int16_t" name="temperature" units="cdegC">Temperature. INT16_MAX for value unknown/not supplied.</field>
      <field type="float" name="raw_press" units="hPa">Raw differential pressure. NaN for value unknown/not supplied.</field>
      <field type="uint8_t" name="flags" enum="AIRSPEED_SENSOR_FLAGS">Airspeed sensor flags.</field>
    </message>
    <message id="354" name="SET_VELOCITY_LIMITS">
      <wip/>
      <!-- This message is work-in-progress and it can therefore change. It should NOT be used in stable production environments. -->
      <description>Set temporary maximum limits for horizontal speed, vertical speed and yaw rate.
        The consumer must stream the current limits in VELOCITY_LIMITS at 1 Hz or more (when limits are being set).
        The consumer should latch the limits until a new limit is received or the mode is changed.
      </description>
      <field type="uint8_t" name="target_system">System ID (0 for broadcast).</field>
      <field type="uint8_t" name="target_component">Component ID (0 for broadcast).</field>
      <field type="float" name="horizontal_speed_limit" default="NaN" units="m/s">Limit for horizontal movement in MAV_FRAME_LOCAL_NED. NaN: Field not used (ignore)</field>
      <field type="float" name="vertical_speed_limit" default="NaN" units="m/s">Limit for vertical movement in MAV_FRAME_LOCAL_NED. NaN: Field not used (ignore)</field>
      <field type="float" name="yaw_rate_limit" default="NaN" units="rad/s">Limit for vehicle turn rate around its yaw axis. NaN: Field not used (ignore)</field>
    </message>
    <message id="355" name="VELOCITY_LIMITS">
      <wip/>
      <!-- This message is work-in-progress and it can therefore change. It should NOT be used in stable production environments. -->
      <description>Current limits for horizontal speed, vertical speed and yaw rate, as set by SET_VELOCITY_LIMITS.</description>
      <field type="float" name="horizontal_speed_limit" default="NaN" units="m/s">Limit for horizontal movement in MAV_FRAME_LOCAL_NED. NaN: No limit applied</field>
      <field type="float" name="vertical_speed_limit" default="NaN" units="m/s">Limit for vertical movement in MAV_FRAME_LOCAL_NED. NaN: No limit applied</field>
      <field type="float" name="yaw_rate_limit" default="NaN" units="rad/s">Limit for vehicle turn rate around its yaw axis. NaN: No limit applied</field>
    </message>
    <message id="361" name="FIGURE_EIGHT_EXECUTION_STATUS">
      <wip/>
      <!-- This message is work-in-progress it can therefore change, and should NOT be used in stable production environments -->
      <description>
        Vehicle status report that is sent out while figure eight execution is in progress (see MAV_CMD_DO_FIGURE_EIGHT).
        This may typically send at low rates: of the order of 2Hz.
      </description>
      <field type="uint64_t" name="time_usec" units="us">Timestamp (UNIX Epoch time or time since system boot). The receiving end can infer timestamp format (since 1.1.1970 or since system boot) by checking for the magnitude of the number.</field>
      <field type="float" name="major_radius" units="m">Major axis radius of the figure eight. Positive: orbit the north circle clockwise. Negative: orbit the north circle counter-clockwise.</field>
      <field type="float" name="minor_radius" units="m">Minor axis radius of the figure eight. Defines the radius of two circles that make up the figure.</field>
      <field type="float" name="orientation" units="rad">Orientation of the figure eight major axis with respect to true north in [-pi,pi).</field>
      <field type="uint8_t" name="frame" enum="MAV_FRAME">The coordinate system of the fields: x, y, z.</field>
      <field type="int32_t" name="x">X coordinate of center point. Coordinate system depends on frame field.</field>
      <field type="int32_t" name="y">Y coordinate of center point. Coordinate system depends on frame field.</field>
      <field type="float" name="z" units="m">Altitude of center point. Coordinate system depends on frame field.</field>
    </message>
    <message id="369" name="BATTERY_STATUS_V2">
      <description>Battery dynamic information.
        This should be streamed (nominally at 1Hz).
        Static/invariant battery information is sent in BATTERY_INFO.
        Note that smart batteries should set the MAV_BATTERY_STATUS_FLAGS_CAPACITY_RELATIVE_TO_FULL bit to indicate that supplied capacity values are relative to a battery that is known to be full.
        Power monitors would not set this bit, indicating that capacity_consumed is relative to drone power-on, and that other values are estimated based on the assumption that the battery was full on power-on.
      </description>
      <field type="uint8_t" name="id" instance="true">Battery ID</field>
      <field type="int16_t" name="temperature" units="cdegC" invalid="INT16_MAX">Temperature of the whole battery pack (not internal electronics). INT16_MAX field not provided.</field>
      <field type="float" name="voltage" units="V" invalid="NaN">Battery voltage (total). NaN: field not provided.</field>
      <field type="float" name="current" units="A" invalid="NaN">Battery current (through all cells/loads). Positive value when discharging and negative if charging. NaN: field not provided.</field>
      <field type="float" name="capacity_consumed" units="Ah" invalid="NaN">Consumed charge. NaN: field not provided. This is either the consumption since power-on or since the battery was full, depending on the value of MAV_BATTERY_STATUS_FLAGS_CAPACITY_RELATIVE_TO_FULL.</field>
      <field type="float" name="capacity_remaining" units="Ah" invalid="NaN">Remaining charge (until empty). NaN: field not provided. Note: If MAV_BATTERY_STATUS_FLAGS_CAPACITY_RELATIVE_TO_FULL is unset, this value is based on the assumption the battery was full when the system was powered.</field>
      <field type="uint8_t" name="percent_remaining" units="%" invalid="UINT8_MAX">Remaining battery energy. Values: [0-100], UINT8_MAX: field not provided.</field>
      <field type="uint32_t" name="status_flags" enum="MAV_BATTERY_STATUS_FLAGS">Fault, health, readiness, and other status indications.</field>
    </message>
    <message id="414" name="GROUP_START">
      <description>Emitted during mission execution when control reaches MAV_CMD_GROUP_START.</description>
      <field type="uint32_t" name="group_id">Mission-unique group id (from MAV_CMD_GROUP_START).</field>
      <field type="uint32_t" name="mission_checksum">CRC32 checksum of current plan for MAV_MISSION_TYPE_ALL. As defined in MISSION_CHECKSUM message.</field>
      <field type="uint64_t" name="time_usec" units="us">Timestamp (UNIX Epoch time or time since system boot).
        The receiving end can infer timestamp format (since 1.1.1970 or since system boot) by checking for the magnitude of the number.</field>
    </message>
    <message id="415" name="GROUP_END">
      <description>Emitted during mission execution when control reaches MAV_CMD_GROUP_END.</description>
      <field type="uint32_t" name="group_id">Mission-unique group id (from MAV_CMD_GROUP_END).</field>
      <field type="uint32_t" name="mission_checksum">CRC32 checksum of current plan for MAV_MISSION_TYPE_ALL. As defined in MISSION_CHECKSUM message.</field>
      <field type="uint64_t" name="time_usec" units="us">Timestamp (UNIX Epoch time or time since system boot).
        The receiving end can infer timestamp format (since 1.1.1970 or since system boot) by checking for the magnitude of the number.</field>
    </message>
    <message id="420" name="RADIO_RC_CHANNELS">
      <description>RC channel outputs from a MAVLink RC receiver for input to a flight controller or other components (allows an RC receiver to connect via MAVLink instead of some other protocol such as PPM-Sum or S.BUS).
        Note that this is not intended to be an over-the-air format, and does not replace RC_CHANNELS and similar messages reported by the flight controller.
        The target_system field should normally be set to the system id of the system to control, typically the flight controller.
        The target_component field can normally be set to 0, so that all components of the system can receive the message.
        The channels array field can publish up to 32 channels; the number of channel items used in the array is specified in the count field.
        The time_last_update_ms field contains the timestamp of the last received valid channels data in the receiver's time domain.
        The count field indicates the first index of the channel array that is not used for channel data (this and later indexes are zero-filled).
        The RADIO_RC_CHANNELS_FLAGS_OUTDATED flag is set by the receiver if the channels data is not up-to-date (for example, if new data from the transmitter could not be validated so the last valid data is resent).
        The RADIO_RC_CHANNELS_FLAGS_FAILSAFE failsafe flag is set by the receiver if the receiver's failsafe condition is met (implementation dependent, e.g., connection to the RC radio is lost).
        In this case time_last_update_ms still contains the timestamp of the last valid channels data, but the content of the channels data is not defined by the protocol (it is up to the implementation of the receiver).
        For instance, the channels data could contain failsafe values configured in the receiver; the default is to carry the last valid data.
        Note: The RC channels fields are extensions to ensure that they are located at the end of the serialized payload and subject to MAVLink's trailing-zero trimming.
      </description>
      <field type="uint8_t" name="target_system">System ID (ID of target system, normally flight controller).</field>
      <field type="uint8_t" name="target_component">Component ID (normally 0 for broadcast).</field>
      <field type="uint32_t" name="time_last_update_ms" units="ms">Time when the data in the channels field were last updated (time since boot in the receiver's time domain).</field>
      <field type="uint16_t" name="flags" enum="RADIO_RC_CHANNELS_FLAGS">Radio RC channels status flags.</field>
      <field type="uint8_t" name="count">Total number of RC channels being received. This can be larger than 32, indicating that more channels are available but not given in this message.</field>
      <extensions/>
      <field type="int16_t[32]" name="channels" minValue="-4096" maxValue="4096">RC channels.
        Channel values are in centered 13 bit format. Range is -4096 to 4096, center is 0. Conversion to PWM is x * 5/32 + 1500.
        Channels with indexes equal or above count should be set to 0, to benefit from MAVLink's trailing-zero trimming.</field>
    </message>
    <message id="441" name="GNSS_INTEGRITY">
      <description>Information about key components of GNSS receivers, like signal authentication, interference and system errors.</description>
      <field type="uint8_t" name="id" instance="true">GNSS receiver id. Must match instance ids of other messages from same receiver.</field>
      <field type="uint32_t" name="system_errors" enum="GPS_SYSTEM_ERROR_FLAGS">Errors in the GPS system.</field>
      <field type="uint8_t" name="authentication_state" enum="GPS_AUTHENTICATION_STATE">Signal authentication state of the GPS system.</field>
      <field type="uint8_t" name="jamming_state" enum="GPS_JAMMING_STATE">Signal jamming state of the GPS system.</field>
      <field type="uint8_t" name="spoofing_state" enum="GPS_SPOOFING_STATE">Signal spoofing state of the GPS system.</field>
      <field type="uint8_t" name="raim_state" enum="GPS_RAIM_STATE">The state of the RAIM processing.</field>
      <field type="uint16_t" name="raim_hfom" units="cm" invalid="UINT16_MAX">Horizontal expected accuracy using satellites successfully validated using RAIM.</field>
      <field type="uint16_t" name="raim_vfom" units="cm" invalid="UINT16_MAX">Vertical expected accuracy using satellites successfully validated using RAIM.</field>
      <field type="uint8_t" name="corrections_quality" minValue="0" maxValue="10" invalid="UINT8_MAX">An abstract value representing the estimated quality of incoming corrections, or 255 if not available.</field>
      <field type="uint8_t" name="system_status_summary" minValue="0" maxValue="10" invalid="UINT8_MAX">An abstract value representing the overall status of the receiver, or 255 if not available.</field>
      <field type="uint8_t" name="gnss_signal_quality" minValue="0" maxValue="10" invalid="UINT8_MAX">An abstract value representing the quality of incoming GNSS signals, or 255 if not available.</field>
      <field type="uint8_t" name="post_processing_quality" minValue="0" maxValue="10" invalid="UINT8_MAX">An abstract value representing the estimated PPK quality, or 255 if not available.</field>
    </message>
    <!-- Target info from a sensor on the target -->
    <message id="510" name="TARGET_ABSOLUTE">
      <description>Current motion information from sensors on a target</description>
      <field type="uint64_t" name="timestamp" units="us">Timestamp (UNIX epoch time).</field>
      <field type="uint8_t" name="id">The ID of the target if multiple targets are present</field>
      <field type="uint8_t" name="sensor_capabilities" enum="TARGET_ABSOLUTE_SENSOR_CAPABILITY_FLAGS">Bitmap to indicate the sensor's reporting capabilities</field>
      <field type="int32_t" name="lat" units="degE7">Target's latitude (WGS84)</field>
      <field type="int32_t" name="lon" units="degE7">Target's longitude (WGS84)</field>
      <field type="float" name="alt" units="m">Target's altitude (AMSL)</field>
      <field type="float[3]" name="vel" units="m/s" invalid="[0]">Target's velocity in its body frame</field>
      <field type="float[3]" name="acc" units="m/s/s" invalid="[0]">Linear target's acceleration in its body frame</field>
      <field type="float[4]" name="q_target" invalid="[0]">Quaternion of the target's orientation from its body frame to the vehicle's NED frame.</field>
      <field type="float[3]" name="rates" units="rad/s" invalid="[0]">Target's roll, pitch and yaw rates</field>
      <field type="float[2]" name="position_std" units="m">Standard deviation of horizontal (eph) and vertical (epv) position errors</field>
      <field type="float[3]" name="vel_std" units="m/s">Standard deviation of the target's velocity in its body frame</field>
      <field type="float[3]" name="acc_std" units="m/s/s">Standard deviation of the target's acceleration in its body frame</field>
    </message>
    <!-- Target info measured by MAV's onboard sensors -->
    <message id="511" name="TARGET_RELATIVE">
      <description>The location of a target measured by MAV's onboard sensors. </description>
      <field type="uint64_t" name="timestamp" units="us">Timestamp (UNIX epoch time)</field>
      <field type="uint8_t" name="id" instance="true">The ID of the target if multiple targets are present</field>
      <field type="uint8_t" name="frame" enum="TARGET_OBS_FRAME">Coordinate frame used for following fields.</field>
      <field type="float" name="x" units="m">X Position of the target in TARGET_OBS_FRAME</field>
      <field type="float" name="y" units="m">Y Position of the target in TARGET_OBS_FRAME</field>
      <field type="float" name="z" units="m">Z Position of the target in TARGET_OBS_FRAME</field>
      <field type="float[3]" name="pos_std" units="m">Standard deviation of the target's position in TARGET_OBS_FRAME</field>
      <field type="float" name="yaw_std" units="rad">Standard deviation of the target's orientation in TARGET_OBS_FRAME</field>
      <field type="float[4]" name="q_target">Quaternion of the target's orientation from the target's frame to the TARGET_OBS_FRAME (w, x, y, z order, zero-rotation is 1, 0, 0, 0)</field>
      <field type="float[4]" name="q_sensor">Quaternion of the sensor's orientation from TARGET_OBS_FRAME to vehicle-carried NED. (Ignored if set to (0,0,0,0)) (w, x, y, z order, zero-rotation is 1, 0, 0, 0)</field>
      <field type="uint8_t" name="type" enum="LANDING_TARGET_TYPE">Type of target</field>
    </message>
    <message id="512" name="CONTROL_STATUS">
      <description>Information about GCS in control of this MAV. This should be broadcast at low rate (nominally 1 Hz) and emitted when ownership or takeover status change. Control over MAV is requested using MAV_CMD_REQUEST_OPERATOR_CONTROL.</description>
      <field type="uint8_t" name="sysid_in_control">System ID of GCS MAVLink component in control (0: no GCS in control).</field>
      <field type="uint8_t" name="flags" enum="GCS_CONTROL_STATUS_FLAGS">Control status. For example, whether takeover is allowed, and whether this message instance defines the default controlling GCS for the whole system.</field>
    </message>
  </messages>
</mavlink>
