/**
 * @file    uvc_camera.c
 * @brief
 * @details
 * <AUTHOR> Software Group
 * @date    2022-11-11
 * @version v1.00
 * @Copyright (c) 2021 Shanghai Visinex Technologies Co., Ltd. All rights reserved.
 *
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <errno.h>
#include <inttypes.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <sys/mman.h>

#include "uvc_player.h"
#include "uvc_framebuf.h"
#include "uvc_camera.h"
#include "uvc_util.h"
#define ENABLE_TIME_COST_PRINT
#include "internal_defs.h"

static vs_int32_t uvc_camera_base_info_get(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t ret;

    memset(&p_camera->vcap, 0, sizeof(p_camera->vcap));
    ret = ioctl(p_camera->fd, VIDIOC_QUERYCAP, &p_camera->vcap);
	if (ret >= 0) {
        printf("driver:\t\t%s\n", p_camera->vcap.driver);
     	printf("card:\t\t%s\n", p_camera->vcap.card);
     	printf("bus_info:\t%s\n", p_camera->vcap.bus_info);
     	printf("version:\t%d\n", p_camera->vcap.version);
     	printf("capabilities:\t0x%x\n", p_camera->vcap.capabilities);

        if (strcmp(p_camera->vcap.card, "C505e HD Webcam") == 0)
        {
#ifdef C505E_FORCE_640
            printf("C505e HD Webcam Found! will set fps to 25\n");
            p_camera->fps = 30;
            p_camera->width = 640;
            p_camera->height = 360; 
#endif
        }

     	if ((p_camera->vcap.capabilities & V4L2_CAP_VIDEO_CAPTURE) == V4L2_CAP_VIDEO_CAPTURE) {
			printf("Device %s: supports capture.\n", p_camera->dev_path);
		} else {
			printf("UVC: Device %s: not supports campture\n", p_camera->dev_path);
			return VS_FAILED;
		}

		if ((p_camera->vcap.capabilities & V4L2_CAP_STREAMING) == V4L2_CAP_STREAMING) {
			printf("Device %s: supports streaming.\n", p_camera->dev_path);
		} else  {
			printf("UVC: Device %s: not supports streaming\n", p_camera->dev_path);
			return VS_FAILED;
        }
	} else {
        VS_UVC_LOG_E("UVC: Device (%s)(%d): VIDIOC_QUERYCAP error, ret[0x%x]\n", p_camera->dev_path, p_camera->fd, ret);
        return VS_FAILED;
    }

    return VS_SUCCESS;
}

static vs_void_t uvc_camera_v4l2_format_printf(struct v4l2_format fmt)
{
    printf("v4l2_format struct:\n");
    printf("type: %d\n", fmt.type);
    printf("width: %d\n", fmt.fmt.pix.width);
    printf("height: %d\n", fmt.fmt.pix.height);
    printf("pixelformat: %s\n", vs_uvc_v4l2_pixelformat_str_get(fmt.fmt.pix.pixelformat));
    printf("field: %d\n", fmt.fmt.pix.field);
    printf("bytesperline: %d\n", fmt.fmt.pix.bytesperline);
    printf("sizeimage: %d\n", fmt.fmt.pix.sizeimage);
    printf("colorspace: %d\n", fmt.fmt.pix.colorspace);
    printf("priv: %d\n", fmt.fmt.pix.priv);
    printf("raw_date: %s\n", fmt.fmt.raw_data);
}

static vs_int32_t uvc_camera_frmsize_check(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t i = 0, j= 0;
    VS_UVC_LOG_D("uvc_camera_frmsize_check start\n");

    for (i = 0; i < p_camera->p_capability->vfmtdesc_num; i++) {
        //printf("pixelformat expect %u cur is %u\n", p_camera->pixelformat, p_camera->p_capability->vfmtdesc[i].pixelformat);
        if (p_camera->pixelformat == p_camera->p_capability->vfmtdesc[i].pixelformat) {
            for (j = 0; j < p_camera->p_capability->vfrmsize_num; j++) {
                //printf("expect width[%u] height[%u] cur is width[%u] height[%u]\n", p_camera->width, p_camera->height,
                //        p_camera->p_capability->vfrmsize[i][j].discrete.width, p_camera->p_capability->vfrmsize[i][j].discrete.height);
                if (p_camera->width == p_camera->p_capability->vfrmsize[i][j].discrete.width
                    && p_camera->height == p_camera->p_capability->vfrmsize[i][j].discrete.height) {
                    VS_UVC_LOG_D("uvc_camera_frmsize_check success!\n");
                    return VS_SUCCESS;
                }
            }
        }
    }

    VS_UVC_LOG_E("uvc not support width(%u) x height(%u) pixelformat(%s)\n",
            p_camera->width, p_camera->height, vs_uvc_v4l2_pixelformat_str_get(p_camera->pixelformat));

    return VS_SUCCESS;
}

static vs_int32_t uvc_camera_format_set(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t ret = VS_SUCCESS;
    struct v4l2_format format;

    VS_UVC_LOG_D("uvc_camera_format_set start\n");

    memset(&format, 0, sizeof(format));
	format.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
	format.fmt.pix.width = p_camera->width;
	format.fmt.pix.height = p_camera->height;
	format.fmt.pix.pixelformat = p_camera->pixelformat;//V4L2_PIX_FMT_YUYV;
	format.fmt.pix.field = V4L2_FIELD_NONE;//V4L2_FIELD_ANY;//V4L2_FIELD_NONE
    format.fmt.pix.priv = V4L2_PIX_FMT_PRIV_MAGIC;
    //set cur fmt
    ret = ioctl(p_camera->fd, VIDIOC_S_FMT, &format);
    if (ret < 0) {
        VS_UVC_LOG_E("UVC: VIDIOC_S_FMT failed: %s (%d) %u %u %s .\n", strerror(errno), errno,
                    p_camera->width, p_camera->height, vs_uvc_v4l2_pixelformat_str_get(p_camera->pixelformat));
        return VS_FAILED;
    }

    memset(&p_camera->cur_vfmt, 0, sizeof(p_camera->cur_vfmt));
    p_camera->cur_vfmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    //get cur fmt
    ret = ioctl(p_camera->fd, VIDIOC_G_FMT, &p_camera->cur_vfmt);
    if (ret < 0) {
        VS_UVC_LOG_E("VIDIOC_G_FMT failed (0x%x)\n", ret);
        return VS_FAILED;
    } else if(p_camera->cur_vfmt.fmt.pix.width == p_camera->width && p_camera->cur_vfmt.fmt.pix.height == p_camera->height
                && p_camera->cur_vfmt.fmt.pix.pixelformat == p_camera->pixelformat){
        printf("set format success <%d * %d> [%s]\n", p_camera->cur_vfmt.fmt.pix.width,
                p_camera->cur_vfmt.fmt.pix.height, vs_uvc_v4l2_pixelformat_str_get(p_camera->pixelformat));
    }
    else
    {
        printf("set format failed, plan set <%d * %d> [%s], now format is <%d * %d> [%s]\n",
                p_camera->width, p_camera->height, vs_uvc_v4l2_pixelformat_str_get(p_camera->pixelformat),
                p_camera->cur_vfmt.fmt.pix.width, p_camera->cur_vfmt.fmt.pix.height,
                vs_uvc_v4l2_pixelformat_str_get(p_camera->pixelformat));
        return VS_FAILED;
    }

    VS_UVC_LOG_D("uvc_camera_format_set end\n");
    return VS_SUCCESS;
}

static vs_int32_t uvc_camera_fps_set(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t ret = VS_SUCCESS;
    struct v4l2_streamparm streamparm;

    VS_UVC_LOG_D("uvc_camera_streamparm_set start\n");

    //get fps
    memset(&streamparm, 0, sizeof(streamparm));
    streamparm.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    ret = ioctl(p_camera->fd, VIDIOC_G_PARM, &streamparm);
    if (ret < 0) {
        VS_UVC_LOG_E("UVC: VIDIOC_G_PARM failed: %s (%d).\n", strerror(errno), errno);
        return VS_FAILED;
    }
    VS_UVC_LOG_I("cur frame rate %u:%u, plan set to fps(%u)\n",streamparm.parm.capture.timeperframe.numerator,
                streamparm.parm.capture.timeperframe.denominator, p_camera->fps);

    //set fps
    streamparm.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    streamparm.parm.capture.timeperframe.numerator = 1;
    streamparm.parm.capture.timeperframe.denominator = p_camera->fps;
    ret = ioctl(p_camera->fd, VIDIOC_S_PARM, &streamparm);
    if (ret < 0) {
        VS_UVC_LOG_E("UVC: VIDIOC_S_PARM failed: %s (%d) %u:%u.\n", \
                strerror(errno), errno, \
                streamparm.parm.capture.timeperframe.numerator, \
                streamparm.parm.capture.timeperframe.denominator);
        return VS_FAILED;
    }

    ret = ioctl(p_camera->fd, VIDIOC_G_PARM, &streamparm);
    if (ret < 0) {
        VS_UVC_LOG_E("UVC: VIDIOC_G_PARM failed: %s (%d).\n", strerror(errno), errno);
        return VS_FAILED;
    }
    printf("set frame rate[%u], result is %u:%u\n", p_camera->fps, streamparm.parm.capture.timeperframe.numerator,
                streamparm.parm.capture.timeperframe.denominator);

    VS_UVC_LOG_D("uvc_camera_streamparm_set end\n");
    return VS_SUCCESS;
}

vs_int32_t uvc_camera_open(vs_uvc_camera_info_s *p_camera)
{
    p_camera->fd = open((char *)p_camera->dev_path, O_RDWR | O_NONBLOCK);
	if (p_camera->fd < 0) {
        VS_UVC_LOG_E("UVC: open [%s] error\n", p_camera->dev_path);
		return VS_FAILED;
	}

	return VS_SUCCESS;
}

vs_int32_t uvc_camera_reopen(vs_uvc_camera_info_s *p_camera)
{
    int index = 0;
    for (index = 0; index < 40; index++)
    {
        sprintf((char *)p_camera->dev_path, "%s%d", "/dev/video", index);
        if (access((char *)p_camera->dev_path, F_OK) != 0) {
            printf("illegal param [%s], can not find the usb camera device \n", optarg);
        }
        else
        {
            printf("found UVC dev path:%s\n", p_camera->dev_path);
            break;
        }
    }
    
    printf("index:%d\n", index);
    if (index >= 4)
    {
        printf("no UVC camera founded!\n");
        return VS_FAILED;
    }

	return uvc_camera_open(p_camera);
}

vs_int32_t uvc_camera_frmivalenum_get(vs_uvc_camera_info_s *p_camera, enum v4l2_buf_type type,
                                vs_uint32_t pixelformat, vs_uint32_t width, vs_uint32_t height, struct v4l2_frmivalenum *p_vfrmival)
{
    vs_int32_t i = 0;
    struct v4l2_frmivalenum *p_frmival = VS_NULL;

    for (i = 0; i < UVC_V4L2_FRMIVAL_MAX; i++) {
        p_frmival = &p_vfrmival[i];
        p_frmival->index = i;
        p_frmival->pixel_format = pixelformat;
        p_frmival->width = width;
        p_frmival->height = height;
        p_frmival->type = V4L2_BUF_TYPE_VIDEO_CAPTURE;

        if (ioctl(p_camera->fd, VIDIOC_ENUM_FRAMEINTERVALS, p_frmival) >= 0) {
            if (p_frmival->type == V4L2_FRMIVAL_TYPE_DISCRETE) {
                printf("\t\t INTERVALS DISCRETE %ux%u\n",
                                  p_frmival->discrete.numerator,
                                  p_frmival->discrete.denominator);
            } else if (p_frmival->type == V4L2_FRMIVAL_TYPE_STEPWISE) {
                printf("\t\t INTERVALS STEPWISE min %ux%u max %ux%u step %ux%u\n",
                                  p_frmival->stepwise.min.numerator,
                                  p_frmival->stepwise.min.denominator,
                                  p_frmival->stepwise.max.numerator,
                                  p_frmival->stepwise.max.denominator,
                                  p_frmival->stepwise.step.numerator,
                                  p_frmival->stepwise.step.denominator);
            } else {
                printf("\t\t p_frmival->type V4L2_FRMIVAL_TYPE_CONTINUOUS \n");
            }
        }  else {
            p_camera->p_capability->vfrmival_num = i;
            break;
        }
    }

    return VS_SUCCESS;
}

vs_int32_t uvc_camera_capability_get(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t i = 0, j = 0;
    enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    struct v4l2_fmtdesc *p_fmtdesc;
    struct v4l2_frmsizeenum *p_frmsize;

    for (i = 0; i < UVC_V4L2_FMX_MAX; i++) {
        p_fmtdesc = &p_camera->p_capability->vfmtdesc[i];
        p_fmtdesc->index = i;
        p_fmtdesc->type = type;
        if (ioctl(p_camera->fd, VIDIOC_ENUM_FMT, p_fmtdesc) >= 0) {
            printf("%s ------------------------------------------------\n",
                        vs_uvc_v4l2_pixelformat_str_get(p_fmtdesc->pixelformat));
            for (j = 0; j < UVC_V4L2_FRMSIZE_MAX; j++) {

                p_frmsize = &p_camera->p_capability->vfrmsize[i][j];
                p_frmsize->pixel_format = p_fmtdesc->pixelformat;
                p_frmsize->index = j;
                if (ioctl(p_camera->fd, VIDIOC_ENUM_FRAMESIZES, p_frmsize) >= 0) {
                    if (p_frmsize->type == V4L2_FRMSIZE_TYPE_DISCRETE) {
                        printf("\t FRMSIZE DISCRETE %ux%u\n",
                                          p_frmsize->discrete.width,
                                          p_frmsize->discrete.height);
                        uvc_camera_frmivalenum_get(p_camera, type, p_fmtdesc->pixelformat, p_frmsize->discrete.width,
                                          p_frmsize->discrete.height, p_camera->p_capability->vfrmival[i][j]);
                    } else if (p_frmsize->type == V4L2_FRMSIZE_TYPE_STEPWISE) {
                        printf("\t FRMSIZE STEPWISE %ux%u\n",
                                          p_frmsize->stepwise.max_width,
                                          p_frmsize->stepwise.max_height);
                        uvc_camera_frmivalenum_get(p_camera, type, p_fmtdesc->pixelformat, p_frmsize->stepwise.max_width,
                                          p_frmsize->stepwise.max_height, p_camera->p_capability->vfrmival[i][j]);
                    }
                } else {
                    p_camera->p_capability->vfrmsize_num = j;
                    break;
                }
            }
        }else {
            p_camera->p_capability->vfmtdesc_num = i;
            break;
        }
    }
    return VS_SUCCESS;
}

vs_int32_t uvc_camera_init(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t ret;

    p_camera->p_capability = (vs_uvc_capability_info_s *)malloc(sizeof(vs_uvc_capability_info_s));
    if (p_camera->p_capability == VS_NULL) {
        printf("malloc vs_uvc_capability_info_s failled, sizeof(%lu) \n", sizeof(vs_uvc_capability_info_s));
        return VS_FAILED;
    }
    memset(p_camera->p_capability, 0, sizeof(vs_uvc_capability_info_s));

    ret = uvc_camera_base_info_get(p_camera);
    if (ret != VS_SUCCESS) {
        return ret;
    }

    ret = uvc_camera_capability_get(p_camera);
    if (ret != VS_SUCCESS) {
        return ret;
    }

    ret = uvc_camera_frmsize_check(p_camera);
    if (ret != VS_SUCCESS) {
        return ret;
    }

    //set width & height & pix
    ret = uvc_camera_format_set(p_camera);
    if (ret != VS_SUCCESS) {
#ifdef GZ_384_HANDLING
       if (p_camera->cur_vfmt.fmt.pix.width == 352 && p_camera->cur_vfmt.fmt.pix.height == 288)
       {
           p_camera->width = 640;
           p_camera->height = 480;
           printf("will try to set width = 640 & height = 480\n");
           ret = uvc_camera_format_set(p_camera);
           if (ret != VS_SUCCESS) 
           {
               return ret;
           }
       }
#else
        return ret;
#endif
    }

    //set fps
    ret = uvc_camera_fps_set(p_camera);
    if (ret != VS_SUCCESS) {
        return ret;
    }

    ret = uvc_framebuf_init(p_camera);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("UVC: uvc_framebuf_init failed:(0x%x).\n", ret);
        return ret;
    }

    
    ret = uvc_player_create(p_camera);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("UVC: uvc_player_init failed:(0x%x).\n", ret);
        return ret;
    }

    
	return VS_SUCCESS;
}

vs_int32_t uvc_camera_start(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t ret;
    enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE;

	ret = ioctl(p_camera->fd, VIDIOC_STREAMON, &type);
    if (ret < 0) {
        VS_UVC_LOG_E("UVC: VIDIOC_QBUF failed: %s (%d).\n", strerror(errno), errno);
        return ret;
    }

	return ret;
}

vs_void_t * uvc_camera_display(vs_void_t *param)
{
    pthread_setname_np(pthread_self(), "uvccamera");
	fd_set read_fds;
    
    vs_int32_t ret;
    
    struct timeval timeout;
    static int g_index_uvc = 0;
    static int g_uvc_pre_deinit = 0;
    
    vs_uvc_camera_info_s *p_camera = (vs_uvc_camera_info_s *)param;
    
    while (!p_camera->is_stop_uvc_camera) {
        if (g_index_uvc > 10)
        {
           printf("\n\n-----------------UVC_REOPEN!------------------\n\n");
            
            if (g_uvc_pre_deinit == 0)
            {
                uvc_camera_close(p_camera);
                uvc_framebuf_deinit(p_camera);

                g_uvc_pre_deinit = 1;
            }
            ret = uvc_camera_reopen(p_camera);
            if (ret != VS_SUCCESS) {
                printf("uvc_camera_reopen ret[0x%x]\n", ret);
                usleep(100 * 1000);
                continue;
            }
            g_index_uvc = 0;
            usleep(100 * 1000);

            p_camera->buf_count = 4;
            p_camera->mem_type = V4L2_MEMORY_MMAP;
            //uvc_framebuf_init(p_camera);
            uvc_camera_init(p_camera);
            uvc_camera_start(p_camera);
        }

        timeout.tv_sec = 0;
        timeout.tv_usec = 100 * 1000;
        FD_ZERO(&read_fds);
        FD_SET(p_camera->fd, &read_fds);
    	ret = select(p_camera->fd + 1, &read_fds, NULL, NULL, &timeout);
    	if (ret < 0) {
    		perror("uvc_camera_frame select");
    		//return ret;
    		usleep(100 * 1000);
    		continue;
    	} else if (ret == 0) {
            airvisen_trace("uvc_camera_frame timeout sec[%ld] usec[%ld]\n", timeout.tv_sec, timeout.tv_usec);
    		//return ret;
    		usleep(100 * 1000);
    		continue;
    	}
        
        ret = uvc_framebuf_dqbuf(p_camera);
        if (ret != VS_SUCCESS) {
            printf("uvc_framebuf_acquire ret[0x%x]\n", ret);

            if (g_uvc_pre_deinit == 0)
            {
                g_index_uvc ++;
            }
            usleep(100 * 1000);
    		continue;
        }

        ret = uvc_player_framesend(p_camera);
        if (ret != VS_SUCCESS) {
            printf("uvc_player_framesend ret[0x%x]\n", ret);
        }

        ret = uvc_framebuf_qbuf(p_camera);
        if (ret != VS_SUCCESS) {
            printf("uvc_framebuf_release ret[0x%x]\n", ret);
            usleep(100 * 1000);
    		continue;
        }


        g_index_uvc = 0;
        g_uvc_pre_deinit = 0;
    }

	return VS_NULL;
}

vs_int32_t uvc_camera_stop(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t ret;
	enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE;

	ret = ioctl(p_camera->fd, VIDIOC_STREAMOFF, &type);
    if (ret < 0) {
        VS_UVC_LOG_E("UVC: VIDIOC_STREAMOFF failed: %s (%d).\n", strerror(errno), errno);
        return ret;
    }

	return ret;
}

vs_void_t uvc_camera_deinit(vs_uvc_camera_info_s *p_camera)
{
    vs_int32_t ret;

    ret = uvc_player_destroy(p_camera);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("UVC: uvc_player_deinit failed:(0x%x).\n", ret);
    }

    usleep(100 * 1000);
    ret = uvc_framebuf_qbuf(p_camera);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("UVC: uvc_framebuf_qbuf failed:(0x%x).\n", ret);
    }

    ret = uvc_framebuf_deinit(p_camera);
    if (ret != VS_SUCCESS) {
        VS_UVC_LOG_E("UVC: uvc_framebuf_deinit failed:(0x%x).\n", ret);
    }

//	free(p_camera->head.start);
//	p_camera->head.length = 0;
//	p_camera->head.start = NULL;

	return ;
}

vs_void_t uvc_camera_close(vs_uvc_camera_info_s *p_camera)
{
	close(p_camera->fd);
}


